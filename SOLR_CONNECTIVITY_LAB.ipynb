{"cells": [{"cell_type": "markdown", "id": "title-section", "metadata": {}, "source": ["# Solr Connectivity and Query Construction Lab\n", "\n", "This notebook is dedicated to testing and validating Solr connectivity and query construction for the Job Offer Translation Pipeline. We'll explore various aspects of Solr interaction to ensure robustness in the main pipeline."]}, {"cell_type": "code", "execution_count": null, "id": "imports-section", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import logging\n", "import os\n", "import pysolr\n", "import requests\n", "import time\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from urllib.parse import quote_plus, urlparse, parse_qs\n", "from tqdm.notebook import tqdm\n", "from datetime import datetime\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('solr_lab')"]}, {"cell_type": "code", "execution_count": null, "id": "config-section", "metadata": {}, "outputs": [], "source": ["# Solr Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "LOCAL_SOLR_URL = \"http://localhost:8983/solr/\"\n", "\n", "# Fields we're interested in for job offers\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "# Countries to test with\n", "TEST_COUNTRIES = ['algerie']\n", "\n", "# Test parameters\n", "MAX_TEST_DOCS = 1000  # Maximum number of documents to retrieve for testing"]}, {"cell_type": "markdown", "id": "basic-connectivity-section", "metadata": {}, "source": ["## 1. Basic Connectivity Tests\n", "\n", "First, let's test basic connectivity to the Solr instance and verify we can access the cores we need."]}, {"cell_type": "code", "execution_count": null, "id": "solr-info-function", "metadata": {}, "outputs": [], "source": ["def get_solr_info(base_url=SOLR_BASE_URL, timeout=30):\n", "    \"\"\"\n", "    Get basic information about the Solr instance.\n", "    \n", "    Args:\n", "        base_url (str): Base URL of the Solr instance\n", "        \n", "    Returns:\n", "        dict: Information about the Solr instance\n", "    \"\"\"\n", "    try:\n", "        # Get Solr admin info\n", "        admin_url = f\"{base_url}admin/info/system\"\n", "        response = requests.get(admin_url, params={'wt': 'json'})\n", "        \n", "        if response.status_code != 200:\n", "            logger.error(f\"Failed to get Solr info. Status code: {response.status_code}\")\n", "            return None\n", "        \n", "        return response.json()\n", "    except Exception as e:\n", "        logger.error(f\"Error getting Solr info: {e}\")\n", "        return None\n", "\n", "def list_solr_cores(base_url=SOLR_BASE_URL):\n", "    \"\"\"\n", "    List all cores in the Solr instance.\n", "    \n", "    Args:\n", "        base_url (str): Base URL of the Solr instance\n", "        \n", "    Returns:\n", "        list: List of core names\n", "    \"\"\"\n", "    try:\n", "        # Get list of cores\n", "        cores_url = f\"{base_url}admin/cores\"\n", "        response = requests.get(cores_url, params={'action': 'STATUS', 'wt': 'json'})\n", "        \n", "        if response.status_code != 200:\n", "            logger.error(f\"Failed to list Solr cores. Status code: {response.status_code}\")\n", "            return []\n", "        \n", "        data = response.json()\n", "        return list(data.get('status', {}).keys())\n", "    except Exception as e:\n", "        logger.error(f\"Error listing Solr cores: {e}\")\n", "        return []\n", "\n", "def test_core_connectivity(core_name, base_url=SOLR_BASE_URL):\n", "    \"\"\"\n", "    Test connectivity to a specific Solr core.\n", "    \n", "    Args:\n", "        core_name (str): Name of the Solr core\n", "        base_url (str): Base URL of the Solr instance\n", "        \n", "    Returns:\n", "        dict: Information about the core\n", "    \"\"\"\n", "    try:\n", "        # Test a simple query\n", "        core_url = f\"{base_url}{core_name}/select\"\n", "        response = requests.get(core_url, params={'q': '*:*', 'rows': '1', 'wt': 'json'})\n", "        \n", "        if response.status_code != 200:\n", "            logger.error(f\"Failed to connect to core {core_name}. Status code: {response.status_code}\")\n", "            return None\n", "        \n", "        data = response.json()\n", "        return {\n", "            'core_name': core_name,\n", "            'num_found': data['response']['numFound'],\n", "            'status': 'OK' if response.status_code == 200 else 'Error',\n", "            'query_time_ms': data['responseHeader']['QTime']\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error testing core {core_name}: {e}\")\n", "        return {\n", "            'core_name': core_name,\n", "            'status': 'Error',\n", "            'error': str(e)\n", "        }"]}, {"cell_type": "code", "execution_count": null, "id": "run-basic-connectivity", "metadata": {}, "outputs": [], "source": ["# Run basic connectivity tests\n", "print(\"Testing Solr connectivity...\\n\")\n", "\n", "# Get Solr information\n", "solr_info = get_solr_info()\n", "if solr_info:\n", "    print(f\"Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}\")\n", "    print(f\"Solr Implementation: {solr_info.get('lucene', {}).get('solr-impl-version', 'Unknown')}\")\n", "    print(f\"JVM: {solr_info.get('jvm', {}).get('name', 'Unknown')} {solr_info.get('jvm', {}).get('version', 'Unknown')}\")\n", "    print(f\"System Memory: {solr_info.get('system', {}).get('totalPhysicalMemorySize', 'Unknown')} bytes\")\n", "else:\n", "    print(\"Could not retrieve Solr information. Check connectivity.\")\n", "\n", "# List cores\n", "print(\"\\nListing Solr cores...\")\n", "cores = list_solr_cores()\n", "if cores:\n", "    print(f\"Found {len(cores)} cores:\")\n", "    for core in cores:\n", "        print(f\"  - {core}\")\n", "else:\n", "    print(\"No cores found or could not retrieve core list.\")\n", "\n", "# Test connectivity to relevant cores\n", "print(\"\\nTesting connectivity to relevant cores...\")\n", "core_results = []\n", "for country in TEST_COUNTRIES:\n", "    core_name = f\"core_cvsearch_{country}\"\n", "    result = test_core_connectivity(core_name)\n", "    core_results.append(result)\n", "    if result:\n", "        print(f\"  - {core_name}: {result['status']} (Documents: {result.get('num_found', 'Unknown')}, Query Time: {result.get('query_time_ms', 'Unknown')}ms)\")\n", "    else:\n", "        print(f\"  - {core_name}: Connection failed\")"]}, {"cell_type": "markdown", "id": "query-construction-section", "metadata": {}, "source": ["## 2. Query Construction and URL Length Testing\n", "\n", "Now let's test query construction, particularly focusing on URL length limitations when querying with multiple entity IDs."]}, {"cell_type": "code", "execution_count": null, "id": "query-construction-functions", "metadata": {}, "outputs": [], "source": ["def get_sample_entity_ids(country, num_ids=100):\n", "    \"\"\"\n", "    Get a sample of entity IDs from a Solr core.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        num_ids (int): Number of IDs to retrieve\n", "        \n", "    Returns:\n", "        list: List of entity IDs\n", "    \"\"\"\n", "    try:\n", "        core_name = f\"core_cvsearch_{country}\"\n", "        solr_url = f\"{SOLR_BASE_URL}{core_name}/select\"\n", "        \n", "        # Query for entity IDs\n", "        params = {\n", "            'q': '*:*',\n", "            'fl': 'id,entity_id',\n", "            'rows': str(num_ids),\n", "            'wt': 'json'\n", "        }\n", "        \n", "        response = requests.get(solr_url, params=params)\n", "        \n", "        if response.status_code != 200:\n", "            logger.error(f\"Failed to get entity IDs for {country}. Status code: {response.status_code}\")\n", "            return []\n", "        \n", "        data = response.json()\n", "        docs = data['response']['docs']\n", "        \n", "        # Extract entity IDs\n", "        entity_ids = []\n", "        for doc in docs:\n", "            if 'entity_id' in doc:\n", "                entity_ids.append(doc['entity_id'])\n", "        \n", "        return entity_ids\n", "    except Exception as e:\n", "        logger.error(f\"Error getting entity IDs for {country}: {e}\")\n", "        return []\n", "\n", "def construct_query_url(country, entity_ids, fields):\n", "    \"\"\"\n", "    Construct a Solr query URL for the given entity IDs and fields.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        entity_ids (list): List of entity IDs\n", "        fields (list): List of fields to retrieve\n", "        \n", "    Returns:\n", "        str: <PERSON><PERSON> query URL\n", "    \"\"\"\n", "    try:\n", "        core_name = f\"core_cvsearch_{country}\"\n", "        solr_url = f\"{SOLR_BASE_URL}{core_name}/select\"\n", "        \n", "        # Convert entity IDs to strings and join with OR\n", "        ids_string = \"+OR+\".join([str(eid) for eid in entity_ids])\n", "        \n", "        # Join fields\n", "        fields_string = \",\".join(fields)\n", "        \n", "        # Construct query URL\n", "        query_url = f\"{solr_url}?q=*:*&fq=entity_id:+{ids_string}&fl={fields_string}&rows=10000000&wt=json&indent=true\"\n", "        \n", "        return query_url\n", "    except Exception as e:\n", "        logger.error(f\"Error constructing query URL: {e}\")\n", "        return None\n", "\n", "def test_url_length(country, max_ids=1000, step=50):\n", "    \"\"\"\n", "    Test URL length limitations by constructing queries with increasing numbers of entity IDs.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        max_ids (int): Maximum number of IDs to test with\n", "        step (int): Step size for increasing the number of IDs\n", "        \n", "    Returns:\n", "        pd.DataFrame: Results of the URL length tests\n", "    \"\"\"\n", "    try:\n", "        # Get a sample of entity IDs\n", "        all_entity_ids = get_sample_entity_ids(country, max_ids)\n", "        if not all_entity_ids:\n", "            logger.error(f\"No entity IDs found for {country}\")\n", "            return None\n", "        \n", "        # Fields to retrieve\n", "        fields = [\"id\", \"entity_id\"] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Test with increasing numbers of IDs\n", "        results = []\n", "        for num_ids in range(step, min(max_ids, len(all_entity_ids)) + 1, step):\n", "            entity_ids = all_entity_ids[:num_ids]\n", "            \n", "            # Construct query URL\n", "            query_url = construct_query_url(country, entity_ids, fields)\n", "            url_length = len(query_url) if query_url else 0\n", "            \n", "            # Test if the URL works\n", "            success = False\n", "            error = None\n", "            query_time = None\n", "            \n", "            if query_url:\n", "                try:\n", "                    start_time = time.time()\n", "                    response = requests.get(query_url)\n", "                    end_time = time.time()\n", "                    query_time = (end_time - start_time) * 1000  # Convert to ms\n", "                    \n", "                    success = response.status_code == 200\n", "                    if not success:\n", "                        error = f\"Status code: {response.status_code}\"\n", "                except Exception as e:\n", "                    error = str(e)\n", "            \n", "            # Record results\n", "            results.append({\n", "                'num_ids': num_ids,\n", "                'url_length': url_length,\n", "                'success': success,\n", "                'error': error,\n", "                'query_time_ms': query_time\n", "            })\n", "            \n", "            # Log progress\n", "            status = \"Success\" if success else f\"Failed: {error}\"\n", "            logger.info(f\"Tested with {num_ids} IDs: URL length = {url_length}, Status = {status}\")\n", "            \n", "            # Stop if we encounter a failure\n", "            if not success:\n", "                logger.warning(f\"Stopping test due to failure at {num_ids} IDs\")\n", "                break\n", "        \n", "        # Convert results to DataFrame\n", "        return pd.DataFrame(results)\n", "    except Exception as e:\n", "        logger.error(f\"Error in URL length test: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "id": "run-url-length-test", "metadata": {}, "outputs": [], "source": ["# Run URL length test for a country\n", "country = TEST_COUNTRIES[0]  # Use the first test country\n", "print(f\"Testing URL length limitations for {country}...\")\n", "\n", "# Run the test with a smaller number for initial testing\n", "url_test_results = test_url_length(country, max_ids=500, step=50)\n", "\n", "if url_test_results is not None and not url_test_results.empty:\n", "    # Display results\n", "    print(\"\\nURL Length Test Results:\")\n", "    display(url_test_results)\n", "    \n", "    # Plot results\n", "    plt.figure(figsize=(12, 6))\n", "    \n", "    # URL length vs. number of IDs\n", "    plt.subplot(1, 2, 1)\n", "    plt.plot(url_test_results['num_ids'], url_test_results['url_length'], marker='o')\n", "    plt.xlabel('Number of Entity IDs')\n", "    plt.ylabel('URL Length (characters)')\n", "    plt.title('URL Length vs. Number of Entity IDs')\n", "    plt.grid(True)\n", "    \n", "    # Query time vs. number of IDs\n", "    plt.subplot(1, 2, 2)\n", "    plt.plot(url_test_results['num_ids'], url_test_results['query_time_ms'], marker='o')\n", "    plt.xlabel('Number of Entity IDs')\n", "    plt.ylabel('Query Time (ms)')\n", "    plt.title('Query Time vs. Number of Entity IDs')\n", "    plt.grid(True)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Determine the maximum safe number of IDs\n", "    max_safe_ids = url_test_results[url_test_results['success']]['num_ids'].max()\n", "    max_safe_url_length = url_test_results[url_test_results['success']]['url_length'].max()\n", "    \n", "    print(f\"\\nMaximum safe number of entity IDs: {max_safe_ids}\")\n", "    print(f\"Maximum safe URL length: {max_safe_url_length} characters\")\n", "    \n", "    # Calculate average URL length per ID\n", "    avg_length_per_id = url_test_results['url_length'].iloc[-1] / url_test_results['num_ids'].iloc[-1]\n", "    print(f\"Average URL length per entity ID: {avg_length_per_id:.2f} characters\")\n", "    \n", "    # Estimate maximum number of IDs for common URL length limits\n", "    for limit in [2000, 4000, 8000, 16000]:\n", "        est_max_ids = int(limit / avg_length_per_id)\n", "        print(f\"Estimated maximum IDs for {limit} character URL limit: {est_max_ids}\")\n", "else:\n", "    print(\"URL length test failed or returned no results.\")"]}, {"cell_type": "markdown", "id": "alternative-approaches-section", "metadata": {}, "source": ["## 3. Alternative Query Approaches\n", "\n", "Based on the URL length limitations, let's explore alternative approaches for querying Solr with large numbers of entity IDs."]}, {"cell_type": "code", "execution_count": null, "id": "alternative-approaches-functions", "metadata": {}, "outputs": [], "source": ["def construct_post_query(country, entity_ids, fields):\n", "    \"\"\"\n", "    Construct a Solr query using POST method for the given entity IDs and fields.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        entity_ids (list): List of entity IDs\n", "        fields (list): List of fields to retrieve\n", "        \n", "    Returns:\n", "        tuple: (url, params) for the POST request\n", "    \"\"\"\n", "    try:\n", "        core_name = f\"core_cvsearch_{country}\"\n", "        solr_url = f\"{SOLR_BASE_URL}{core_name}/select\"\n", "        \n", "        # Convert entity IDs to a filter query\n", "        entity_ids_str = \" OR \".join([f\"entity_id:{eid}\" for eid in entity_ids])\n", "        \n", "        # Prepare parameters for POST request\n", "        params = {\n", "            'q': '*:*',\n", "            'fq': f\"({entity_ids_str})\",\n", "            'fl': ','.join(fields),\n", "            'rows': '10000000',\n", "            'wt': 'json',\n", "            'indent': 'true'\n", "        }\n", "        \n", "        return solr_url, params\n", "    except Exception as e:\n", "        logger.error(f\"Error constructing POST query: {e}\")\n", "        return None, None\n", "\n", "def test_post_query(country, num_ids=500):\n", "    \"\"\"\n", "    Test querying Solr using POST method with a large number of entity IDs.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        num_ids (int): Number of entity IDs to use\n", "        \n", "    Returns:\n", "        dict: Results of the test\n", "    \"\"\"\n", "    try:\n", "        # Get a sample of entity IDs\n", "        entity_ids = get_sample_entity_ids(country, num_ids)\n", "        if not entity_ids:\n", "            logger.error(f\"No entity IDs found for {country}\")\n", "            return None\n", "        \n", "        # Fields to retrieve\n", "        fields = [\"id\", \"entity_id\"] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Construct POST query\n", "        solr_url, params = construct_post_query(country, entity_ids, fields)\n", "        if not solr_url or not params:\n", "            return None\n", "        \n", "        # Execute the query\n", "        start_time = time.time()\n", "        response = requests.post(solr_url, data=params)\n", "        end_time = time.time()\n", "        query_time = (end_time - start_time) * 1000  # Convert to ms\n", "        \n", "        # Check results\n", "        success = response.status_code == 200\n", "        error = None if success else f\"Status code: {response.status_code}\"\n", "        \n", "        # Get number of documents returned\n", "        num_docs = 0\n", "        if success:\n", "            data = response.json()\n", "            num_docs = data['response']['numFound']\n", "        \n", "        return {\n", "            'num_ids': num_ids,\n", "            'success': success,\n", "            'error': error,\n", "            'query_time_ms': query_time,\n", "            'num_docs': num_docs\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error in POST query test: {e}\")\n", "        return None\n", "\n", "def test_batch_query(country, total_ids=1000, batch_size=100):\n", "    \"\"\"\n", "    Test querying Solr in batches with a large number of entity IDs.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        total_ids (int): Total number of entity IDs to use\n", "        batch_size (int): Size of each batch\n", "        \n", "    Returns:\n", "        dict: Results of the test\n", "    \"\"\"\n", "    try:\n", "        # Get a sample of entity IDs\n", "        all_entity_ids = get_sample_entity_ids(country, total_ids)\n", "        if not all_entity_ids:\n", "            logger.error(f\"No entity IDs found for {country}\")\n", "            return None\n", "        \n", "        # Fields to retrieve\n", "        fields = [\"id\", \"entity_id\"] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Split into batches\n", "        batches = [all_entity_ids[i:i+batch_size] for i in range(0, len(all_entity_ids), batch_size)]\n", "        \n", "        # Process each batch\n", "        all_docs = []\n", "        batch_results = []\n", "        total_query_time = 0\n", "        \n", "        for i, batch in enumerate(batches):\n", "            # Construct query URL\n", "            query_url = construct_query_url(country, batch, fields)\n", "            \n", "            # Execute the query\n", "            start_time = time.time()\n", "            response = requests.get(query_url)\n", "            end_time = time.time()\n", "            query_time = (end_time - start_time) * 1000  # Convert to ms\n", "            total_query_time += query_time\n", "            \n", "            # Check results\n", "            success = response.status_code == 200\n", "            error = None if success else f\"Status code: {response.status_code}\"\n", "            \n", "            # Get documents\n", "            num_docs = 0\n", "            if success:\n", "                data = response.json()\n", "                docs = data['response']['docs']\n", "                num_docs = len(docs)\n", "                all_docs.extend(docs)\n", "            \n", "            # Record batch results\n", "            batch_results.append({\n", "                'batch': i+1,\n", "                'num_ids': len(batch),\n", "                'success': success,\n", "                'error': error,\n", "                'query_time_ms': query_time,\n", "                'num_docs': num_docs\n", "            })\n", "            \n", "            # Log progress\n", "            logger.info(f\"Processed batch {i+1}/{len(batches)}: {num_docs} documents retrieved\")\n", "            \n", "            # Stop if we encounter a failure\n", "            if not success:\n", "                logger.warning(f\"Stopping test due to failure in batch {i+1}\")\n", "                break\n", "        \n", "        return {\n", "            'total_ids': total_ids,\n", "            'batch_size': batch_size,\n", "            'num_batches': len(batches),\n", "            'total_docs': len(all_docs),\n", "            'total_query_time_ms': total_query_time,\n", "            'batch_results': batch_results\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error in batch query test: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "id": "run-alternative-approaches", "metadata": {}, "outputs": [], "source": ["# Test POST query approach\n", "country = TEST_COUNTRIES[0]  # Use the first test country\n", "print(f\"Testing POST query approach for {country}...\")\n", "\n", "post_test_result = test_post_query(country, num_ids=500)\n", "if post_test_result:\n", "    print(\"\\nPOST Query Test Results:\")\n", "    for key, value in post_test_result.items():\n", "        print(f\"  - {key}: {value}\")\n", "else:\n", "    print(\"POST query test failed.\")\n", "\n", "# Test batch query approach\n", "print(f\"\\nTesting batch query approach for {country}...\")\n", "\n", "batch_test_result = test_batch_query(country, total_ids=500, batch_size=100)\n", "if batch_test_result:\n", "    print(\"\\nBatch Query Test Results:\")\n", "    print(f\"  - Total IDs: {batch_test_result['total_ids']}\")\n", "    print(f\"  - <PERSON><PERSON>: {batch_test_result['batch_size']}\")\n", "    print(f\"  - Number of Batches: {batch_test_result['num_batches']}\")\n", "    print(f\"  - Total Documents Retrieved: {batch_test_result['total_docs']}\")\n", "    print(f\"  - Total Query Time: {batch_test_result['total_query_time_ms']:.2f} ms\")\n", "    \n", "    # Display batch results\n", "    print(\"\\nBatch Details:\")\n", "    batch_df = pd.DataFrame(batch_test_result['batch_results'])\n", "    display(batch_df)\n", "    \n", "    # Plot batch query times\n", "    plt.figure(figsize=(10, 6))\n", "    plt.bar(batch_df['batch'], batch_df['query_time_ms'])\n", "    plt.xlabel('Batch Number')\n", "    plt.ylabel('Query Time (ms)')\n", "    plt.title('Query Time per Batch')\n", "    plt.grid(True, axis='y')\n", "    plt.show()\n", "else:\n", "    print(\"Batch query test failed.\")"]}, {"cell_type": "markdown", "id": "field-existence-section", "metadata": {}, "source": ["## 4. Field Existence and Data Quality Check\n", "\n", "Let's check if the fields we need actually exist in the documents and analyze their quality."]}, {"cell_type": "code", "execution_count": null, "id": "field-existence-functions", "metadata": {}, "outputs": [], "source": ["def check_field_existence(country, num_docs=500):\n", "    \"\"\"\n", "    Check if the required fields exist in the documents and analyze their quality.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        num_docs (int): Number of documents to check\n", "        \n", "    Returns:\n", "        dict: Field existence statistics\n", "    \"\"\"\n", "    try:\n", "        # Get a sample of documents\n", "        core_name = f\"core_cvsearch_{country}\"\n", "        solr_url = f\"{SOLR_BASE_URL}{core_name}/select\"\n", "        \n", "        # Fields to check\n", "        fields = [\"id\", \"entity_id\"] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Query for documents\n", "        params = {\n", "            'q': '*:*',\n", "            'fl': ','.join(fields),\n", "            'rows': str(num_docs),\n", "            'wt': 'json'\n", "        }\n", "        \n", "        response = requests.get(solr_url, params=params)\n", "        \n", "        if response.status_code != 200:\n", "            logger.error(f\"Failed to get documents for {country}. Status code: {response.status_code}\")\n", "            return None\n", "        \n", "        data = response.json()\n", "        docs = data['response']['docs']\n", "        \n", "        # Check field existence\n", "        field_stats = {}\n", "        for field in fields:\n", "            # Count documents with this field\n", "            docs_with_field = [doc for doc in docs if field in doc]\n", "            \n", "            # Calculate statistics\n", "            field_stats[field] = {\n", "                'exists_count': len(docs_with_field),\n", "                'exists_percent': len(docs_with_field) / len(docs) * 100 if docs else 0,\n", "                'missing_count': len(docs) - len(docs_with_field),\n", "                'missing_percent': (len(docs) - len(docs_with_field)) / len(docs) * 100 if docs else 0\n", "            }\n", "            \n", "            # Additional statistics for text fields\n", "            if field in SOURCE_FIELDS.keys():\n", "                # Get non-empty values\n", "                values = []\n", "                for doc in docs_with_field:\n", "                    val = doc[field]\n", "                    if isinstance(val, list) and val:\n", "                        val = val[0]\n", "                    if val:  # Skip empty values\n", "                        values.append(val)\n", "                \n", "                # Calculate length statistics\n", "                if values:\n", "                    lengths = [len(str(val)) for val in values]\n", "                    field_stats[field].update({\n", "                        'non_empty_count': len(values),\n", "                        'non_empty_percent': len(values) / len(docs) * 100 if docs else 0,\n", "                        'min_length': min(lengths),\n", "                        'max_length': max(lengths),\n", "                        'avg_length': sum(lengths) / len(lengths)\n", "                    })\n", "        \n", "        return {\n", "            'total_docs': len(docs),\n", "            'field_stats': field_stats\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error checking field existence: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "id": "run-field-existence-check", "metadata": {}, "outputs": [], "source": ["# Check field existence\n", "country = TEST_COUNTRIES[0]  # Use the first test country\n", "print(f\"Checking field existence for {country}...\")\n", "\n", "field_check_result = check_field_existence(country, num_docs=500)\n", "if field_check_result:\n", "    print(f\"\\nField Existence Check Results (Total Documents: {field_check_result['total_docs']}):\\n\")\n", "    \n", "    # Create a DataFrame for better visualization\n", "    field_data = []\n", "    for field, stats in field_check_result['field_stats'].items():\n", "        field_data.append({\n", "            'Field': field,\n", "            'Exists Count': stats['exists_count'],\n", "            'Exists %': f\"{stats['exists_percent']:.2f}%\",\n", "            'Missing Count': stats['missing_count'],\n", "            'Missing %': f\"{stats['missing_percent']:.2f}%\",\n", "            'Non-Empty Count': stats.get('non_empty_count', 'N/A'),\n", "            'Non-Empty %': f\"{stats.get('non_empty_percent', 0):.2f}%\" if 'non_empty_percent' in stats else 'N/A',\n", "            'Min Length': stats.get('min_length', 'N/A'),\n", "            'Max Length': stats.get('max_length', 'N/A'),\n", "            'Avg Length': f\"{stats.get('avg_length', 0):.2f}\" if 'avg_length' in stats else 'N/A'\n", "        })\n", "    \n", "    field_df = pd.DataFrame(field_data)\n", "    display(field_df)\n", "    \n", "    # Plot field existence percentages\n", "    plt.figure(figsize=(12, 6))\n", "    \n", "    # Field existence\n", "    plt.subplot(1, 2, 1)\n", "    exists_data = [stats['exists_percent'] for field, stats in field_check_result['field_stats'].items()]\n", "    plt.bar(field_check_result['field_stats'].keys(), exists_data)\n", "    plt.xlabel('Field')\n", "    plt.ylabel('Existence Percentage')\n", "    plt.title('Field Existence Percentage')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(True, axis='y')\n", "    \n", "    # Field lengths for text fields\n", "    plt.subplot(1, 2, 2)\n", "    text_fields = [field for field in SOURCE_FIELDS.keys() if 'avg_length' in field_check_result['field_stats'][field]]\n", "    if text_fields:\n", "        avg_lengths = [field_check_result['field_stats'][field]['avg_length'] for field in text_fields]\n", "        plt.bar(text_fields, avg_lengths)\n", "        plt.xlabel('Field')\n", "        plt.ylabel('Average Length (characters)')\n", "        plt.title('Average Field Length')\n", "        plt.xticks(rotation=45)\n", "        plt.grid(True, axis='y')\n", "    else:\n", "        plt.text(0.5, 0.5, 'No text fields with length data', ha='center', va='center')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Recommendations based on field existence\n", "    print(\"\\nRecommendations:\")\n", "    for field, stats in field_check_result['field_stats'].items():\n", "        if stats['missing_percent'] > 0:\n", "            print(f\"  - Field '{field}' is missing in {stats['missing_percent']:.2f}% of documents. Consider handling missing values.\")\n", "        \n", "        if field in SOURCE_FIELDS.keys() and 'non_empty_percent' in stats and stats['non_empty_percent'] < 100:\n", "            print(f\"  - Field '{field}' has empty values in {100 - stats['non_empty_percent']:.2f}% of documents where it exists. Consider handling empty values.\")\n", "else:\n", "    print(\"Field existence check failed.\")"]}, {"cell_type": "markdown", "id": "conclusions-section", "metadata": {}, "source": ["## 5. Conclusions and Recommendations\n", "\n", "Based on the tests performed, let's summarize our findings and provide recommendations for the Job Offer Translation Pipeline."]}, {"cell_type": "code", "execution_count": null, "id": "conclusions", "metadata": {}, "outputs": [], "source": ["# This cell will be filled in after running the tests above\n", "print(\"Conclusions and Recommendations for Solr Connectivity and Query Construction:\")\n", "print(\"\\n1. Connectivity:\")\n", "print(\"   - [To be filled based on test results]\")\n", "\n", "print(\"\\n2. URL Length Limitations:\")\n", "print(\"   - [To be filled based on test results]\")\n", "\n", "print(\"\\n3. Query Approaches:\")\n", "print(\"   - [To be filled based on test results]\")\n", "\n", "print(\"\\n4. Field Existence and Data Quality:\")\n", "print(\"   - [To be filled based on test results]\")\n", "\n", "print(\"\\n5. Recommended Implementation for the Pipeline:\")\n", "print(\"   - [To be filled based on test results]\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}