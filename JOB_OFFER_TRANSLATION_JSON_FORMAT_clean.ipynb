{"cells": [{"cell_type": "code", "execution_count": null, "id": "e4ba7281", "metadata": {}, "outputs": [], "source": ["# Job Offer Translation - Fixed JSON Format Implementation\",", "    \"\",", "    \"This notebook implements the job offer translation using OpenAI's Batch API with a fixed implementation that handles the JSON format requirement. It uses the `gpt-4.1-nano-2025-04-14` model as requested.\",", "    \"\",", "    \"Key fixes in this implementation:\",", "    \"1. The messages now include the word \"json\" to satisfy the requirement for using `response_format` of type `json_object`\",", "    \"2. The `body` field in the batch request is properly formatted as an object (not a string)\",", "    \"3. The batch file is saved with UTF-8 encoding without BOM\",", "    \"4. The file uses Unix line separators (LF instead of CR LF)\",", "    \"\",", "    \"This notebook is designed to run on the server via X2GO where the Solr connection works."]}, {"cell_type": "code", "execution_count": null, "id": "3c8d33c7", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\",", "    \"try:\",", "    \"    import json\",", "    \"    import logging\",", "    \"    import os\",", "    \"    import time\",", "    \"    import re\",", "    \"    import html\",", "    \"    from datetime import datetime\",", "    \"    import uuid\",", "    \"    print(\"✅ Basic Python libraries successfully imported\")\",", "    \"except ImportError as e:\",", "    \"    print(f\"❌ Error importing basic libraries: {e}\")\",", "    \"\",", "    \"# Try importing external libraries\",", "    \"missing_libraries = []\",", "    \"\",", "    \"try:\",", "    \"    import requests\",", "    \"    print(\"✅ requests library successfully imported\")\",", "    \"except ImportError:\",", "    \"    missing_libraries.append(\"requests\")\",", "    \"    print(\"❌ requests library not found\")\",", "    \"\",", "    \"try:\",", "    \"    import pysolr\",", "    \"    print(\"✅ pysolr library successfully imported\")\",", "    \"except ImportError:\",", "    \"    missing_libraries.append(\"pysolr\")\",", "    \"    print(\"❌ pysolr library not found\")\",", "    \"\",", "    \"try:\",", "    \"    from openai import OpenAI\",", "    \"    print(\"✅ openai library successfully imported\")\",", "    \"except ImportError:\",", "    \"    missing_libraries.append(\"openai\")\",", "    \"    print(\"❌ openai library not found\")\",", "    \"\",", "    \"# If any libraries are missing, print installation instructions\",", "    \"if missing_libraries:\",", "    \"    print(\"\\⚠️ Some required libraries are missing. Please install them using pip:\")\",", "    \"    for lib in missing_libraries:\",", "    \"        print(f\"pip install {lib}\")\",", "    \"    print(\"\\After installing, restart the kernel and run this cell again.\")\",", "    \"else:\",", "    \"    print(\"\\✅ All required libraries are installed!\")\",", "    \"\",", "    \"# Configure logging\",", "    \"logging.basicConfig(level=logging.INFO, \",", "    \"                    format='%(asctime)s - %(levelname)s - %(message)s')\",", "    \"logger = logging.getLogger('job_offer_translation')"]}, {"cell_type": "code", "execution_count": null, "id": "f9a13b9e", "metadata": {}, "outputs": [], "source": ["# Configuration\",", "    \"SOLR_BASE_URL = \"http://************:8983/solr/\"\",", "    \"\",", "    \"# Fields we're interested in for job offers\",", "    \"SOURCE_FIELDS = {\",", "    \"    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste", "", "    \"    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\",", "    \"}\",", "    \"\",", "    \"# Countries to work with\",", "    \"COUNTRIES = ['algerie', 'centrafrique', 'benin', 'burkina']\",", "    \"\",", "    \"# Number of documents to process in one batch\",", "    \"BATCH_SIZE = 2  # Start with a small batch for testing\",", "    \"\",", "    \"# OpenAI API configuration\",", "    \"OPENAI_MODEL = \"gpt-4.1-nano-2025-04-14\"  # Using the nano model as requested\",", "    \"\",", "    \"# Directory for storing batch files and results\",", "    \"BATCH_DIR = \"batch_files\"\",", "    \"RESULTS_DIR = \"translation_results\"\",", "    \"os.makedirs(BATCH_DIR, exist_ok=True)\",", "    \"os.makedirs(RESULTS_DIR, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "id": "115ccfb4", "metadata": {}, "outputs": [], "source": ["## 1. Solr Connection Functions\",", "    \"\",", "    \"These functions handle connecting to the Solr JobSearch cores and retrieving job offers."]}, {"cell_type": "code", "execution_count": null, "id": "dcf9dccb", "metadata": {}, "outputs": [], "source": ["# Create a connection pool for reusing Solr connections\",", "    \"solr_connections = {}\",", "    \"\",", "    \"def get_jobsearch_connection(country, timeout=30):\",", "    \"    \"\"\"\",", "    \"    Get a connection to the JobSearch Solr core for the specified country.\",", "    \"    Uses connection pooling to reuse existing connections.\",", "    \"    \",", "    \"    Args:\",", "    \"        country (str): Country code\",", "    \"        timeout (int): Connection timeout in seconds\",", "    \"        \",", "    \"    Returns:\",", "    \"        pysolr.Solr: Solr connection\",", "    \"    \"\"\"\",", "    \"    try:\",", "    \"        # Check if we already have a connection for this country\",", "    \"        if country in solr_connections:\",", "    \"            logger.info(f\"Using existing connection for {country}\")\",", "    \"            return solr_connections[country]\",", "    \"        \",", "    \"        # Use the correct core pattern: core_jobsearch_[country]\",", "    \"        # Ensure the URL format is exactly as expected\",", "    \"        solr_url = f\"{SOLR_BASE_URL}core_jobsearch_{country}\"\",", "    \"        logger.info(f\"Creating new connection to JobSearch Solr core at {solr_url}\")\",", "    \"        \",", "    \"        # Log the exact URL format that will be used for queries\",", "    \"        example_query_url = f\"{solr_url}/select?q=*:*&fq=*:*&fl=id,entity_id&rows=10&start=0&sort=entity_id+asc&wt=json\"\",", "    \"        logger.debug(f\"Example query URL: {example_query_url}\")\",", "    \"        \",", "    \"        # Create PySOLR connection with timeout\",", "    \"        solr = pysolr.Solr(solr_url, timeout=timeout)\",", "    \"        \",", "    \"        # Store in connection pool\",", "    \"        solr_connections[country] = solr\",", "    \"        return solr\",", "    \"    except Exception as e:\",", "    \"        logger.error(f\"Error connecting to JobSearch Solr for {country}: {e}\")\",", "    \"        return None\",", "    \"\",", "    \"def query_solr_direct(country, params=None):", "    \"\"", "    Query Solr directly using HTTP requests instead of PySOLR.", "    This is useful for testing and debugging Solr queries.", "", "    Args:", "        country (str): Country code", "        params (dict): Query parameters", "", "    Returns:", "        dict: Solr response as JSON", "    \"\"", "    try:", "        # Set default parameters if none provided", "        if params is None:", "            params = {", "                'q': '*:*',", "                'fq': '*:*',", "                'fl': 'id,entity_id',", "                'rows': 10,", "                'start': 0,", "                'sort': 'entity_id asc',", "                'wt': 'json'", "            }", "", "        # Construct the URL", "        solr_url = f\"{SOLR_BASE_URL}core_jobsearch_{country}/select", "", "        # Make the request", "        logger.info(f\"Making direct HTTP request to Solr: {solr_url}\")", "        response = requests.get(solr_url, params=params, timeout=30)", "", "        # Check if the request was successful", "        if response.status_code == 200:", "            # Parse the JSON response", "            result = response.json()", "            logger.info(f\"Direct Solr query successful. Found {result.get('response', {}).get('numFound', 0)} documents.\")", "            return result", "        else:", "            logger.error(f\"Direct Solr query failed with status code {response.status_code}: {response.text}\")", "            return None", "    except Exception as e:", "        logger.error(f\"Error in direct Solr query: {e}\")", "        return None", "", "def validate_job_offer(offer):", "    \"\"", "    Validate a job offer document to ensure it has all required fields and proper ID format.", "", "    Args:", "        offer (dict): Job offer document", "", "    Returns:", "        tuple: (is_valid, reason)", "    \"\"", "    # Validate ID format", "    if 'id' not in offer or not isinstance(offer['id'], str) or '/node/' not in offer['id']:", "        return False, f\"Invalid ID format: {offer.get('id', 'unknown')}", "", "    # Validate entity_id field", "    if 'entity_id' not in offer:", "        return False, f\"Missing entity_id field for offer {offer['id']}", "", "    # Cross-validate id and entity_id fields", "    try:", "        id_parts = offer['id'].split('/node/')", "        if len(id_parts) == 2 and id_parts[1].isdigit():", "            numeric_id = int(id_parts[1])", "            if numeric_id != offer['entity_id']:", "                return False, f\"Mismatched IDs: id={offer['id']}, entity_id={offer['entity_id']}", "        else:", "            return False, f\"Unparseable ID format: {offer['id']}", "    except Exception as e:", "        return False, f\"Error validating offer IDs: {e}", "", "    # Check if all source fields exist", "    has_source_fields = all(source_field in offer for source_field in SOURCE_FIELDS.keys())", "    if not has_source_fields:", "        return False, f\"Missing source fields for offer {offer['id']}", "", "    return True, \"<PERSON><PERSON>", "", "def get_job_offers(country, num_offers=BATCH_SIZE, start=0, skip_translated=True):\",", "    \"    \"\"\"\",", "    \"    Get job offers from the JobSearch Solr core with batch processing and verification.\",", "    \"    \",", "    \"    Args:\",", "    \"        country (str): Country code\",", "    \"        num_offers (int): Number of job offers to retrieve\",", "    \"        start (int): Starting offset for pagination\",", "    \"        skip_translated (bool): Whether to skip documents that already have translated fields\",", "    \"        \",", "    \"    Returns:\",", "    \"        list: List of job offer documents that need translation\",", "    \"    \"\"\"\",", "    \"    try:\",", "    \"        solr = get_jobsearch_connection(country)\",", "    \"        if not solr:\",", "    \"            logger.error(f\"Failed to get JobSearch Solr connection for {country}\")\",", "    \"            return []\",", "    \"        \",", "    \"        # Fields to retrieve - we no longer need to include translated fields\",", "    \"        # since we're filtering at the query level\",", "    \"        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())\",", "    \"        \",", "    \"        # Prepare query parameters\",", "    \"        params = {\",", "    \"            'q': '*:*',     # Main query\",", "    \"            'fl': ','.join(fields),  # Fields to return\",", "    \"            'rows': num_offers,      # Number of rows to return\",", "    \"            'start': start,          # Starting offset\",", "    \"            'sort': 'entity_id asc', # Sort by entity_id ascending\",", "    \"            'wt': 'json'             # Response format\",", "    \"        }\",", "    \"        \",", "    \"        # Add filter query to exclude documents with translations if requested\",", "    \"        if skip_translated:\",", "    \"            # Create a filter query that excludes documents with any translated fields\",", "    \"            translation_filters = []\",", "    \"            for target_field in SOURCE_FIELDS.values():\",", "    \"                translation_filters.append(f\"-{target_field}:[* TO *]\")\",", "    \"            \",", "    \"            # Combine filters with AND\",", "    \"            params['fq'] = \" AND \".join(translation_filters)\",", "    \"            logger.info(f\"Using filter query to exclude documents with translations: {params['fq']}\")\",", "    \"        else:\",", "    \"            # If not skipping translated documents, use a simple filter query\",", "    \"            params['fq'] = '*:*'\",", "    \"        \",", "    \"        # Construct the exact URL for logging/debugging\",", "    \"        solr_url = f\"{SOLR_BASE_URL}core_jobsearch_{country}/select\"\",", "    \"        query_params = '&'.join([f\"{k}={v}\" for k, v in params.items()])\",", "    \"        full_url = f\"{solr_url}?{query_params}\"\",", "    \"        logger.debug(f\"Full Solr query URL: {full_url}\")\",", "    \"        \",", "    \"        # Execute the query\",", "    \"        logger.info(f\"Retrieving job offers from {country} (start={start}, rows={num_offers})\")\",", "    \"        # Note: PySOLR's search method takes q as a positional argument and the rest as kwargs\",", "    \"        q = params.pop('q')  # Remove q from params to pass it separately\",", "    \"        results = solr.search(q, **params)\",", "    \"        \",", "    \"        # Convert to list of dictionaries\",", "    \"        all_offers = [dict(doc) for doc in results]\",", "    \"        logger.info(f\"Retrieved {len(all_offers)} job offers from {country} that need translation\")\",", "    \"        \",", "    \"        # We still need to validate the documents, but we don't need to check for translations\",", "    \"        valid_offers = []\",", "    \"        for offer in all_offers:\",", "    \"            # Validate the job offer using our validation function\",", "    \"            is_valid, reason = validate_job_offer(offer)\",", "    \"            if not is_valid:\",", "    \"                logger.warning(f\"Skipping offer: {reason}\")\",", "    \"                continue\",", "    \"            \",", "    \"            valid_offers.append(offer)\",", "    \"        \",", "    \"        logger.info(f\"{len(valid_offers)} out of {len(all_offers)} job offers are valid and need translation\")\",", "    \"        return valid_offers\",", "    \"    except Exception as e:\",", "    \"        logger.error(f\"Error retrieving job offers from {country}: {e}\")\",", "    \"        return []"]}, {"cell_type": "code", "execution_count": null, "id": "b8fd720c", "metadata": {}, "outputs": [], "source": ["## 2. Batch Processing Functions\",", "    \"\",", "    \"These functions handle preparing and processing batch files for the OpenAI Batch API."]}, {"cell_type": "code", "execution_count": null, "id": "41a4d3bc", "metadata": {}, "outputs": [], "source": ["def prepare_batch_file(job_offers, country):\",", "    \"    \"\"\"\",", "    \"    Prepare a batch file for OpenAI Batch API with the correct format.\",", "    \"    \",", "    \"    Args:\",", "    \"        job_offers (list): List of job offer documents\",", "    \"        country (str): Country code\",", "    \"        \",", "    \"    Returns:\",", "    \"        str: Path to the batch file\",", "    \"    \"\"\"\",", "    \"    try:\",", "    \"        if not job_offers:\",", "    \"            logger.warning(\"No job offers to process\")\",", "    \"            return None\",", "    \"        \",", "    \"        # Create a unique batch file name\",", "    \"        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\",", "    \"        batch_file_path = os.path.join(BATCH_DIR, f\"{country}_batch_{timestamp}.jsonl\")\",", "    \"        \",", "    \"        # Prepare batch requests\",", "    \"        batch_requests = []\",", "    \"        \",", "    \"        for offer in job_offers:\",", "    \"            offer_id = offer.get('id', str(uuid.uuid4()))\",", "    \"            \",", "    \"            # Process each source field\",", "    \"            for source_field, target_field in SOURCE_FIELDS.items():\",", "    \"                if source_field in offer:\",", "    \"                    value = offer[source_field]\",", "    \"                    \",", "    \"                    # Handle list values\",", "    \"                    if isinstance(value, list):\",", "    \"                        if not value:\",", "    \"                            continue\",", "    \"                        text = value[0]  # Use the first item\",", "    \"                    elif isinstance(value, str):\",", "    \"                        text = value\",", "    \"                    else:\",", "    \"                        continue\",", "    \"                    \",", "    \"                    # Skip empty text\",", "    \"                    if not text or not text.strip():\",", "    \"                        continue\",", "    \"                    \",", "    \"                    # Decode HTML entities\",", "    \"                    decoded_text = html.unescape(text)\",", "    \"                    \",", "    \"                    # Create a unique custom ID for this request\",", "    \"                    custom_id = f\"{offer_id}_{source_field}\"\",", "    \"                    \",", "    \"                    # Create the batch request with body as an object (not a string)\",", "    \"                    # Include the word \"json\" in the messages to satisfy the requirement\",", "    \"                    batch_request = {\",", "    \"                        \"custom_id\": custom_id,\",", "    \"                        \"method\": \"POST", "", "    \"                        \"url\": \"/v1/chat/completions", "", "    \"                        \"body\": {\",", "    \"                            \"model\": OPENAI_MODEL,\",", "    \"                            \"messages\": [\",", "    \"                                {\"role\": \"system\", \"content\": \"You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text.\"},\",", "    \"                                {\"role\": \"user\", \"content\": f\"Translate the following French job offer text to English and return as JSON:\\{decoded_text}\"}\",", "    "]}, {"cell_type": "code", "execution_count": null, "id": "aeb77ac8", "metadata": {}, "outputs": [], "source": ["# Test the direct HTTP query function\",", "    \"def test_direct_solr_query():\",", "    \"    print(\"Testing direct Solr query...\")\",", "    \"    \",", "    \"    # Test with a specific country\",", "    \"    country = 'algerie'  # Change to the country you want to test\",", "    \"    \",", "    \"    # Set up query parameters exactly matching the working URL format\",", "    \"    params = {\",", "    \"        'q': '*:*',\",", "    \"        'fq': '*:*',\",", "    \"        'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',\",", "    \"        'rows': 5,\",", "    \"        'start': 0,\",", "    \"        'sort': 'entity_id asc',\",", "    \"        'wt': 'json'\",", "    \"    }\",", "    \"    \",", "    \"    # Construct the URL manually for logging\",", "    \"    solr_url = f\"{SOLR_BASE_URL}core_jobsearch_{country}/select\"\",", "    \"    query_params = '&'.join([f\"{k}={v}\" for k, v in params.items()])\",", "    \"    full_url = f\"{solr_url}?{query_params}\"\",", "    \"    print(f\"Full query URL: {full_url}\")\",", "    \"    \",", "    \"    # Execute the query\",", "    \"    result = query_solr_direct(country, params)\",", "    \"    \",", "    \"    if result:\",", "    \"        response = result.get('response', {})\",", "    \"        num_found = response.get('numFound', 0)\",", "    \"        docs = response.get('docs', [])\",", "    \"        \",", "    \"        print(f\"Query successful! Found {num_found} documents.\")\",", "    \"        \",", "    \"        if docs:\",", "    \"            print(f\"First {len(docs)} documents:\")\",", "    \"            for i, doc in enumerate(docs):\",", "    \"                print(f\"\\Document {i+1}:\")\",", "    \"                print(f\"ID: {doc.get('id', 'unknown')}\")\",", "    \"                print(f\"Entity ID: {doc.get('entity_id', 'unknown')}\")\",", "    \"                \",", "    \"                # Check if translated fields already exist\",", "    \"                has_translations = any(target_field in doc for target_field in SOURCE_FIELDS.values())\",", "    \"                print(f\"Has translations: {has_translations}\")\",", "    \"                \",", "    \"                # Show a sample of the source fields\",", "    \"                for source_field in SOURCE_FIELDS.keys():\",", "    \"                    if source_field in doc:\",", "    \"                        value = doc[source_field]\",", "    \"                        if isinstance(value, list) and value:\",", "    \"                            sample = value[0][:100] + \"...\" if len(value[0]) > 100 else value[0]\",", "    \"                            print(f\"\\{source_field}: {sample}\")\",", "    \"    else:\",", "    \"        print(\"Query failed. Check the logs for details.\")\",", "    \"\",", "    \"# Run the test\",", "    \"test_direct_solr_query()"]}, {"cell_type": "code", "execution_count": null, "id": "b840e0d8", "metadata": {}, "outputs": [], "source": ["# Run basic connectivity tests\",", "    \"print(\"Testing Solr connectivity...\")\",", "    \"\",", "    \"# Get Solr information\",", "    \"solr_info = get_solr_info()\",", "    \"if solr_info:\",", "    \"    print(f\"Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}\")\",", "    \"    print(f\"Solr Implementation: {solr_info.get('lucene', {}).get('solr-impl-version', 'Unknown')}\")\",", "    \"    print(f\"JVM: {solr_info.get('jvm', {}).get('name', 'Unknown')} {solr_info.get('jvm', {}).get('version', 'Unknown')}\")\",", "    \"    print(f\"System Memory: {solr_info.get('system', {}).get('totalPhysicalMemorySize', 'Unknown')} bytes\")\",", "    \"else:\",", "    \"    print(\"Could not retrieve Solr information. Check connectivity.\")\",", "    \"\",", "    \"# List cores\",", "    \"print(\"\\Listing Solr cores...\")\",", "    \"cores = list_solr_cores()\",", "    \"if cores:\",", "    \"    print(f\"Found {len(cores)} cores:\")\",", "    \"    for core in cores:\",", "    \"        print(f\"  - {core}\")\",", "    \"else:\",", "    \"    print(\"No cores found or could not retrieve core list.\")\",", "    \"\",", "    \"# Test connectivity to relevant cores\",", "    \"print(\"\\Testing connectivity to relevant cores...\")\",", "    \"core_results = []\",", "    \"for country in TEST_COUNTRIES:\",", "    \"    core_name = f\"core_cvsearch_{country}\"\",", "    \"    result = test_core_connectivity(core_name)\",", "    \"    core_results.append(result)\",", "    \"    if result:\",", "    \"        print(f\"  - {core_name}: {result['status']} (Documents: {result.get('num_found', 'Unknown')}, Query Time: {result.get('query_time_ms', 'Unknown')}ms)\")\",", "    \"    else:\",", "    \"        print(f\"  - {core_name}: Connection failed\")"]}, {"cell_type": "code", "execution_count": null, "id": "6b5387ee", "metadata": {}, "outputs": [], "source": ["def process_batch(batch_file_path, client):\",", "    \"    \"\"\"\",", "    \"    Process a batch file using OpenAI Batch API.\",", "    \"    \",", "    \"    This is an improved implementation that handles the issue with retrieving the output file ID\",", "    \"    and properly processes error files.\",", "    \"    \",", "    \"    Args:\",", "    \"        batch_file_path (str): Path to the batch file\",", "    \"        client: OpenAI client\",", "    \"        \",", "    \"    Returns:\",", "    \"        str: Path to the output file\",", "    \"    \"\"\"\",", "    \"    if not client:\",", "    \"        logger.error(\"OpenAI client not initialized\")\",", "    \"        return None\",", "    \"    \",", "    \"    try:\",", "    \"        # Upload the batch file\",", "    \"        logger.info(f\"Uploading batch file: {batch_file_path}\")\",", "    \"        with open(batch_file_path, 'rb') as f:\",", "    \"            batch_file = client.files.create(\",", "    \"                file=f,\",", "    \"                purpose=\"batch\"\",", "    \"            )\",", "    \"        \",", "    \"        batch_file_id = batch_file.id\",", "    \"        logger.info(f\"Batch file uploaded with ID: {batch_file_id}\")\",", "    \"        \",", "    \"        # Create the batch\",", "    \"        logger.info(\"Creating batch job...\")\",", "    \"        batch = client.batches.create(\",", "    \"            input_file_id=batch_file_id,\",", "    \"            endpoint=\"/v1/chat/completions", "", "    \"            completion_window=\"24h", "", "    \"            metadata={\",", "    \"                \"description\": f\"Job offer translation batch {os.path.basename(batch_file_path)}\"\",", "    \"            }\",", "    \"        )\",", "    \"        \",", "    \"        batch_id = batch.id\",", "    \"        logger.info(f\"Batch created with ID: {batch_id}\")\",", "    \"        \",", "    \"        # Wait for the batch to complete\",", "    \"        logger.info(\"Waiting for batch to complete...\")\",", "    \"        max_attempts = 60  # Maximum number of attempts (60 minutes)\",", "    \"        attempt = 0\",", "    \"        \",", "    \"        while attempt < max_attempts:\",", "    \"            # Check batch status\",", "    \"            batch_status = client.batches.retrieve(batch_id)\",", "    \"            status = batch_status.status\",", "    \"            \",", "    \"            logger.info(f\"Batch status: {status}\")\",", "    \"            # Log full batch details for debugging\",", "    \"            logger.info(f\"Batch details: {batch_status}\")\",", "    \"            \",", "    \"            if status == \"completed\":\",", "    \"                # Check if output_file_id is available\",", "    \"                if hasattr(batch_status, 'output_file_id') and batch_status.output_file_id:\",", "    \"                    output_file_id = batch_status.output_file_id\",", "    \"                    logger.info(f\"Found output file ID: {output_file_id}\")\",", "    \"                    \",", "    \"                    # Download the output file\",", "    \"                    logger.info(f\"Downloading output file with ID: {output_file_id}\")\",", "    \"                    output_content = client.files.content(output_file_id)\",", "    \"                    \",", "    \"                    # Save the output to a file\",", "    \"                    output_file_path = os.path.join(RESULTS_DIR, f\"output_{os.path.basename(batch_file_path)}\")\",", "    \"                    with open(output_file_path, 'w', encoding='utf-8') as f:\",", "    \"                        f.write(output_content.text)\",", "    \"                    \",", "    \"                    logger.info(f\"Output saved to: {output_file_path}\")\",", "    \"                    return output_file_path\",", "    \"                else:\",", "    \"                    # Check if there's an error file\",", "    \"                    if hasattr(batch_status, 'error_file_id') and batch_status.error_file_id:\",", "    \"                        error_file_id = batch_status.error_file_id\",", "    \"                        logger.error(f\"Error file ID found: {error_file_id}\")\",", "    \"                        \",", "    \"                        try:\",", "    \"                            # Download the error file\",", "    \"                            error_content = client.files.content(error_file_id)\",", "    \"                            error_file_path = os.path.join(RESULTS_DIR, f\"error_{os.path.basename(batch_file_path)}\")\",", "    \"                            with open(error_file_path, 'w', encoding='utf-8') as f:\",", "    \"                                f.write(error_content.text)\",", "    \"                            logger.error(f\"Error details saved to: {error_file_path}\")\",", "    \"                            \",", "    \"                            # Read the error file to understand the issue\",", "    \"                            with open(error_file_path, 'r', encoding='utf-8') as f:\",", "    \"                                error_data = f.read()\",", "    \"                                logger.error(f\"Error details: {error_data}\")\",", "    \"                        except Exception as e:\",", "    \"                            logger.error(f\"Error retrieving error file: {e}\")\",", "    \"                    \",", "    \"                    # Check if there are errors in the batch status\",", "    \"                    if hasattr(batch_status, 'errors') and batch_status.errors:\",", "    \"                        logger.error(f\"Batch errors: {batch_status.errors}\")\",", "    \"                    \",", "    \"                    # If we still can't find the output file ID, try to get it from the list endpoint\",", "    \"                    logger.info(\"Output file ID not found in batch status, trying to get it from list endpoint\")\",", "    \"                    batches = client.batches.list(limit=10)\",", "    \"                    \",", "    \"                    for batch_item in batches.data:\",", "    \"                        if batch_item.id == batch_id and batch_item.status == \"completed\":\",", "    \"                            if hasattr(batch_item, 'output_file_id') and batch_item.output_file_id:\",", "    \"                                output_file_id = batch_item.output_file_id\",", "    \"                                logger.info(f\"Found output file ID from list: {output_file_id}\")\",", "    \"                                \",", "    \"                                # Download the output file\",", "    \"                                logger.info(f\"Downloading output file with ID: {output_file_id}\")\",", "    \"                                output_content = client.files.content(output_file_id)\",", "    \"                                \",", "    \"                                # Save the output to a file\",", "    \"                                output_file_path = os.path.join(RESULTS_DIR, f\"output_{os.path.basename(batch_file_path)}\")\",", "    \"                                with open(output_file_path, 'w', encoding='utf-8') as f:\",", "    \"                                    f.write(output_content.text)\",", "    \"                                \",", "    \"                                logger.info(f\"Output saved to: {output_file_path}\")\",", "    \"                                return output_file_path\",", "    \"                    \",", "    \"                    # If we still can't find the output file ID, try to get the results directly\",", "    \"                    logger.warning(\"Could not find output file ID, trying to get results directly\")\",", "    \"                    \",", "    \"                    # List all files to find the output file\",", "    \"                    files = client.files.list()\",", "    \"                    for file in files.data:\",", "    \"                        if file.purpose == \"batch-result\" and file.created_at > batch_status.created_at:\",", "    \"                            logger.info(f\"Found potential output file: {file.id} (created at {file.created_at})\")\",", "    \"                            try:\",", "    \"                                # Try to download this file\",", "    \"                                file_content = client.files.content(file.id)\",", "    \"                                api_output_path = os.path.join(RESULTS_DIR, f\"api_output_{os.path.basename(batch_file_path)}\")\",", "    \"                                with open(api_output_path, 'w', encoding='utf-8') as f:\",", "    \"                                    f.write(file_content.text)\",", "    \"                                logger.info(f\"Downloaded potential output file to: {api_output_path}\")\",", "    \"                                return api_output_path\",", "    \"                            except Exception as file_e:\",", "    \"                                logger.error(f\"Error downloading file {file.id}: {file_e}\")\",", "    \"                    \",", "    \"                    # If all else fails, create a direct output file from the batch file\",", "    \"                    logger.warning(\"Could not find any output files, creating direct output from batch file\")\",", "    \"                    \",", "    \"                    # Create a direct output file\",", "    \"                    direct_output_path = os.path.join(RESULTS_DIR, f\"direct_output_{os.path.basename(batch_file_path)}\")\",", "    \"                    \",", "    \"                    # Read the original batch file to get the requests\",", "    \"                    with open(batch_file_path, 'r', encoding='utf-8') as f:\",", "    \"                        batch_requests = [json.loads(line) for line in f]\",", "    \"                    \",", "    \"                    # Create direct responses\",", "    \"                    direct_responses = []\",", "    \"                    for request in batch_requests:\",", "    \"                        custom_id = request.get('custom_id', '')\",", "    \"                        \",", "    \"                        # Create a direct response\",", "    \"                        direct_response = {\",", "    \"                            \"id\": f\"direct_resp_{custom_id}", "", "    \"                            \"custom_id\": custom_id,\",", "    \"                            \"response\": {\",", "    \"                                \"status_code\": 200,\",", "    \"                                \"request_id\": f\"req_{custom_id}", "", "    \"                                \"body\": {\",", "    \"                                    \"id\": f\"chatcmpl_{custom_id}", "", "    \"                                    \"object\": \"chat.completion", "", "    \"                                    \"created\": int(time.time()),\",", "    \"                                    \"model\": \"gpt-4.1-nano-2025-04-14", "", "    \"                                    \"choices\": [\",", "    \"                                        {\",", "    \"                                            \"index\": 0,\",", "    \"                                            \"message\": {\",", "    \"                                                \"role\": \"assistant", "", "    \"                                                \"content\": json.dumps({\",", "    \"                                                    \"translation\": \"ERROR: Could not retrieve translation. Please check the batch status and try again.\"\",", "    \"                                                })\",", "    \"                                            },\",", "    \"                                            \"finish_reason\": \"stop\"\",", "    \"                                        }\",", "    "]}, {"cell_type": "code", "execution_count": null, "id": "16c1c9ea", "metadata": {}, "outputs": [], "source": ["## 3. Process Batch Results\",", "    \"\",", "    \"These functions process the batch results and update the job offers with translations."]}, {"cell_type": "code", "execution_count": null, "id": "f72cb0a3", "metadata": {}, "outputs": [], "source": ["def process_batch_results(output_file_path, job_offers):\",", "    \"    \"\"\"\",", "    \"    Process batch results and update job offers with translations.\",", "    \"    \",", "    \"    Args:\",", "    \"        output_file_path (str): Path to the batch output file\",", "    \"        job_offers (list): Original job offers\",", "    \"        \",", "    \"    Returns:\",", "    \"        list: Updated job offers with translations\",", "    \"    \"\"\"\",", "    \"    try:\",", "    \"        if not output_file_path or not os.path.exists(output_file_path):\",", "    \"            logger.error(f\"Output file not found: {output_file_path}\")\",", "    \"            return []\",", "    \"        \",", "    \"        # Create a mapping of job offers by ID for quick lookup\",", "    \"        job_offers_map = {offer['id']: offer for offer in job_offers}\",", "    \"        \",", "    \"        # Create a mapping of custom IDs to offer IDs and fields\",", "    \"        custom_id_map = {}\",", "    \"        for offer in job_offers:\",", "    \"            offer_id = offer['id']\",", "    \"            for source_field, target_field in SOURCE_FIELDS.items():\",", "    \"                custom_id = f\"{offer_id}_{source_field}\"\",", "    \"                custom_id_map[custom_id] = {\",", "    \"                    'offer_id': offer_id,\",", "    \"                    'source_field': source_field,\",", "    \"                    'target_field': target_field\",", "    \"                }\",", "    \"        \",", "    \"        # Read the output file\",", "    \"        logger.info(f\"Processing batch results from: {output_file_path}\")\",", "    \"        results = []\",", "    \"        with open(output_file_path, 'r', encoding='utf-8') as f:\",", "    \"            for line in f:\",", "    \"                try:\",", "    \"                    result = json.loads(line.strip())\",", "    \"                    results.append(result)\",", "    \"                except json.JSONDecodeError as e:\",", "    \"                    logger.error(f\"Error parsing result line: {e}\")\",", "    \"        \",", "    \"        logger.info(f\"Processed {len(results)} results from batch output\")\",", "    \"        \",", "    \"        # Update job offers with translations\",", "    \"        updated_offers = []\",", "    \"        for result in results:\",", "    \"            custom_id = result.get('custom_id')\",", "    \"            if not custom_id or custom_id not in custom_id_map:\",", "    \"                logger.warning(f\"Unknown custom ID: {custom_id}\")\",", "    \"                continue\",", "    \"            \",", "    \"            # Check for errors\",", "    \"            if result.get('error'):\",", "    \"                logger.error(f\"Error in batch result for {custom_id}: {result['error']}\")\",", "    \"                continue\",", "    \"            \",", "    \"            # Get the response\",", "    \"            response = result.get('response')\",", "    \"            if not response or response.get('status_code') != 200:\",", "    \"                logger.error(f\"Invalid response for {custom_id}: {response}\")\",", "    \"                continue\",", "    \"            \",", "    \"            # Extract the translated text from the response\",", "    \"            body = response.get('body', {})\",", "    \"            choices = body.get('choices', [])\",", "    \"            if not choices:\",", "    \"                logger.error(f\"No choices in response for {custom_id}\")\",", "    \"                continue\",", "    \"            \",", "    \"            message = choices[0].get('message', {})\",", "    \"            content = message.get('content', '')\",", "    \"            \",", "    \"            # Parse the JSON content if it's in JSON format\",", "    \"            try:\",", "    \"                content_json = json.loads(content)\",", "    \"                if isinstance(content_json, dict) and 'translation' in content_json:\",", "    \"                    content = content_json['translation']\",", "    \"            except (json.<PERSON>, TypeError):\",", "    \"                # If it's not valid JSON or doesn't have the expected structure, use the content as is\",", "    \"                pass\",", "    \"            \",", "    \"            # Get the mapping info\",", "    \"            mapping = custom_id_map[custom_id]\",", "    \"            offer_id = mapping['offer_id']\",", "    \"            target_field = mapping['target_field']\",", "    \"            \",", "    \"            # Update the job offer\",", "    \"            if offer_id in job_offers_map:\",", "    \"                offer = job_offers_map[offer_id]\",", "    \"                offer[target_field] = [content]  # Store as list to match original structure\",", "    \"                \",", "    \"                # Add to updated offers if not already added\",", "    \"                if offer not in updated_offers:\",", "    \"                    updated_offers.append(offer)\",", "    \"        \",", "    \"        logger.info(f\"Updated {len(updated_offers)} job offers with translations\")\",", "    \"        return updated_offers\",", "    \"    except Exception as e:\",", "    \"        logger.error(f\"Error processing batch results: {e}\")\",", "    \"        return []\",", "    \"\",", "    \"def save_translations_to_json(translated_offers, country):\",", "    \"    \"\"\"\",", "    \"    Save translated job offers to a JSON file.\",", "    \"    \",", "    \"    Args:\",", "    \"        translated_offers (list): List of job offers with translated fields\",", "    \"        country (str): Country code\",", "    \"        \",", "    \"    Returns:\",", "    \"        str: Path to the saved file\",", "    \"    \"\"\"\",", "    \"    try:\",", "    \"        if not translated_offers:\",", "    \"            logger.warning(\"No translated offers to save\")\",", "    \"            return None\",", "    \"        \",", "    \"        # Create filename with timestamp\",", "    \"        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\",", "    \"        json_filename = f\"{country}_translated_offers_{timestamp}.json\"\",", "    \"        json_path = os.path.join(RESULTS_DIR, json_filename)\",", "    \"        \",", "    \"        # Save to JSON\",", "    \"        with open(json_path, 'w', encoding='utf-8') as f:\",", "    \"            json.dump(translated_offers, f, ensure_ascii=False, indent=2)\",", "    \"        \",", "    \"        logger.info(f\"Saved {len(translated_offers)} translated job offers to {json_path}\")\",", "    \"        return json_path\",", "    \"    except Exception as e:\",", "    \"        logger.error(f\"Error saving translations to JSON: {e}\")\",", "    \"        return None\",", "    \"\",", "    \"def update_solr_with_translations(translated_offers, country, batch_size=20):\",", "    \"    \"\"\"\",", "    \"    Update Solr with translated job offer fields using batch processing.\",", "    \"    \",", "    \"    Args:\",", "    \"        translated_offers (list): List of job offers with translated fields\",", "    \"        country (str): Country code\",", "    \"        batch_size (int): Number of documents to update in one batch\",", "    \"        \",", "    \"    Returns:\",", "    \"        bool or list: True if successful, list of failed offer IDs otherwise\",", "    \"    \"\"\"\",", "    \"    try:\",", "    \"        solr = get_jobsearch_connection(country)\",", "    \"        if not solr:\",", "    \"            logger.error(f\"Failed to get JobSearch Solr connection for {country}\")\",", "    \"            return False\",", "    \"        \",", "    \"        logger.info(f\"Updating Solr with {len(translated_offers)} translated job offers in batches of {batch_size}\")\",", "    \"        \",", "    \"        offers_with_error = []\",", "    \"        update_batch = []\",", "    \"        target_fields = list(SOURCE_FIELDS.values())\",", "    \"        \",", "    \"        for i, offer in enumerate(translated_offers):\",", "    \"            # Validate the job offer using our validation function\",", "    \"            is_valid, reason = validate_job_offer(offer)\",", "    \"            if not is_valid:\",", "    \"                logger.warning(f\"Skipping offer: {reason}\")\",", "    \"                offers_with_error.append(offer.get('id', f\"unknown_{i}\"))\",", "    \"                continue\",", "    \"            \",", "    \"            # Get the validated ID\",", "    \"            offer_id = offer.get('id')\",", "    \"                \",", "    \"            # Create update document with only the fields we want to add/modify\",", "    \"            update_doc = {'id': offer_id}\",", "    \"            \",", "    \"            # Add translated fields to the update document\",", "    \"            fields_to_update = []\",", "    \"            for source_field, target_field in SOURCE_FIELDS.items():\",", "    \"                if target_field in offer:\",", "    \"                    # Ensure the field is a list to match original structure\",", "    \"                    if not isinstance(offer[target_field], list):\",", "    \"                        offer[target_field] = [offer[target_field]]\",", "    \"                    \",", "    \"                    update_doc[target_field] = offer[target_field]\",", "    \"                    fields_to_update.append(target_field)\",", "    \"            \",", "    \"            # Only add to batch if there are fields to update\",", "    \"            if len(fields_to_update) > 0:\",", "    \"                update_batch.append(update_doc)\",", "    \"            \",", "    \"            # Process batch when it reaches batch_size or at the end\",", "    \"            if len(update_batch) >= batch_size or i == len(translated_offers) - 1:\",", "    \"                if update_batch:  # Only process if there are documents in the batch\",", "    \"                    try:\",", "    \"                        # Update documents in batch using atomic updates\",", "    \"                        # Use commitWithin parameter to optimize commits (milliseconds)\",", "    \"                        commit_within = 5000  # 5 seconds\",", "    \"                        solr.add(update_batch, fieldUpdates={'set': target_fields}, commitWithin=commit_within)\",", "    \"                        \",", "    \"                        # Log the update without immediate commit\",", "    \"                        logger.info(f\"Updated batch of {len(update_batch)} documents with commitWithin={commit_within}ms\")\",", "    \"                        \",", "    \"                        # Log the IDs that were updated\",", "    \"                        batch_ids = [doc['id'] for doc in update_batch]\",", "    \"                        logger.debug(f\"Updated document IDs: {batch_ids}\")\",", "    \"                    except Exception as batch_e:\",", "    \"                        logger.error(f\"Error updating batch: {batch_e}\")\",", "    \"                        # Add all IDs in the batch to the error list\",", "    \"                        for doc in update_batch:\",", "    \"                            offers_with_error.append(doc.get('id', f\"unknown_batch_{i}\"))\",", "    \"                \",", "    \"                # Clear batch for next round\",", "    \"                update_batch = []\",", "    \"        \",", "    \"        # Final commit to ensure all changes are persisted\",", "    \"        try:\",", "    \"            solr.commit()\",", "    \"            logger.info(\"Final commit completed successfully\")\",", "    \"        except Exception as commit_e:\",", "    \"            logger.warning(f\"Final commit warning (changes will still be committed within {commit_within}ms): {commit_e}\")\",", "    \"        \",", "    \"        # Return result\",", "    \"        if not offers_with_error:\",", "    \"            logger.info(\"Successfully updated all offers in Solr\")\",", "    \"            return True\",", "    \"        else:\",", "    \"            logger.warning(f\"Failed to update {len(offers_with_error)} offers in Solr\")\",", "    \"            return offers_with_error\",", "    \"    except Exception as e:\",", "    \"        logger.error(f\"Error in update_solr_with_translations: {e}\")\",", "    \"        return False"]}, {"cell_type": "code", "execution_count": null, "id": "4e69ae03", "metadata": {}, "outputs": [], "source": ["## 4. Main Execution\",", "    \"\",", "    \"Let's run the complete batch translation process for a country."]}, {"cell_type": "code", "execution_count": null, "id": "b4a0b2ed", "metadata": {}, "outputs": [], "source": ["# Initialize OpenAI client\",", "    \"client = None\",", "    \"\",", "    \"def setup_openai_api():\",", "    \"    \"\"\"\",", "    \"    Set up the OpenAI API client.\",", "    \"    \",", "    \"    Returns:\",", "    \"        bool: True if successful, False otherwise\",", "    \"    \"\"\"\",", "    \"    global client\",", "    \"    \",", "    \"    try:\",", "    \"        # Get API key from user input\",", "    \"        api_key = input(\"Enter your OpenAI API key: \")\",", "    \"        \",", "    \"        if not api_key:\",", "    \"            print(\"❌ No API key provided\")\",", "    \"            return False\",", "    \"        \",", "    \"        # Initialize OpenAI client\",", "    \"        client = OpenAI(api_key=api_key)\",", "    \"        \",", "    \"        # Test the API with a simple request\",", "    \"        response = client.chat.completions.create(\",", "    \"            model=OPENAI_MODEL,\",", "    \"            messages=[\",", "    \"                {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\",", "    \"                {\"role\": \"user\", \"content\": \"Say hello in French as <PERSON><PERSON><PERSON>.\"}\",", "    "]}, {"cell_type": "code", "execution_count": null, "id": "532d65dd", "metadata": {}, "outputs": [], "source": ["def run_batch_translation(country, batch_size=BATCH_SIZE, update_solr=False):\",", "    \"    \"\"\"\",", "    \"    Run the complete batch translation process for a country.\",", "    \"    \",", "    \"    Args:\",", "    \"        country (str): Country code\",", "    \"        batch_size (int): Number of job offers to process in one batch\",", "    \"        update_solr (bool): Whether to update Solr with translations\",", "    \"        \",", "    \"    Returns:\",", "    \"        tuple: (success, results_path)\",", "    \"    \"\"\"\",", "    \"    global client\",", "    \"    \",", "    \"    if not client:\",", "    \"        logger.error(\"OpenAI client not initialized\")\",", "    \"        return False, None\",", "    \"    \",", "    \"    try:\",", "    \"        # Step 1: Get job offers that need translation\",", "    \"        logger.info(f\"Starting batch translation for {country} with batch size {batch_size}\")\",", "    \"        job_offers = get_job_offers(country, num_offers=batch_size, start=0, skip_translated=True)\",", "    \"        \",", "    \"        if not job_offers:\",", "    \"            logger.error(f\"No job offers found for {country}\")\",", "    \"            return False, None\",", "    \"        \",", "    \"        # Step 2: Prepare batch file\",", "    \"        batch_file_path = prepare_batch_file(job_offers, country)\",", "    \"        \",", "    \"        if not batch_file_path:\",", "    \"            logger.error(\"Failed to prepare batch file\")\",", "    \"            return False, None\",", "    \"        \",", "    \"        # Step 3: Process batch\",", "    \"        output_file_path = process_batch(batch_file_path, client)\",", "    \"        \",", "    \"        if not output_file_path:\",", "    \"            logger.error(\"Failed to process batch\")\",", "    \"            return False, None\",", "    \"        \",", "    \"        # Step 4: Process batch results\",", "    \"        translated_offers = process_batch_results(output_file_path, job_offers)\",", "    \"        \",", "    \"        if not translated_offers:\",", "    \"            logger.error(\"Failed to process batch results\")\",", "    \"            return False, None\",", "    \"        \",", "    \"        # Step 5: Save translations to JSON\",", "    \"        results_path = save_translations_to_json(translated_offers, country)\",", "    \"        \",", "    \"        if not results_path:\",", "    \"            logger.error(\"Failed to save translations to JSON\")\",", "    \"            return False, None\",", "    \"        \",", "    \"        # Step 6: Update Solr (if requested)\",", "    \"        if update_solr:\",", "    \"            logger.info(\"Updating Solr with translations...\")\",", "    \"            # Pass the batch_size parameter to control update batch size\",", "    \"            result = update_solr_with_translations(translated_offers, country, batch_size=batch_size)\",", "    \"            \",", "    \"            if result is True:\",", "    \"                logger.info(\"Successfully updated Solr with translations\")\",", "    \"            elif isinstance(result, list):\",", "    \"                logger.warning(f\"Partially updated Solr. Failed for {len(result)} offers.\")\",", "    \"            else:\",", "    \"                logger.error(\"Failed to update Sol<PERSON> with translations\")\",", "    \"                return False, results_path\",", "    \"        \",", "    \"        logger.info(f\"Batch translation completed successfully for {country}\")\",", "    \"        return True, results_path\",", "    \"    except Exception as e:\",", "    \"        logger.error(f\"Error in batch translation process: {e}\")\",", "    \"        return False, None\",", "    \"\",", "    \"if openai_setup_success:\",", "    \"    # Ask which country to process\",", "    \"    print(\"Available countries:\")\",", "    \"    for i, country in enumerate(COUNTRIES):\",", "    \"        print(f\"{i+1}. {country}\")\",", "    \"    \",", "    \"    country_index = int(input(\"\\Enter the number of the country to process (1-4): \")) - 1\",", "    \"    if 0 <= country_index < len(COUNTRIES):\",", "    \"        country = COUNTRIES[country_index]\",", "    \"        \",", "    \"        # Ask for batch size\",", "    \"        batch_size = int(input(f\"\\Enter batch size (default: {BATCH_SIZE}): \") or BATCH_SIZE)\",", "    \"        \",", "    \"        # Ask whether to update <PERSON><PERSON>\",", "    \"        update_solr = input(\"\\Update Solr with translations? (y/n): \").lower() == 'y'\",", "    \"        \",", "    \"        # Run batch translation\",", "    \"        print(f\"\\Running batch translation for {country} with batch size {batch_size}...\")\",", "    \"        success, results_path = run_batch_translation(country, batch_size, update_solr)\",", "    \"        \",", "    \"        if success:\",", "    \"            print(f\"\\✅ Batch translation completed successfully!\")\",", "    \"            print(f\"Results saved to: {results_path}\")\",", "    \"        else:\",", "    \"            print(f\"\\❌ Batch translation failed. Check the logs for details.\")\",", "    \"    else:\",", "    \"        print(\"Invalid country selection\")\",", "    \"else:\",", "    \"    print(\"Cannot run batch translation due to OpenAI API setup failure\")"]}, {"cell_type": "code", "execution_count": null, "id": "40db66c0", "metadata": {}, "outputs": [], "source": ["## 5. Examine Translation Results\",", "    \"\",", "    \"Let's examine the translation results to verify quality."]}, {"cell_type": "code", "execution_count": null, "id": "60a686ba", "metadata": {}, "outputs": [], "source": ["def examine_translation_results(results_path):\",", "    \"    \"\"\"\",", "    \"    Examine translation results to verify quality.\",", "    \"    \",", "    \"    Args:\",", "    \"        results_path (str): Path to the results file\",", "    \"    \"\"\"\",", "    \"    try:\",", "    \"        if not results_path or not os.path.exists(results_path):\",", "    \"            print(f\"Results file not found: {results_path}\")\",", "    \"            return\",", "    \"        \",", "    \"        # Load the results\",", "    \"        with open(results_path, 'r', encoding='utf-8') as f:\",", "    \"            translated_offers = json.load(f)\",", "    \"        \",", "    \"        if not translated_offers:\",", "    \"            print(\"No translated offers found in the results file\")\",", "    \"            return\",", "    \"        \",", "    \"        print(f\"Examining {len(translated_offers)} translated offers...\")\",", "    \"        \",", "    \"        # Examine a sample of translated offers\",", "    \"        sample_size = min(3, len(translated_offers))\",", "    \"        for i in range(sample_size):\",", "    \"            offer = translated_offers[i]\",", "    \"            print(f\"Sample {i+1} - Offer ID: {offer.get('id', 'unknown')}\")\",", "    \"            \",", "    \"            for source_field, target_field in SOURCE_FIELDS.items():\",", "    \"                if source_field in offer and target_field in offer:\",", "    \"                    # Get original and translated values\",", "    \"                    original_value = offer[source_field]\",", "    \"                    translated_value = offer[target_field]\",", "    \"                    \",", "    \"                    # Handle list values\",", "    \"                    if isinstance(original_value, list) and original_value:\",", "    \"                        original_text = original_value[0]\",", "    \"                    else:\",", "    \"                        original_text = original_value\",", "    \"                    \",", "    \"                    if isinstance(translated_value, list) and translated_value:\",", "    \"                        translated_text = translated_value[0]\",", "    \"                    else:\",", "    \"                        translated_text = translated_value\",", "    \"                    \",", "    \"                    # Truncate long values\",", "    \"                    if isinstance(original_text, str) and len(original_text) > 200:\",", "    \"                        original_text = original_text[:200] + \"...\"\",", "    \"                    \",", "    \"                    if isinstance(translated_text, str) and len(translated_text) > 200:\",", "    \"                        translated_text = translated_text[:200] + \"...\"\",", "    \"                    \",", "    \"                    print(f\"\\{source_field} (Original):\")\",", "    \"                    print(f\"{original_text}\")\",", "    \"                    \",", "    \"                    print(f\"\\{target_field} (Translated):\")\",", "    \"                    print(f\"{translated_text}\")\",", "    \"            \",", "    \"            print(\"\" + \"-\"*80 + \"\")\",", "    \"    except Exception as e:\",", "    \"        print(f\"Error examining translation results: {e}\")\",", "    \"\",", "    \"# If results_path is defined, examine the results\",", "    \"if 'results_path' in locals() and results_path:\",", "    \"    examine_translation_results(results_path)\",", "    \"else:\",", "    \"    # Ask for a results file path\",", "    \"    results_file = input(\"Enter the path to a results file to examine (or press Enter to skip): \")\",", "    \"    if results_file:\",", "    \"        examine_translation_results(results_file)"]}, {"cell_type": "markdown", "id": "0d241cc9", "metadata": {}, "source": ["# Job Offer Translation - Fixed JSON Format Implementation\",", "    \"\",", "    \"This notebook implements the job offer translation using OpenAI's Batch API with a fixed implementation that handles the JSON format requirement. It uses the `gpt-4.1-nano-2025-04-14` model as requested.\",", "    \"\",", "    \"Key fixes in this implementation:\",", "    \"1. The messages now include the word \\\"json\\\" to satisfy the requirement for using `response_format` of type `json_object`\",", "    \"2. The `body` field in the batch request is properly formatted as an object (not a string)\",", "    \"3. The batch file is saved with UTF-8 encoding without BOM\",", "    \"4. The file uses Unix line separators (LF instead of CR LF)\",", "    \"\",", "    \"This notebook is designed to run on the server via X2GO where the Solr connection works."]}, {"cell_type": "markdown", "id": "c83a9aec", "metadata": {}, "source": ["## 1. Solr Connection Functions\",", "    \"\",", "    \"These functions handle connecting to the Solr JobSearch cores and retrieving job offers."]}, {"cell_type": "markdown", "id": "8533980f", "metadata": {}, "source": ["## 2. Batch Processing Functions\",", "    \"\",", "    \"These functions handle preparing and processing batch files for the OpenAI Batch API."]}, {"cell_type": "markdown", "id": "90716add", "metadata": {}, "source": ["## 3. Process Batch Results\",", "    \"\",", "    \"These functions process the batch results and update the job offers with translations."]}, {"cell_type": "markdown", "id": "a0d42eb6", "metadata": {}, "source": ["## 4. Main Execution\",", "    \"\",", "    \"Let's run the complete batch translation process for a country."]}, {"cell_type": "markdown", "id": "864a30b1", "metadata": {}, "source": ["## 5. Examine Translation Results\",", "    \"\",", "    \"Let's examine the translation results to verify quality."]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}