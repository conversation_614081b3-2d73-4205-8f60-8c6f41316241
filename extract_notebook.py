import re
import json
from pathlib import Path

def extract_notebook_cells(file_path):
    """Extract code and markdown cells from a potentially corrupted notebook"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract code cells
    code_pattern = r'"source": \[\s*"(.*?)"\s*\]'
    code_cells = re.findall(code_pattern, content, re.DOTALL)
    
    # Extract markdown cells
    markdown_pattern = r'"cell_type": "markdown".*?"source": \[\s*"(.*?)"\s*\]'
    markdown_cells = re.findall(markdown_pattern, content, re.DOTALL)
    
    return code_cells, markdown_cells

def create_new_notebook(code_cells, markdown_cells, output_path):
    """Create a new notebook with the extracted cells"""
    notebook = {
        "cells": [],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "codemirror_mode": {
                    "name": "ipython",
                    "version": 3
                },
                "file_extension": ".py",
                "mimetype": "text/x-python",
                "name": "python",
                "nbconvert_exporter": "python",
                "pygments_lexer": "ipython3",
                "version": "3.8.10"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 5
    }
    
    # Add code cells
    for i, code in enumerate(code_cells):
        notebook["cells"].append({
            "cell_type": "code",
            "execution_count": None,
            "id": f"code_{i}",
            "metadata": {},
            "outputs": [],
            "source": code.split('\\n')
        })
    
    # Add markdown cells
    for i, markdown in enumerate(markdown_cells):
        notebook["cells"].append({
            "cell_type": "markdown",
            "id": f"markdown_{i}",
            "metadata": {},
            "source": markdown.split('\\n')
        })
    
    # Write the notebook
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)

try:
    # Extract cells from the corrupted notebook
    print("Extracting cells from the notebook...")
    code_cells, markdown_cells = extract_notebook_cells('JOB_OFFER_TRANSLATION_JSON_FORMAT.ipynb')
    print(f"Extracted {len(code_cells)} code cells and {len(markdown_cells)} markdown cells")
    
    # Create a new notebook
    print("Creating a new notebook...")
    create_new_notebook(code_cells, markdown_cells, 'JOB_OFFER_TRANSLATION_JSON_FORMAT_new.ipynb')
    print("Successfully created a new notebook")
except Exception as e:
    print(f"Error: {e}")
