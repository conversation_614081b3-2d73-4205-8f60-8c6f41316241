import json
import requests
from tqdm import tqdm
 
centrafrique_url = "http://************:8983/solr/core_cvsearch_centrafrique/select?q=*%3A*&rows=1000000&fl=entity_id&wt=json&indent=true"
ids_list = json.loads(requests.get(centrafrique_url).text)['response']['docs']
 
TRANS_API = f"http://translator.dev.emploi.ma/translate_candidate"
 
for element in tqdm(ids_list):
    try:
        nid = element['entity_id']
        country = 'centrafrique'
        trans_api_call = f"{TRANS_API}?user_nid={nid}&country={country}"
        response = requests.get(trans_api_call).text
        json.loads(response)
        # print(nid)
    except:
        pass
 
countries = ['algerie', 'benin', 'burkina', 'burundi', 'cameroun', 'centrafrique', 'congo', 'cote_d_ivoire', 'guinee',
    #             'gabon', 'mali', 'mauritanie', 'maroc', 'niger', 'rdc', 'senegal', 'tchad', 'togo', 'tunisie']
 

not that fileds are not existing but rather we didn't search in the right place because all they exist in the url like the following (http://************:8983/solr/#/~cores/core_jobsearch_burkina) so please study that so you can get too smart about it 

what found is completly logique because the tr_ fields are the new fields that would be containing the english version 'trnslated version'
here's the summary :
[JOBSEARCH CORE EXPLORATION SUMMARY
=================================

1. JobSearch Core Connectivity:
   - Successful: 4/4 countries
   - Successful countries: algerie, centrafrique, benin, burkina

2. Field Existence Analysis:
   algerie:
     - sm_field_offre_description_poste: 100.0% exist, 100.0% non-empty
     - sm_field_offre_profil: 100.0% exist, 100.0% non-empty
     - tr_field_offre_description_poste: 0.0% exist, 0.0% non-empty
     - tr_field_offre_profil: 0.0% exist, 0.0% non-empty
   centrafrique:
     - sm_field_offre_description_poste: 100.0% exist, 100.0% non-empty
     - sm_field_offre_profil: 100.0% exist, 100.0% non-empty
     - tr_field_offre_description_poste: 0.0% exist, 0.0% non-empty
     - tr_field_offre_profil: 0.0% exist, 0.0% non-empty
   benin:
     - sm_field_offre_description_poste: 100.0% exist, 100.0% non-empty
     - sm_field_offre_profil: 100.0% exist, 100.0% non-empty
     - tr_field_offre_description_poste: 0.0% exist, 0.0% non-empty
     - tr_field_offre_profil: 0.0% exist, 0.0% non-empty
   burkina:
     - sm_field_offre_description_poste: 100.0% exist, 100.0% non-empty
     - sm_field_offre_profil: 100.0% exist, 100.0% non-empty
     - tr_field_offre_description_poste: 0.0% exist, 0.0% non-empty
     - tr_field_offre_profil: 0.0% exist, 0.0% non-empty

CONCLUSION:
✅ The fields we're looking for exist in the JobSearch cores!
We can proceed with implementing the translation pipeline using these fields.]


well let's focus right now very well on the understanding the data stucture very well maybe it would be and then we can pass to other ascpect 
- 












2025-05-13 12:38:38,985 - INFO - Connecting to JobSearch Solr core at http://************:8983/solr/core_jobsearch_algerie
2025-05-13 12:38:38,986 - INFO - Retrieving 20 sample documents from JobSearch core for algerie
2025-05-13 12:38:39,036 - INFO - Finished 'http://************:8983/solr/core_jobsearch_algerie/select/?q=%2A%3A%2A&rows=20&wt=json' (get) with body '' in 0.049 seconds, with status 200
2025-05-13 12:38:39,038 - INFO - Retrieved 20 sample documents from JobSearch core for algerie
2025-05-13 12:38:39,040 - INFO - Connecting to JobSearch Solr core at http://************:8983/solr/core_jobsearch_centrafrique
2025-05-13 12:38:39,041 - INFO - Retrieving 20 sample documents from JobSearch core for centrafrique
2025-05-13 12:38:39,090 - INFO - Finished 'http://************:8983/solr/core_jobsearch_centrafrique/select/?q=%2A%3A%2A&rows=20&wt=json' (get) with body '' in 0.048 seconds, with status 200
2025-05-13 12:38:39,092 - INFO - Retrieved 20 sample documents from JobSearch core for centrafrique
2025-05-13 12:38:39,094 - INFO - Connecting to JobSearch Solr core at http://************:8983/solr/core_jobsearch_benin
2025-05-13 12:38:39,095 - INFO - Retrieving 20 sample documents from JobSearch core for benin
2025-05-13 12:38:39,152 - INFO - Finished 'http://************:8983/solr/core_jobsearch_benin/select/?q=%2A%3A%2A&rows=20&wt=json' (get) with body '' in 0.057 seconds, with status 200
2025-05-13 12:38:39,154 - INFO - Retrieved 20 sample documents from JobSearch core for benin
2025-05-13 12:38:39,157 - INFO - Connecting to JobSearch Solr core at http://************:8983/solr/core_jobsearch_burkina
2025-05-13 12:38:39,158 - INFO - Retrieving 20 sample documents from JobSearch core for burkina
Analyzing text formatting across all countries...

Analyzing text formatting for algerie...
  ✅ Retrieved 20 sample documents

  sm_field_offre_description_poste:
    - Exists in: 20/20 documents (100.0%)
    - Documents with bullet points: 8/20 (40.0%)
    - Documents with multiple paragraphs: 0/20 (0.0%)
    - Documents with HTML tags: 0/20 (0.0%)
    - Documents with line breaks: 0/20 (0.0%)

  sm_field_offre_profil:
    - Exists in: 20/20 documents (100.0%)
    - Documents with bullet points: 3/20 (15.0%)
    - Documents with multiple paragraphs: 0/20 (0.0%)
    - Documents with HTML tags: 0/20 (0.0%)
    - Documents with line breaks: 0/20 (0.0%)

Analyzing text formatting for centrafrique...
  ✅ Retrieved 20 sample documents

  sm_field_offre_description_poste:
    - Exists in: 20/20 documents (100.0%)
    - Documents with bullet points: 13/20 (65.0%)
    - Documents with multiple paragraphs: 0/20 (0.0%)
    - Documents with HTML tags: 0/20 (0.0%)
    - Documents with line breaks: 0/20 (0.0%)

  sm_field_offre_profil:
    - Exists in: 20/20 documents (100.0%)
    - Documents with bullet points: 11/20 (55.0%)
    - Documents with multiple paragraphs: 0/20 (0.0%)
    - Documents with HTML tags: 0/20 (0.0%)
    - Documents with line breaks: 0/20 (0.0%)

Analyzing text formatting for benin...
  ✅ Retrieved 20 sample documents

  sm_field_offre_description_poste:
    - Exists in: 20/20 documents (100.0%)
    - Documents with bullet points: 5/20 (25.0%)
    - Documents with multiple paragraphs: 0/20 (0.0%)
    - Documents with HTML tags: 0/20 (0.0%)
    - Documents with line breaks: 0/20 (0.0%)

  sm_field_offre_profil:
    - Exists in: 20/20 documents (100.0%)
    - Documents with bullet points: 8/20 (40.0%)
    - Documents with multiple paragraphs: 0/20 (0.0%)
    - Documents with HTML tags: 0/20 (0.0%)
    - Documents with line breaks: 0/20 (0.0%)

Analyzing text formatting for burkina...
2025-05-13 12:38:39,206 - INFO - Finished 'http://************:8983/solr/core_jobsearch_burkina/select/?q=%2A%3A%2A&rows=20&wt=json' (get) with body '' in 0.047 seconds, with status 200
2025-05-13 12:38:39,209 - INFO - Retrieved 20 sample documents from JobSearch core for burkina
  ✅ Retrieved 20 sample documents

  sm_field_offre_description_poste:
    - Exists in: 20/20 documents (100.0%)
    - Documents with bullet points: 5/20 (25.0%)
    - Documents with multiple paragraphs: 0/20 (0.0%)
    - Documents with HTML tags: 0/20 (0.0%)
    - Documents with line breaks: 0/20 (0.0%)

  sm_field_offre_profil:
    - Exists in: 20/20 documents (100.0%)
    - Documents with bullet points: 10/20 (50.0%)
    - Documents with multiple paragraphs: 0/20 (0.0%)
    - Documents with HTML tags: 0/20 (0.0%)
    - Documents with line breaks: 0/20 (0.0%)




      ✅ Retrieved 20 sample documents

  sm_field_offre_description_poste:
    - Exists in: 20/20 documents (100.0%)
    - Documents with HTML entities: 16/20 (80.0%)
    - Total HTML entities: 36
    - Unique HTML entities: 1
    - Most common HTML entities:
      &#039; (36 occurrences) → '''

  sm_field_offre_profil:
    - Exists in: 20/20 documents (100.0%)
    - Documents with HTML entities: 15/20 (75.0%)
    - Total HTML entities: 49
    - Unique HTML entities: 1
    - Most common HTML entities:
      &#039; (49 occurrences) → '''

Analyzing HTML entities for centrafrique...
  ✅ Retrieved 20 sample documents

  sm_field_offre_description_poste:
    - Exists in: 20/20 documents (100.0%)
    - Documents with HTML entities: 19/20 (95.0%)
    - Total HTML entities: 68
    - Unique HTML entities: 1
    - Most common HTML entities:
      &#039; (68 occurrences) → '''

  sm_field_offre_profil:
    - Exists in: 20/20 documents (100.0%)
    - Documents with HTML entities: 17/20 (85.0%)
    - Total HTML entities: 28
    - Unique HTML entities: 1
    - Most common HTML entities:
      &#039; (28 occurrences) → '''

Analyzing HTML entities for benin...
  ✅ Retrieved 20 sample documents

  sm_field_offre_description_poste:
    - Exists in: 20/20 documents (100.0%)
    - Documents with HTML entities: 8/20 (40.0%)
    - Total HTML entities: 23
    - Unique HTML entities: 2
    - Most common HTML entities:
      &#039; (22 occurrences) → '''
      &amp; (1 occurrences) → '&'

  sm_field_offre_profil:
    - Exists in: 20/20 documents (100.0%)
    - Documents with HTML entities: 5/20 (25.0%)
    - Total HTML entities: 11
    - Unique HTML entities: 1
    - Most common HTML entities:
      &#039; (11 occurrences) → '''

Analyzing HTML entities for burkina...
  ✅ Retrieved 20 sample documents

  sm_field_offre_description_poste:
    - Exists in: 20/20 documents (100.0%)
    - Documents with HTML entities: 12/20 (60.0%)
    - Total HTML entities: 26
    - Unique HTML entities: 1
    - Most common HTML entities:
      &#039; (26 occurrences) → '''

  sm_field_offre_profil:
    - Exists in: 20/20 documents (100.0%)
    - Documents with HTML entities: 18/20 (90.0%)
    - Total HTML entities: 55
    - Unique HTML entities: 1
    - Most common HTML entities:
      &#039; (55 occurrences) → '''















