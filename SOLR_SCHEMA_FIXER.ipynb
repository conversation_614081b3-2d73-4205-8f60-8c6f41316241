{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON> Schema <PERSON>r - Translation Fields\n", "\n", "Based on the debug results, all update attempts are failing because the translation fields don't exist in the Solr schema.\n", "This notebook will:\n", "\n", "1. **Investigate the current schema**\n", "2. **Check if translation fields exist**\n", "3. **Add missing translation fields**\n", "4. **Test field creation**\n", "5. **Verify updates work after schema fix**\n", "\n", "## Debug Results Summary\n", "- ✅ Connectivity: GOOD (115,132 documents found)\n", "- ✅ Document exists: c8xu1x/node/157221\n", "- ✅ Source fields present: sm_field_offre_description_poste, sm_field_offre_profil\n", "- ❌ **ALL update approaches failed** - fields not found after update\n", "- ❌ Translation fields missing: tr_field_offre_description_poste, tr_field_offre_profil\n", "\n", "## Root Cause\n", "**Schema Issue**: The translation fields are not defined in the Solr schema, so updates are silently ignored."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import requests\n", "import logging\n", "import time\n", "from datetime import datetime\n", "import os\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('schema_fixer')\n", "\n", "# Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "COUNTRY = \"maroc\"\n", "CORE_NAME = f\"core_jobsearch_{COUNTRY}\"\n", "SOLR_CORE_URL = f\"{SOLR_BASE_URL}{CORE_NAME}\"\n", "SCHEMA_URL = f\"{SOLR_CORE_URL}/schema\"\n", "UPDATE_URL = f\"{SOLR_CORE_URL}/update\"\n", "SELECT_URL = f\"{SOLR_CORE_URL}/select\"\n", "\n", "# Test document\n", "TEST_DOC_ID = \"c8xu1x/node/157221\"\n", "\n", "# Translation fields we need\n", "TRANSLATION_FIELDS = {\n", "    \"tr_field_offre_description_poste\": \"text_general\",\n", "    \"tr_field_offre_profil\": \"text_general\"\n", "}\n", "\n", "# Create results directory\n", "RESULTS_DIR = \"schema_fix_results\"\n", "os.makedirs(RESULTS_DIR, exist_ok=True)\n", "\n", "print(\"🔧 Solr Schema Fixer Initialized\")\n", "print(f\"Core: {CORE_NAME}\")\n", "print(f\"Schema URL: {SCHEMA_URL}\")\n", "print(f\"Translation fields to add: {list(TRANSLATION_FIELDS.keys())}\")\n", "print(f\"Test document: {TEST_DOC_ID}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Investigate Current Schema"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def investigate_schema():\n", "    \"\"\"\n", "    Investigate the current Solr schema to understand field definitions.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 STEP 1: INVESTIGATING CURRENT SCHEMA\")\n", "    print(\"=\"*60)\n", "    \n", "    schema_info = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'existing_fields': {},\n", "        'field_types': {},\n", "        'translation_fields_exist': {},\n", "        'source_fields_info': {}\n", "    }\n", "    \n", "    try:\n", "        # Get schema information\n", "        print(\"📋 Fetching schema information...\")\n", "        response = requests.get(f\"{SCHEMA_URL}/fields\", params={'wt': 'json'}, timeout=30)\n", "        \n", "        if response.status_code == 200:\n", "            schema_data = response.json()\n", "            fields = schema_data.get('fields', [])\n", "            \n", "            print(f\"✅ Schema retrieved: {len(fields)} fields found\")\n", "            \n", "            # Check for source fields (sm_field_*)\n", "            source_fields = [f for f in fields if f['name'].startswith('sm_field_offre_')]\n", "            print(f\"\\n📝 Source fields found: {len(source_fields)}\")\n", "            \n", "            for field in source_fields:\n", "                field_name = field['name']\n", "                field_type = field.get('type', 'unknown')\n", "                is_multivalued = field.get('multiValued', False)\n", "                print(f\"  📄 {field_name}: type={field_type}, multiValued={is_multivalued}\")\n", "                schema_info['source_fields_info'][field_name] = field\n", "            \n", "            # Check for translation fields (tr_field_*)\n", "            translation_fields = [f for f in fields if f['name'].startswith('tr_field_offre_')]\n", "            print(f\"\\n🔄 Translation fields found: {len(translation_fields)}\")\n", "            \n", "            if translation_fields:\n", "                for field in translation_fields:\n", "                    field_name = field['name']\n", "                    field_type = field.get('type', 'unknown')\n", "                    is_multivalued = field.get('multiValued', False)\n", "                    print(f\"  ✅ {field_name}: type={field_type}, multiValued={is_multivalued}\")\n", "                    schema_info['translation_fields_exist'][field_name] = field\n", "            else:\n", "                print(\"  ❌ No translation fields found in schema\")\n", "            \n", "            # Check which translation fields we need to add\n", "            existing_translation_field_names = [f['name'] for f in translation_fields]\n", "            missing_fields = []\n", "            \n", "            for field_name in TRANSLATION_FIELDS.keys():\n", "                if field_name in existing_translation_field_names:\n", "                    print(f\"  ✅ {field_name}: EXISTS\")\n", "                    schema_info['translation_fields_exist'][field_name] = True\n", "                else:\n", "                    print(f\"  ❌ {field_name}: MISSING\")\n", "                    missing_fields.append(field_name)\n", "                    schema_info['translation_fields_exist'][field_name] = False\n", "            \n", "            schema_info['missing_fields'] = missing_fields\n", "            \n", "            # Store all fields for reference\n", "            for field in fields:\n", "                schema_info['existing_fields'][field['name']] = field\n", "            \n", "            print(f\"\\n📊 Summary:\")\n", "            print(f\"  Total fields in schema: {len(fields)}\")\n", "            print(f\"  Source fields: {len(source_fields)}\")\n", "            print(f\"  Existing translation fields: {len(translation_fields)}\")\n", "            print(f\"  Missing translation fields: {len(missing_fields)}\")\n", "            \n", "            if missing_fields:\n", "                print(f\"\\n🎯 Fields to add: {missing_fields}\")\n", "            else:\n", "                print(f\"\\n✅ All translation fields already exist!\")\n", "                \n", "        else:\n", "            print(f\"❌ Failed to retrieve schema: {response.status_code}\")\n", "            print(f\"Response: {response.text}\")\n", "            schema_info['error'] = f\"HTTP {response.status_code}: {response.text}\"\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error investigating schema: {e}\")\n", "        schema_info['error'] = str(e)\n", "    \n", "    return schema_info\n", "\n", "# Run schema investigation\n", "schema_info = investigate_schema()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Add Missing Translation Fields"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def add_translation_fields(missing_fields):\n", "    \"\"\"\n", "    Add missing translation fields to the Solr schema.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔧 STEP 2: ADDING MISSING TRANSLATION FIELDS\")\n", "    print(\"=\"*60)\n", "    \n", "    if not missing_fields:\n", "        print(\"✅ No fields to add - all translation fields already exist!\")\n", "        return {'success': True, 'message': 'No fields needed'}\n", "    \n", "    results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'fields_to_add': missing_fields,\n", "        'added_fields': [],\n", "        'failed_fields': [],\n", "        'success': <PERSON><PERSON><PERSON>\n", "    }\n", "    \n", "    print(f\"🎯 Adding {len(missing_fields)} missing fields...\")\n", "    \n", "    for field_name in missing_fields:\n", "        print(f\"\\n📝 Adding field: {field_name}\")\n", "        \n", "        try:\n", "            # Define field based on source field characteristics\n", "            field_definition = {\n", "                \"name\": field_name,\n", "                \"type\": \"text_general\",  # Use same type as source fields\n", "                \"multiValued\": True,     # Match source field format\n", "                \"stored\": True,\n", "                \"indexed\": True\n", "            }\n", "            \n", "            print(f\"📄 Field definition: {json.dumps(field_definition, indent=2)}\")\n", "            \n", "            # Add field to schema\n", "            add_field_url = f\"{SCHEMA_URL}/fields\"\n", "            headers = {'Content-Type': 'application/json'}\n", "            \n", "            response = requests.post(\n", "                add_field_url,\n", "                headers=headers,\n", "                data=json.dumps(field_definition),\n", "                timeout=30\n", "            )\n", "            \n", "            print(f\"📡 Response status: {response.status_code}\")\n", "            print(f\"📡 Response text: {response.text}\")\n", "            \n", "            if response.status_code == 200:\n", "                response_data = response.json()\n", "                if response_data.get('responseHeader', {}).get('status') == 0:\n", "                    print(f\"✅ Field {field_name} added successfully!\")\n", "                    results['added_fields'].append(field_name)\n", "                else:\n", "                    print(f\"❌ Solr returned error for {field_name}: {response_data}\")\n", "                    results['failed_fields'].append({'field': field_name, 'error': response_data})\n", "            else:\n", "                print(f\"❌ HTTP error adding {field_name}: {response.status_code}\")\n", "                results['failed_fields'].append({'field': field_name, 'error': f'HTTP {response.status_code}'})\n", "                \n", "        except Exception as e:\n", "            print(f\"❌ Exception adding {field_name}: {e}\")\n", "            results['failed_fields'].append({'field': field_name, 'error': str(e)})\n", "    \n", "    # Summary\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"📊 FIELD ADDITION SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    if results['added_fields']:\n", "        print(f\"✅ Successfully added {len(results['added_fields'])} field(s):\")\n", "        for field in results['added_fields']:\n", "            print(f\"  ✅ {field}\")\n", "        results['success'] = True\n", "    \n", "    if results['failed_fields']:\n", "        print(f\"\\n❌ Failed to add {len(results['failed_fields'])} field(s):\")\n", "        for failed in results['failed_fields']:\n", "            print(f\"  ❌ {failed['field']}: {failed['error']}\")\n", "    \n", "    if results['added_fields']:\n", "        print(\"\\n⏳ Waiting for schema changes to take effect...\")\n", "        time.sleep(5)  # Give <PERSON><PERSON> time to process schema changes\n", "    \n", "    return results\n", "\n", "# Add missing fields if any\n", "missing_fields = schema_info.get('missing_fields', [])\n", "field_addition_results = add_translation_fields(missing_fields)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: <PERSON><PERSON><PERSON> Changes and Test Updates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def verify_schema_and_test_updates():\n", "    \"\"\"\n", "    Verify that the schema changes took effect and test updates.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 STEP 3: VERIFYING SCHEMA CHANGES AND TESTING UPDATES\")\n", "    print(\"=\"*60)\n", "    \n", "    verification_results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'schema_verification': {},\n", "        'update_tests': [],\n", "        'success': <PERSON><PERSON><PERSON>\n", "    }\n", "    \n", "    # Step 3.1: Verify schema changes\n", "    print(\"\\n📋 Verifying schema changes...\")\n", "    \n", "    try:\n", "        response = requests.get(f\"{SCHEMA_URL}/fields\", params={'wt': 'json'}, timeout=30)\n", "        \n", "        if response.status_code == 200:\n", "            schema_data = response.json()\n", "            fields = schema_data.get('fields', [])\n", "            field_names = [f['name'] for f in fields]\n", "            \n", "            for field_name in TRANSLATION_FIELDS.keys():\n", "                if field_name in field_names:\n", "                    field_info = next(f for f in fields if f['name'] == field_name)\n", "                    print(f\"  ✅ {field_name}: EXISTS (type={field_info.get('type')}, multiValued={field_info.get('multiValued')})\")\n", "                    verification_results['schema_verification'][field_name] = {'exists': True, 'info': field_info}\n", "                else:\n", "                    print(f\"  ❌ {field_name}: STILL MISSING\")\n", "                    verification_results['schema_verification'][field_name] = {'exists': False}\n", "        else:\n", "            print(f\"❌ Failed to verify schema: {response.status_code}\")\n", "            verification_results['schema_verification']['error'] = f'HTTP {response.status_code}'\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error verifying schema: {e}\")\n", "        verification_results['schema_verification']['error'] = str(e)\n", "    \n", "    # Step 3.2: Test updates with the new fields\n", "    print(\"\\n🧪 Testing updates with translation fields...\")\n", "    \n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    test_desc = f\"[SCHEMA FIX TEST DESC] {timestamp}\"\n", "    test_profil = f\"[SCHEMA FIX TEST PROFIL] {timestamp}\"\n", "    \n", "    # Test atomic update with set\n", "    update_doc = {\n", "        'id': TEST_DOC_ID,\n", "        'tr_field_offre_description_poste': {'set': [test_desc]},\n", "        'tr_field_offre_profil': {'set': [test_profil]}\n", "    }\n", "    \n", "    print(f\"📄 Test update document: {json.dumps(update_doc, indent=2)}\")\n", "    \n", "    try:\n", "        # Send the update\n", "        headers = {'Content-Type': 'application/json'}\n", "        params = {'commit': 'true', 'wt': 'json'}\n", "        \n", "        print(f\"🚀 Sending test update...\")\n", "        response = requests.post(\n", "            UPDATE_URL,\n", "            params=params,\n", "            headers=headers,\n", "            data=json.dumps([update_doc]),\n", "            timeout=30\n", "        )\n", "        \n", "        print(f\"📡 Update response status: {response.status_code}\")\n", "        print(f\"📡 Update response text: {response.text}\")\n", "        \n", "        if response.status_code == 200:\n", "            response_json = response.json()\n", "            solr_status = response_json.get('responseHeader', {}).get('status', -1)\n", "            \n", "            if solr_status == 0:\n", "                print(f\"✅ Update request successful\")\n", "                \n", "                # Wait and verify\n", "                print(\"⏳ Waiting for update to be processed...\")\n", "                time.sleep(3)\n", "                \n", "                # Verify the update\n", "                verify_params = {\n", "                    'q': f'id:\"{TEST_DOC_ID}\"',\n", "                    'fl': 'id,tr_field_offre_description_poste,tr_field_offre_profil',\n", "                    'wt': 'json'\n", "                }\n", "                \n", "                verify_response = requests.get(SELECT_URL, params=verify_params, timeout=10)\n", "                \n", "                if verify_response.status_code == 200:\n", "                    verify_data = verify_response.json()\n", "                    docs = verify_data.get('response', {}).get('docs', [])\n", "                    \n", "                    if docs:\n", "                        doc = docs[0]\n", "                        print(f\"📋 Updated document: {json.dumps(doc, indent=2)}\")\n", "                        \n", "                        # Check if fields were updated\n", "                        desc_updated = 'tr_field_offre_description_poste' in doc and test_desc in str(doc['tr_field_offre_description_poste'])\n", "                        profil_updated = 'tr_field_offre_profil' in doc and test_profil in str(doc['tr_field_offre_profil'])\n", "                        \n", "                        if desc_updated and profil_updated:\n", "                            print(f\"🎉 SUCCESS! Both translation fields updated successfully!\")\n", "                            print(f\"  ✅ Description field: {doc.get('tr_field_offre_description_poste')}\")\n", "                            print(f\"  ✅ Profil field: {doc.get('tr_field_offre_profil')}\")\n", "                            verification_results['success'] = True\n", "                        else:\n", "                            print(f\"⚠️ Partial success - desc: {'✅' if desc_updated else '❌'}, profil: {'✅' if profil_updated else '❌'}\")\n", "                        \n", "                        verification_results['update_tests'].append({\n", "                            'success': desc_updated and profil_updated,\n", "                            'desc_updated': desc_updated,\n", "                            'profil_updated': profil_updated,\n", "                            'document': doc\n", "                        })\n", "                    else:\n", "                        print(f\"❌ Document not found after update\")\n", "                        verification_results['update_tests'].append({'success': False, 'error': 'Document not found'})\n", "                else:\n", "                    print(f\"❌ Verification query failed: {verify_response.status_code}\")\n", "                    verification_results['update_tests'].append({'success': False, 'error': f'Verification failed: {verify_response.status_code}'})\n", "            else:\n", "                print(f\"❌ Solr returned error status: {solr_status}\")\n", "                verification_results['update_tests'].append({'success': False, 'error': f'Solr error: {solr_status}'})\n", "        else:\n", "            print(f\"❌ Update request failed: {response.status_code}\")\n", "            verification_results['update_tests'].append({'success': False, 'error': f'HTTP {response.status_code}'})\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error testing update: {e}\")\n", "        verification_results['update_tests'].append({'success': False, 'error': str(e)})\n", "    \n", "    return verification_results\n", "\n", "# Run verification and testing\n", "verification_results = verify_schema_and_test_updates()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Generate Fix Summary and Pipeline Update Instructions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_fix_summary():\n", "    \"\"\"\n", "    Generate a comprehensive summary of the schema fix and provide pipeline update instructions.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"📋 COMPREHENSIVE SCHEMA FIX SUMMARY\")\n", "    print(\"=\"*80)\n", "    \n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    \n", "    # Compile comprehensive report\n", "    fix_report = {\n", "        'timestamp': timestamp,\n", "        'problem_identified': 'Translation fields missing from Solr schema',\n", "        'root_cause': 'Fields tr_field_offre_description_poste and tr_field_offre_profil were not defined in the schema',\n", "        'schema_investigation': schema_info,\n", "        'field_addition_results': field_addition_results,\n", "        'verification_results': verification_results,\n", "        'fix_successful': verification_results.get('success', False)\n", "    }\n", "    \n", "    # Analysis\n", "    print(\"\\n🔍 PROBLEM ANALYSIS:\")\n", "    print(\"-\" * 50)\n", "    print(\"❌ Root Cause: Translation fields missing from Solr schema\")\n", "    print(\"❌ Impact: All update attempts silently ignored by <PERSON><PERSON>\")\n", "    print(\"❌ Fields Missing: tr_field_offre_description_poste, tr_field_offre_profil\")\n", "    \n", "    # Results\n", "    print(\"\\n📊 FIX RESULTS:\")\n", "    print(\"-\" * 50)\n", "    \n", "    if field_addition_results.get('success', False):\n", "        added_fields = field_addition_results.get('added_fields', [])\n", "        print(f\"✅ Schema Fix: SUCCESS - Added {len(added_fields)} field(s)\")\n", "        for field in added_fields:\n", "            print(f\"  ✅ {field}\")\n", "    else:\n", "        print(\"❌ Schema Fix: FAILED\")\n", "        failed_fields = field_addition_results.get('failed_fields', [])\n", "        for failed in failed_fields:\n", "            print(f\"  ❌ {failed['field']}: {failed['error']}\")\n", "    \n", "    if verification_results.get('success', False):\n", "        print(\"✅ Update Test: SUCCESS - Translation fields now working!\")\n", "        print(\"✅ Pipeline Status: READY TO USE\")\n", "    else:\n", "        print(\"❌ Update Test: FAILED - Additional investigation needed\")\n", "        print(\"❌ Pipeline Status: NEEDS MORE WORK\")\n", "    \n", "    # Instructions\n", "    print(\"\\n💡 NEXT STEPS:\")\n", "    print(\"-\" * 50)\n", "    \n", "    if verification_results.get('success', False):\n", "        print(\"🎉 SUCCESS! Your translation pipeline should now work correctly.\")\n", "        print(\"\\n📋 To use the working pipeline:\")\n", "        print(\"1. ✅ Use the existing atomic update syntax in your main pipeline\")\n", "        print(\"2. ✅ The working format is:\")\n", "        print(\"   {\")\n", "        print(\"     'id': 'document_id',\")\n", "        print(\"     'tr_field_offre_description_poste': {'set': [translation_text]},\")\n", "        print(\"     'tr_field_offre_profil': {'set': [translation_text]}\")\n", "        print(\"   }\")\n", "        print(\"3. ✅ Test with Morocco first, then expand to other countries\")\n", "        print(\"4. ✅ Monitor the first few batches to ensure consistency\")\n", "        \n", "        print(\"\\n🔧 For other countries:\")\n", "        print(\"- Run this same schema fixer for each country core\")\n", "        print(\"- Or add fields manually using the same field definitions\")\n", "        \n", "    else:\n", "        print(\"❌ Schema fix incomplete. Additional steps needed:\")\n", "        print(\"1. Check Solr permissions for schema modifications\")\n", "        print(\"2. Verify Solr version supports dynamic schema updates\")\n", "        print(\"3. Consider manual schema.xml modification\")\n", "        print(\"4. Contact Solr administrator if needed\")\n", "    \n", "    # Save comprehensive report\n", "    report_filename = f\"schema_fix_report_{timestamp}.json\"\n", "    report_filepath = os.path.join(RESULTS_DIR, report_filename)\n", "    \n", "    with open(report_filepath, 'w', encoding='utf-8') as f:\n", "        json.dump(fix_report, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"\\n📁 Detailed report saved to: {report_filepath}\")\n", "    \n", "    # Final verification URL\n", "    verification_url = f\"http://************:8983/solr/core_jobsearch_maroc/select?q=id:\\\"{TEST_DOC_ID}\\\"&fl=id,tr_field_offre_description_poste,tr_field_offre_profil&wt=json&indent=true\"\n", "    print(f\"\\n🔗 Verify the fix manually:\")\n", "    print(f\"{verification_url}\")\n", "    \n", "    return fix_report\n", "\n", "# Generate final summary\n", "final_report = generate_fix_summary()\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"🎯 SCHEMA FIX SESSION COMPLETE\")\n", "print(\"=\"*80)\n", "\n", "if final_report.get('fix_successful', False):\n", "    print(\"\\n🎉 SUCCESS! Your Solr schema has been fixed!\")\n", "    print(\"✅ Translation fields are now available\")\n", "    print(\"✅ Updates are working correctly\")\n", "    print(\"✅ Your translation pipeline is ready to use\")\n", "    print(\"\\n🚀 You can now run your Morocco translation test again and it should work!\")\nelse:\n", "    print(\"\\n⚠️ Schema fix needs additional work\")\n", "    print(\"📋 Review the detailed report for next steps\")\n", "    print(\"🔧 Consider manual schema modification or admin assistance\")\n", "\n", "print(\"\\n🔧 Happy translating!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}