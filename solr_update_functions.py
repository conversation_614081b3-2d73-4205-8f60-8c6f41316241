"""
Solr update functions for the job offer translation pipeline.
"""

import json
import logging
import requests
import time
from datetime import datetime

logger = logging.getLogger(__name__)

def update_solr_with_translations(translated_offers, country, batch_size=50, commit_within=10000, final_commit=True, dry_run=False):
    """
    Update Solr with translated job offers.
    
    Args:
        translated_offers (list): List of job offers with translations
        country (str): Country code
        batch_size (int): Number of documents to update in each batch
        commit_within (int): Commit within time in milliseconds
        final_commit (bool): Whether to perform a final commit after all updates
        dry_run (bool): If True, don't actually update Solr, just log what would be done
        
    Returns:
        bool or list: True if successful, list of failed offers if partial success, False if failed
    """
    if not translated_offers:
        logger.warning("No translated offers to update in Solr")
        return True
    
    try:
        # Get Solr URL for the country
        solr_base_url = "http://51.195.54.52:8983/solr/"
        solr_url = f"{solr_base_url}core_jobsearch_{country}"
        update_url = f"{solr_url}/update"
        
        logger.info(f"Updating Solr with {len(translated_offers)} translated offers for {country}")
        logger.info(f"Using batch size: {batch_size}")
        
        # Track offers that failed to update
        offers_with_error = []
        
        # Process in batches
        for i in range(0, len(translated_offers), batch_size):
            batch = translated_offers[i:i+batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(translated_offers) + batch_size - 1)//batch_size} ({len(batch)} offers)")
            
            # Prepare update documents
            update_docs = []
            
            for offer in batch:
                # Ensure we have the ID field
                if 'id' not in offer:
                    logger.warning(f"Offer missing ID field, skipping: {offer.get('entity_id', 'unknown')}")
                    offers_with_error.append(offer)
                    continue
                
                # Create update document with ID field
                update_doc = {
                    "id": offer['id']
                }
                
                # Add translated fields
                target_fields = {}
                for source_field, target_field in {
                    'sm_field_offre_description_poste': 'tr_field_offre_description_poste',
                    'sm_field_offre_profil': 'tr_field_offre_profil'
                }.items():
                    if target_field in offer:
                        # Ensure the value is an array to match the original format
                        if isinstance(offer[target_field], list):
                            update_doc[target_field] = offer[target_field]
                        else:
                            update_doc[target_field] = [offer[target_field]]
                        target_fields[target_field] = update_doc[target_field]
                
                # Skip if no translated fields to update
                if not target_fields:
                    logger.warning(f"No translated fields found for offer {offer['id']}, skipping")
                    offers_with_error.append(offer)
                    continue
                
                update_docs.append(update_doc)
            
            # Skip if no documents to update
            if not update_docs:
                logger.warning(f"No valid documents to update in batch {i//batch_size + 1}")
                continue
            
            # Log what would be done in dry run mode
            if dry_run:
                logger.info(f"DRY RUN: Would update {len(update_docs)} documents in Solr")
                for doc in update_docs[:3]:  # Log first 3 as examples
                    logger.info(f"DRY RUN: Example update document: {json.dumps(doc)}")
                if len(update_docs) > 3:
                    logger.info(f"DRY RUN: ... and {len(update_docs) - 3} more")
                continue
            
            # Prepare update request
            headers = {
                'Content-Type': 'application/json'
            }
            
            params = {
                'commit': 'false',
                'commitWithin': commit_within,
                'wt': 'json'
            }
            
            # Send update request
            try:
                start_time = time.time()
                response = requests.post(
                    update_url,
                    headers=headers,
                    params=params,
                    data=json.dumps(update_docs)
                )
                end_time = time.time()
                
                # Check response
                if response.status_code == 200:
                    response_json = response.json()
                    if response_json.get('responseHeader', {}).get('status') == 0:
                        logger.info(f"Successfully updated batch {i//batch_size + 1} ({len(update_docs)} documents) in {end_time - start_time:.2f} seconds")
                    else:
                        logger.error(f"Solr update failed for batch {i//batch_size + 1}: {response_json}")
                        offers_with_error.extend(batch)
                else:
                    logger.error(f"Solr update request failed for batch {i//batch_size + 1}: {response.status_code} - {response.text}")
                    offers_with_error.extend(batch)
            except Exception as e:
                logger.error(f"Error sending update request for batch {i//batch_size + 1}: {e}")
                offers_with_error.extend(batch)
        
        # Perform final commit if requested
        if final_commit and not dry_run and not offers_with_error:
            try:
                logger.info("Performing final commit")
                commit_url = f"{update_url}?commit=true"
                response = requests.get(commit_url)
                
                if response.status_code == 200:
                    response_json = response.json()
                    if response_json.get('responseHeader', {}).get('status') == 0:
                        logger.info("Final commit successful")
                    else:
                        logger.warning(f"Final commit returned non-zero status: {response_json}")
                else:
                    logger.warning(f"Final commit request failed: {response.status_code} - {response.text}")
            except Exception as e:
                logger.warning(f"Error during final commit: {e}")
        
        # Return result
        if not offers_with_error:
            logger.info("Successfully updated all offers in Solr")
            return True
        else:
            logger.warning(f"Failed to update {len(offers_with_error)} out of {len(translated_offers)} offers in Solr")
            return offers_with_error
    except Exception as e:
        logger.error(f"Error in update_solr_with_translations: {e}")
        return False

def test_solr_update(country, num_docs=1, dry_run=True):
    """
    Test Solr update functionality with a sample document.
    
    Args:
        country (str): Country code
        num_docs (int): Number of sample documents to create
        dry_run (bool): If True, don't actually update Solr
        
    Returns:
        bool: True if successful, False if failed
    """
    print("\n" + "=" * 50)
    print("🔍 SOLR UPDATE TEST")
    print("=" * 50)
    
    # Create sample translated offers
    sample_offers = []
    
    for i in range(num_docs):
        offer_id = f"test_offer_{i}_{int(time.time())}"
        sample_offers.append({
            "id": f"test/node/{offer_id}",
            "entity_id": offer_id,
            "sm_field_offre_description_poste": [
                "Ceci est une description de poste de test."
            ],
            "sm_field_offre_profil": [
                "Ceci est un profil de test."
            ],
            "tr_field_offre_description_poste": [
                "This is a test job description."
            ],
            "tr_field_offre_profil": [
                "This is a test profile."
            ]
        })
    
    print(f"\nCreated {len(sample_offers)} sample translated offers")
    print(f"Dry run mode: {'Enabled' if dry_run else 'Disabled'}")
    
    # Update Solr
    result = update_solr_with_translations(
        sample_offers,
        country,
        batch_size=num_docs,
        commit_within=1000,
        final_commit=True,
        dry_run=dry_run
    )
    
    if result is True:
        print("\n✅ Solr update test successful")
        if dry_run:
            print("Note: This was a dry run, no actual updates were made to Solr")
        return True
    elif isinstance(result, list):
        print(f"\n⚠️ Solr update partially successful ({len(result)} failures)")
        return False
    else:
        print("\n❌ Solr update test failed")
        return False

def verify_solr_update(country, offer_id):
    """
    Verify that a document was updated in Solr.
    
    Args:
        country (str): Country code
        offer_id (str): Document ID to verify
        
    Returns:
        dict: Document if found, None if not found
    """
    try:
        # Get Solr URL for the country
        solr_base_url = "http://51.195.54.52:8983/solr/"
        solr_url = f"{solr_base_url}core_jobsearch_{country}"
        
        # Prepare query
        params = {
            'q': f'id:"{offer_id}"',
            'fl': '*',
            'wt': 'json'
        }
        
        # Send query
        response = requests.get(f"{solr_url}/select", params=params)
        
        if response.status_code == 200:
            response_json = response.json()
            docs = response_json.get('response', {}).get('docs', [])
            
            if docs:
                return docs[0]
            else:
                return None
        else:
            logger.error(f"Error querying Solr: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error verifying Solr update: {e}")
        return None
