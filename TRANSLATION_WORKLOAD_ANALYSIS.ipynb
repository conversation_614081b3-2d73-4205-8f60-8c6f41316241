{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Translation Workload Analysis\n", "\n", "This notebook analyzes the translation workload across all French-speaking countries by querying each country's Solr core to count documents that need translation and gather statistics.\n", "\n", "## Key Features:\n", "1. Counts untranslated job offers per country (missing both translation fields)\n", "2. Generates statistical overview (counts, percentages, document sizes)\n", "3. Creates visualizations (bar charts, pie charts)\n", "4. Exports results to CSV/JSON for planning and resource allocation\n", "\n", "This notebook uses the same configuration system as the main translation pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "try:\n", "    import json\n", "    import os\n", "    import time\n", "    import requests\n", "    import pandas as pd\n", "    import numpy as np\n", "    from datetime import datetime\n", "    import logging\n", "    print(\"✅ Basic Python libraries successfully imported\")\n", "except ImportError as e:\n", "    print(f\"❌ Error importing basic libraries: {e}\")\n", "\n", "# Try importing visualization libraries\n", "missing_libraries = []\n", "try:\n", "    import matplotlib.pyplot as plt\n", "    import seaborn as sns\n", "    print(\"✅ Visualization libraries successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"matplotlib seaborn\")\n", "    print(\"❌ Visualization libraries not found\")\n", "\n", "try:\n", "    import pysolr\n", "    print(\"✅ pysolr library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"pysolr\")\n", "    print(\"❌ pysolr library not found\")\n", "\n", "# If any libraries are missing, print installation instructions\n", "if missing_libraries:\n", "    print(\"\\n⚠️ Some required libraries are missing. Please install them using pip:\")\n", "    for lib in missing_libraries:\n", "        print(f\"pip install {lib}\")\n", "    print(\"\\nAfter installing, restart the kernel and run this cell again.\")\n", "else:\n", "    print(\"\\n✅ All required libraries are installed!\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('translation_workload_analysis')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Configuration\n", "\n", "We'll load the same configuration system used in the main translation pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Base configuration constants\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "CONFIG_DIR = \"config\"\n", "CONFIG_FILE = os.path.join(CONFIG_DIR, \"translation_config.json\")\n", "RESULTS_DIR = \"analysis_results\"\n", "\n", "# Create necessary directories\n", "for directory in [CONFIG_DIR, RESULTS_DIR]:\n", "    os.makedirs(directory, exist_ok=True)\n", "\n", "# Fields we're interested in for job offers\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "# Countries to analyze - all French-speaking countries with job offers\n", "COUNTRIES = [\n", "    'algerie', 'benin', 'burkina', 'burundi', 'cameroun', 'centrafrique', \n", "    'congo', 'cote_d_ivoire', 'guinee', 'gabon', 'mali', 'mauritanie', \n", "    'maroc', 'niger', 'rdc', 'senegal', 'tchad', 'togo', 'tunisie'\n", "]\n", "\n", "# Default configuration\n", "DEFAULT_CONFIG = {\n", "    \"general\": {\n", "        \"default_batch_size\": 5,\n", "        \"config_version\": \"1.0.0\",\n", "        \"last_updated\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    },\n", "    \"country_specific\": {\n", "        \"batch_sizes\": {\n", "            \"algerie\": 10,\n", "            \"maroc\": 10,\n", "            \"tunisie\": 10,\n", "            \"senegal\": 8,\n", "            \"cote_d_ivoire\": 8\n", "        }\n", "    },\n", "    \"solr\": {\n", "        \"connection\": {\n", "            \"timeout\": 30,\n", "            \"base_url\": SOLR_BASE_URL\n", "        },\n", "        \"query\": {\n", "            \"rows_per_query\": 100,\n", "            \"sort_field\": \"entity_id\",\n", "            \"sort_order\": \"asc\"\n", "        }\n", "    },\n", "    \"analysis\": {\n", "        \"sample_size\": 100,  # Number of documents to sample for size analysis\n", "        \"export_formats\": [\"csv\", \"json\"],  # Export formats\n", "        \"visualization\": {\n", "            \"figure_size\": [12, 8],  # Default figure size\n", "            \"color_palette\": \"viridis\"  # Default color palette\n", "        }\n", "    },\n", "    \"logging\": {\n", "        \"level\": \"INFO\",\n", "        \"format\": \"%(asctime)s - %(levelname)s - %(message)s\"\n", "    }\n", "}\n", "\n", "# Function to load configuration\n", "def load_config():\n", "    \"\"\"\n", "    Load configuration from file or create default if not exists.\n", "    \n", "    Returns:\n", "        dict: Configuration dictionary\n", "    \"\"\"\n", "    try:\n", "        if os.path.exists(CONFIG_FILE):\n", "            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:\n", "                config = json.load(f)\n", "                # Add analysis section if it doesn't exist (for backward compatibility)\n", "                if \"analysis\" not in config:\n", "                    config[\"analysis\"] = DEFAULT_CONFIG[\"analysis\"]\n", "                    save_config(config)\n", "                print(f\"✅ Configuration loaded from {CONFIG_FILE}\")\n", "                return config\n", "        else:\n", "            # Create default configuration file\n", "            save_config(DEFAULT_CONFIG)\n", "            print(f\"✅ Default configuration created at {CONFIG_FILE}\")\n", "            return DEFAULT_CONFIG\n", "    except Exception as e:\n", "        print(f\"❌ Error loading configuration: {e}\")\n", "        print(\"Using default configuration instead.\")\n", "        return DEFAULT_CONFIG\n", "\n", "# Function to save configuration\n", "def save_config(config):\n", "    \"\"\"\n", "    Save configuration to file.\n", "    \n", "    Args:\n", "        config (dict): Configuration dictionary\n", "        \n", "    Returns:\n", "        bool: True if successful, False otherwise\n", "    \"\"\"\n", "    try:\n", "        # Update last_updated timestamp\n", "        config['general']['last_updated'] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "        \n", "        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:\n", "            json.dump(config, f, ensure_ascii=False, indent=2)\n", "        print(f\"✅ Configuration saved to {CONFIG_FILE}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"❌ Error saving configuration: {e}\")\n", "        return False\n", "\n", "# Load the configuration\n", "CONFIG = load_config()\n", "\n", "# Configure logging based on configuration\n", "log_level = getattr(logging, CONFIG['logging']['level'])\n", "logging.basicConfig(level=log_level, format=CONFIG['logging']['format'])\n", "logger = logging.getLogger('translation_workload_analysis')\n", "\n", "# Print configuration summary\n", "print(f\"📋 Configuration loaded (version {CONFIG['general']['config_version']})\")\n", "print(f\"📅 Last updated: {CONFIG['general']['last_updated']}\")\n", "print(f\"🔍 Log level: {CONFIG['logging']['level']}\")\n", "print(f\"🌍 Analyzing {len(COUNTRIES)} countries\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Solr Connection Functions\n", "\n", "These functions handle connecting to Solr and querying the job offer cores."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a connection pool for reusing Solr connections\n", "solr_connections = {}\n", "\n", "def get_jobsearch_connection(country, timeout=None):\n", "    \"\"\"\n", "    Get a connection to the JobSearch Solr core for the specified country.\n", "    Uses connection pooling to reuse existing connections.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        timeout (int, optional): Connection timeout in seconds. If None, uses the value from config.\n", "        \n", "    Returns:\n", "        pysolr.Solr: Solr connection\n", "    \"\"\"\n", "    try:\n", "        # Use timeout from config if not specified\n", "        if timeout is None:\n", "            timeout = CONFIG['solr']['connection']['timeout']\n", "            \n", "        # Check if we already have a connection for this country\n", "        if country in solr_connections:\n", "            logger.debug(f\"Using existing connection for {country}\")\n", "            return solr_connections[country]\n", "        \n", "        # Use the correct core pattern: core_jobsearch_[country]\n", "        solr_url = f\"{CONFIG['solr']['connection']['base_url']}core_jobsearch_{country}\"\n", "        logger.info(f\"Creating new connection to JobSearch Solr core at {solr_url}\")\n", "        \n", "        # Create PySOLR connection with timeout\n", "        solr = pysolr.Solr(solr_url, timeout=timeout)\n", "        \n", "        # Store in connection pool\n", "        solr_connections[country] = solr\n", "        return solr\n", "    except Exception as e:\n", "        logger.error(f\"Error connecting to JobSearch Solr for {country}: {e}\")\n", "        return None\n", "\n", "def query_solr_direct(country, params=None):\n", "    \"\"\"\n", "    Query Solr directly using HTTP requests instead of PySOLR.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        params (dict): Query parameters\n", "        \n", "    Returns:\n", "        dict: Solr response as JSON\n", "    \"\"\"\n", "    try:\n", "        # Set default parameters if none provided\n", "        if params is None:\n", "            sort_param = f\"{CONFIG['solr']['query']['sort_field']} {CONFIG['solr']['query']['sort_order']}\"\n", "            params = {\n", "                'q': '*:*',\n", "                'fq': '*:*',\n", "                'fl': 'id',\n", "                'rows': 0,  # Just get counts, no actual documents\n", "                'wt': 'json'\n", "            }\n", "        \n", "        # Construct the URL\n", "        solr_url = f\"{CONFIG['solr']['connection']['base_url']}core_jobsearch_{country}/select\"\n", "        \n", "        # Make the request\n", "        logger.debug(f\"Making direct HTTP request to Solr: {solr_url}\")\n", "        response = requests.get(solr_url, params=params, timeout=CONFIG['solr']['connection']['timeout'])\n", "        \n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            # Parse the JSON response\n", "            result = response.json()\n", "            logger.debug(f\"Direct Solr query successful. Found {result.get('response', {}).get('numFound', 0)} documents.\")\n", "            return result\n", "        else:\n", "            logger.error(f\"Direct Solr query failed with status code {response.status_code}: {response.text}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error in direct Solr query: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workload Analysis Functions\n", "\n", "These functions analyze the translation workload across all countries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def count_untranslated_documents(country):\n", "    \"\"\"\n", "    Count the number of documents that need translation for a specific country.\n", "    Documents need translation if they are missing both translation fields.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        int: Number of documents that need translation\n", "    \"\"\"\n", "    try:\n", "        # Create filter query to find documents missing both translation fields\n", "        translation_filters = []\n", "        for target_field in SOURCE_FIELDS.values():\n", "            translation_filters.append(f\"-{target_field}:[* TO *]\")\n", "        \n", "        # Combine filters with AND\n", "        filter_query = \" AND \".join(translation_filters)\n", "        \n", "        # Set up query parameters\n", "        params = {\n", "            'q': '*:*',\n", "            'fq': filter_query,\n", "            'rows': 0,  # We only need the count, not the actual documents\n", "            'wt': 'json'\n", "        }\n", "        \n", "        # Execute the query\n", "        result = query_solr_direct(country, params)\n", "        \n", "        if result:\n", "            count = result.get('response', {}).get('numFound', 0)\n", "            logger.info(f\"Found {count} untranslated documents for {country}\")\n", "            return count\n", "        else:\n", "            logger.error(f\"Failed to count untranslated documents for {country}\")\n", "            return 0\n", "    except Exception as e:\n", "        logger.error(f\"Error counting untranslated documents for {country}: {e}\")\n", "        return 0\n", "\n", "def count_total_documents(country):\n", "    \"\"\"\n", "    Count the total number of documents for a specific country.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        int: Total number of documents\n", "    \"\"\"\n", "    try:\n", "        # Set up query parameters\n", "        params = {\n", "            'q': '*:*',\n", "            'rows': 0,  # We only need the count, not the actual documents\n", "            'wt': 'json'\n", "        }\n", "        \n", "        # Execute the query\n", "        result = query_solr_direct(country, params)\n", "        \n", "        if result:\n", "            count = result.get('response', {}).get('numFound', 0)\n", "            logger.info(f\"Found {count} total documents for {country}\")\n", "            return count\n", "        else:\n", "            logger.error(f\"Failed to count total documents for {country}\")\n", "            return 0\n", "    except Exception as e:\n", "        logger.error(f\"Error counting total documents for {country}: {e}\")\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_document_samples(country, sample_size=None):\n", "    \"\"\"\n", "    Get a sample of documents to analyze their size.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        sample_size (int, optional): Number of documents to sample. If None, uses the configured value.\n", "        \n", "    Returns:\n", "        list: List of document samples\n", "    \"\"\"\n", "    try:\n", "        # Use configured sample size if not specified\n", "        if sample_size is None:\n", "            sample_size = CONFIG['analysis']['sample_size']\n", "        \n", "        # Create filter query to find documents missing both translation fields\n", "        translation_filters = []\n", "        for target_field in SOURCE_FIELDS.values():\n", "            translation_filters.append(f\"-{target_field}:[* TO *]\")\n", "        \n", "        # Combine filters with AND\n", "        filter_query = \" AND \".join(translation_filters)\n", "        \n", "        # Fields to retrieve\n", "        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Set up query parameters\n", "        params = {\n", "            'q': '*:*',\n", "            'fq': filter_query,\n", "            'fl': ','.join(fields),\n", "            'rows': sample_size,\n", "            'start': 0,\n", "            'wt': 'json'\n", "        }\n", "        \n", "        # Execute the query\n", "        result = query_solr_direct(country, params)\n", "        \n", "        if result:\n", "            docs = result.get('response', {}).get('docs', [])\n", "            logger.info(f\"Retrieved {len(docs)} document samples for {country}\")\n", "            return docs\n", "        else:\n", "            logger.error(f\"Failed to retrieve document samples for {country}\")\n", "            return []\n", "    except Exception as e:\n", "        logger.error(f\"Error retrieving document samples for {country}: {e}\")\n", "        return []\n", "\n", "def calculate_document_sizes(samples):\n", "    \"\"\"\n", "    Calculate the average size (character count) of documents.\n", "    \n", "    Args:\n", "        samples (list): List of document samples\n", "        \n", "    Returns:\n", "        dict: Dictionary with average sizes for each field and total\n", "    \"\"\"\n", "    try:\n", "        if not samples:\n", "            return {\n", "                'description_size': 0,\n", "                'profile_size': 0,\n", "                'total_size': 0,\n", "                'sample_count': 0\n", "            }\n", "        \n", "        description_sizes = []\n", "        profile_sizes = []\n", "        \n", "        for doc in samples:\n", "            # Process description field\n", "            if 'sm_field_offre_description_poste' in doc:\n", "                value = doc['sm_field_offre_description_poste']\n", "                if isinstance(value, list) and value:\n", "                    description_sizes.append(len(value[0]))\n", "                elif isinstance(value, str):\n", "                    description_sizes.append(len(value))\n", "            \n", "            # Process profile field\n", "            if 'sm_field_offre_profil' in doc:\n", "                value = doc['sm_field_offre_profil']\n", "                if isinstance(value, list) and value:\n", "                    profile_sizes.append(len(value[0]))\n", "                elif isinstance(value, str):\n", "                    profile_sizes.append(len(value))\n", "        \n", "        # Calculate averages\n", "        avg_description_size = sum(description_sizes) / len(description_sizes) if description_sizes else 0\n", "        avg_profile_size = sum(profile_sizes) / len(profile_sizes) if profile_sizes else 0\n", "        avg_total_size = avg_description_size + avg_profile_size\n", "        \n", "        return {\n", "            'description_size': avg_description_size,\n", "            'profile_size': avg_profile_size,\n", "            'total_size': avg_total_size,\n", "            'sample_count': len(samples)\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error calculating document sizes: {e}\")\n", "        return {\n", "            'description_size': 0,\n", "            'profile_size': 0,\n", "            'total_size': 0,\n", "            'sample_count': 0\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_workload():\n", "    \"\"\"\n", "    Analyze the translation workload across all countries.\n", "    \n", "    Returns:\n", "        pandas.DataFrame: DataFrame with workload analysis results\n", "    \"\"\"\n", "    try:\n", "        # Initialize results dictionary\n", "        results = []\n", "        \n", "        # Analyze each country\n", "        for country in COUNTRIES:\n", "            logger.info(f\"Analyzing workload for {country}...\")\n", "            \n", "            # Count documents\n", "            untranslated_count = count_untranslated_documents(country)\n", "            total_count = count_total_documents(country)\n", "            \n", "            # Calculate percentage\n", "            percentage = (untranslated_count / total_count * 100) if total_count > 0 else 0\n", "            \n", "            # Get document samples and calculate sizes\n", "            samples = get_document_samples(country)\n", "            sizes = calculate_document_sizes(samples)\n", "            \n", "            # Add to results\n", "            results.append({\n", "                'country': country,\n", "                'untranslated_count': untranslated_count,\n", "                'total_count': total_count,\n", "                'percentage_untranslated': percentage,\n", "                'avg_description_size': sizes['description_size'],\n", "                'avg_profile_size': sizes['profile_size'],\n", "                'avg_total_size': sizes['total_size'],\n", "                'sample_count': sizes['sample_count']\n", "            })\n", "            \n", "            logger.info(f\"Completed analysis for {country}\")\n", "        \n", "        # Convert to DataFrame\n", "        df = pd.DataFrame(results)\n", "        \n", "        # Calculate total workload\n", "        total_untranslated = df['untranslated_count'].sum()\n", "        df['percentage_of_total_workload'] = df['untranslated_count'] / total_untranslated * 100 if total_untranslated > 0 else 0\n", "        \n", "        # Calculate estimated characters to translate\n", "        df['estimated_chars_to_translate'] = df['untranslated_count'] * df['avg_total_size']\n", "        \n", "        # Sort by untranslated count (descending)\n", "        df = df.sort_values('untranslated_count', ascending=False).reset_index(drop=True)\n", "        \n", "        logger.info(f\"Workload analysis completed for {len(COUNTRIES)} countries\")\n", "        return df\n", "    except Exception as e:\n", "        logger.error(f\"Error analyzing workload: {e}\")\n", "        return pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Workload Analysis\n", "\n", "Now let's run the workload analysis across all countries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the workload analysis\n", "print(\"Starting workload analysis...\")\n", "workload_df = analyze_workload()\n", "\n", "# Display the results\n", "if not workload_df.empty:\n", "    print(\"\\nWorkload Analysis Results:\")\n", "    print(f\"Total countries analyzed: {len(workload_df)}\")\n", "    print(f\"Total untranslated documents: {workload_df['untranslated_count'].sum():,}\")\n", "    print(f\"Total estimated characters to translate: {workload_df['estimated_chars_to_translate'].sum():,.0f}\")\n", "    \n", "    # Display the DataFrame\n", "    pd.set_option('display.float_format', '{:.2f}'.format)\n", "    display(workload_df)\n", "else:\n", "    print(\"No results found. Check the logs for errors.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize the Results\n", "\n", "Let's create visualizations to better understand the workload distribution."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_visualizations(df):\n", "    \"\"\"\n", "    Create visualizations of the workload analysis results.\n", "    \n", "    Args:\n", "        df (pandas.DataFrame): DataFrame with workload analysis results\n", "    \"\"\"\n", "    if df.empty:\n", "        print(\"No data to visualize.\")\n", "        return\n", "    \n", "    # Set the style\n", "    sns.set_theme(style=\"whitegrid\")\n", "    \n", "    # Get figure size and color palette from config\n", "    fig_size = CONFIG['analysis']['visualization']['figure_size']\n", "    color_palette = CONFIG['analysis']['visualization']['color_palette']\n", "    \n", "    # 1. Bar chart of untranslated documents by country\n", "    plt.figure(figsize=fig_size)\n", "    ax = sns.barplot(x='country', y='untranslated_count', data=df, palette=color_palette)\n", "    plt.title('Number of Untranslated Documents by Country', fontsize=16)\n", "    plt.xlabel('Country', fontsize=12)\n", "    plt.ylabel('Number of Untranslated Documents', fontsize=12)\n", "    plt.xticks(rotation=45, ha='right')\n", "    \n", "    # Add value labels on top of bars\n", "    for i, v in enumerate(df['untranslated_count']):\n", "        ax.text(i, v + 0.1, f\"{v:,}\", ha='center', fontsize=9)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 2. Pie chart of workload distribution\n", "    plt.figure(figsize=fig_size)\n", "    \n", "    # Filter to only include countries with significant workload (>1%)\n", "    significant_df = df[df['percentage_of_total_workload'] > 1].copy()\n", "    other_percentage = 100 - significant_df['percentage_of_total_workload'].sum()\n", "    \n", "    # Create data for pie chart\n", "    labels = significant_df['country'].tolist()\n", "    if other_percentage > 0:\n", "        labels.append('Other')\n", "    \n", "    sizes = significant_df['percentage_of_total_workload'].tolist()\n", "    if other_percentage > 0:\n", "        sizes.append(other_percentage)\n", "    \n", "    # Create pie chart\n", "    plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90, shadow=False, \n", "            textprops={'fontsize': 12}, colors=sns.color_palette(color_palette, len(labels)))\n", "    plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle\n", "    plt.title('Distribution of Translation Workload by Country', fontsize=16)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 3. Bar chart of average document sizes\n", "    plt.figure(figsize=fig_size)\n", "    \n", "    # Prepare data for stacked bar chart\n", "    top_countries = df.head(10).copy()  # Top 10 countries by untranslated count\n", "    \n", "    # Create stacked bar chart\n", "    ax = top_countries.plot(kind='bar', x='country', \n", "                           y=['avg_description_size', 'avg_profile_size'], \n", "                           stacked=True, figsize=fig_size, \n", "                           color=['#3498db', '#e74c3c'])\n", "    \n", "    plt.title('Average Document Size by Country (Top 10)', fontsize=16)\n", "    plt.xlabel('Country', fontsize=12)\n", "    plt.ylabel('Average Characters', fontsize=12)\n", "    plt.xticks(rotation=45, ha='right')\n", "    plt.legend(['Description', 'Profile'], fontsize=12)\n", "    \n", "    # Add total size labels on top of bars\n", "    for i, v in enumerate(top_countries['avg_total_size']):\n", "        ax.text(i, v + 10, f\"{v:.0f}\", ha='center', fontsize=9)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 4. Scatter plot of document count vs. average size\n", "    plt.figure(figsize=fig_size)\n", "    \n", "    # Create scatter plot\n", "    plt.scatter(df['untranslated_count'], df['avg_total_size'], \n", "               s=df['percentage_of_total_workload'] * 20,  # Size points by percentage\n", "               alpha=0.7, c=range(len(df)), cmap=color_palette)\n", "    \n", "    # Add country labels to points\n", "    for i, row in df.iterrows():\n", "        plt.annotate(row['country'], \n", "                    (row['untranslated_count'], row['avg_total_size']),\n", "                    xytext=(5, 5), textcoords='offset points', fontsize=9)\n", "    \n", "    plt.title('Untranslated Documents vs. Average Document Size', fontsize=16)\n", "    plt.xlabel('Number of Untranslated Documents', fontsize=12)\n", "    plt.ylabel('Average Document Size (Characters)', fontsize=12)\n", "    plt.grid(True, linestyle='--', alpha=0.7)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualizations if we have data\n", "if not workload_df.empty:\n", "    create_visualizations(workload_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Results\n", "\n", "Let's export the results to CSV and JSON for further analysis and planning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_results(df, formats=None):\n", "    \"\"\"\n", "    Export the workload analysis results to various formats.\n", "    \n", "    Args:\n", "        df (pandas.DataFrame): DataFrame with workload analysis results\n", "        formats (list, optional): List of export formats. If None, uses the configured formats.\n", "        \n", "    Returns:\n", "        dict: Dictionary with paths to exported files\n", "    \"\"\"\n", "    if df.empty:\n", "        print(\"No data to export.\")\n", "        return {}\n", "    \n", "    # Use configured formats if not specified\n", "    if formats is None:\n", "        formats = CONFIG['analysis']['export_formats']\n", "    \n", "    # Create timestamp for filenames\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # Initialize results\n", "    export_paths = {}\n", "    \n", "    # Export to each format\n", "    for fmt in formats:\n", "        try:\n", "            if fmt.lower() == 'csv':\n", "                # Export to CSV\n", "                path = os.path.join(RESULTS_DIR, f\"translation_workload_{timestamp}.csv\")\n", "                df.to_csv(path, index=False)\n", "                export_paths['csv'] = path\n", "                print(f\"✅ Results exported to CSV: {path}\")\n", "            \n", "            elif fmt.lower() == 'json':\n", "                # Export to JSON\n", "                path = os.path.join(RESULTS_DIR, f\"translation_workload_{timestamp}.json\")\n", "                df.to_json(path, orient='records', indent=2)\n", "                export_paths['json'] = path\n", "                print(f\"✅ Results exported to JSON: {path}\")\n", "            \n", "            elif fmt.lower() == 'excel':\n", "                # Export to Excel\n", "                path = os.path.join(RESULTS_DIR, f\"translation_workload_{timestamp}.xlsx\")\n", "                df.to_excel(path, index=False, sheet_name='Workload Analysis')\n", "                export_paths['excel'] = path\n", "                print(f\"✅ Results exported to Excel: {path}\")\n", "            \n", "            else:\n", "                print(f\"⚠️ Unsupported export format: {fmt}\")\n", "        \n", "        except Exception as e:\n", "            logger.error(f\"Error exporting to {fmt}: {e}\")\n", "            print(f\"❌ Failed to export to {fmt}: {e}\")\n", "    \n", "    return export_paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export the results if we have data\n", "if not workload_df.empty:\n", "    export_paths = export_results(workload_df)\n", "    \n", "    # Print summary\n", "    print(\"\\nExport Summary:\")\n", "    for fmt, path in export_paths.items():\n", "        print(f\"- {fmt.upper()}: {path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Recommendations\n", "\n", "Based on the workload analysis, we can make the following observations and recommendations:\n", "\n", "1. **Workload Distribution**: The countries with the highest number of untranslated documents are likely to be the larger French-speaking countries like Algeria, Morocco, and Tunisia.\n", "\n", "2. **Resource Allocation**: Resources should be allocated proportionally to the workload percentage. Countries with a higher percentage of the total workload should receive more translation resources.\n", "\n", "3. **Batch Size Optimization**: Countries with larger document sizes might benefit from smaller batch sizes to avoid timeouts or memory issues during translation.\n", "\n", "4. **Prioritization Strategy**: Consider prioritizing countries with:\n", "   - Higher number of untranslated documents\n", "   - Smaller average document sizes (for quicker wins)\n", "   - Higher business importance (if known)\n", "\n", "5. **Cost Estimation**: The total estimated characters to translate can be used to estimate the cost of translation using the OpenAI API pricing.\n", "\n", "6. **Monitoring Progress**: This analysis should be run periodically to monitor translation progress and adjust resource allocation as needed.\n", "\n", "The exported data can be used for detailed planning and resource allocation in spreadsheet applications or project management tools."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Execution\n", "\n", "Run the entire analysis pipeline with a single command."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def main():\n", "    \"\"\"\n", "    Run the complete workload analysis pipeline.\n", "    \"\"\"\n", "    print(\"🔍 Starting Translation Workload Analysis\")\n", "    print(f\"📊 Analyzing {len(COUNTRIES)} countries: {', '.join(COUNTRIES)}\\n\")\n", "    \n", "    # Step 1: Run the workload analysis\n", "    print(\"Step 1: Running workload analysis...\")\n", "    workload_df = analyze_workload()\n", "    \n", "    if workload_df.empty:\n", "        print(\"❌ Analysis failed. Check the logs for errors.\")\n", "        return\n", "    \n", "    # Display summary statistics\n", "    print(\"\\n✅ Analysis complete!\")\n", "    print(f\"📈 Total countries analyzed: {len(workload_df)}\")\n", "    print(f\"📄 Total untranslated documents: {workload_df['untranslated_count'].sum():,}\")\n", "    print(f\"📝 Total estimated characters to translate: {workload_df['estimated_chars_to_translate'].sum():,.0f}\")\n", "    \n", "    # Display the top 5 countries by workload\n", "    top_countries = workload_df.head(5)\n", "    print(\"\\n🔝 Top 5 countries by untranslated documents:\")\n", "    for i, row in top_countries.iterrows():\n", "        print(f\"   {i+1}. {row['country']}: {row['untranslated_count']:,} documents ({row['percentage_of_total_workload']:.1f}% of total workload)\")\n", "    \n", "    # Step 2: Create visualizations\n", "    print(\"\\nStep 2: Creating visualizations...\")\n", "    create_visualizations(workload_df)\n", "    print(\"✅ Visualizations created!\")\n", "    \n", "    # Step 3: Export results\n", "    print(\"\\nStep 3: Exporting results...\")\n", "    export_paths = export_results(workload_df)\n", "    \n", "    if export_paths:\n", "        print(\"\\n✅ Results exported to:\")\n", "        for fmt, path in export_paths.items():\n", "            print(f\"   - {fmt.upper()}: {path}\")\n", "    else:\n", "        print(\"❌ Failed to export results.\")\n", "    \n", "    print(\"\\n🎉 Workload analysis pipeline completed successfully!\")\n", "    print(\"📋 Use the exported data for planning and resource allocation.\")\n", "    \n", "    return workload_df\n", "\n", "# Uncomment to run the complete pipeline\n", "# workload_results = main()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}