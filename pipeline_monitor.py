"""
Pipeline Monitoring System

A minimalistic system to track data evolution through the job offer translation pipeline.
"""

import os
import json
import glob
from datetime import datetime
import statistics

# Directory for storing snapshots
SNAPSHOT_DIR = "pipeline_snapshots"

def take_snapshot(stage_name, data_sample, metrics, country=None, run_id=None):
    """
    Save a simple snapshot of pipeline stage data.
    
    Args:
        stage_name (str): Name of the pipeline stage
        data_sample (list): Small sample of records (5-10)
        metrics (dict): Key metrics like counts, success rates, etc.
        country (str, optional): Country code for this data
        run_id (str, optional): Unique ID for this pipeline run
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Use provided run_id or generate from timestamp
    if not run_id:
        run_id = timestamp[:8]  # Use date as run ID
    
    # Create a sample of the data (max 5 records)
    if data_sample and isinstance(data_sample, list):
        # For each record, include only essential fields to keep snapshots small
        sample = []
        for record in data_sample[:5]:
            if isinstance(record, dict):
                sample_record = {
                    "id": record.get("id", "unknown"),
                    "entity_id": record.get("entity_id", "unknown")
                }
                
                # Include source fields if present
                for field in ["sm_field_offre_description_poste", "sm_field_offre_profil"]:
                    if field in record:
                        # Get first 100 chars only to keep snapshot small
                        value = record[field]
                        if isinstance(value, list) and value:
                            sample_record[field] = value[0][:100] + "..." if len(value[0]) > 100 else value[0]
                        else:
                            sample_record[field] = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                
                # Include translated fields if present
                for field in ["tr_field_offre_description_poste", "tr_field_offre_profil"]:
                    if field in record:
                        # Get first 100 chars only to keep snapshot small
                        value = record[field]
                        if isinstance(value, list) and value:
                            sample_record[field] = value[0][:100] + "..." if len(value[0]) > 100 else value[0]
                        else:
                            sample_record[field] = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                
                sample.append(sample_record)
    else:
        sample = "No data sample provided"
    
    snapshot = {
        "timestamp": timestamp,
        "run_id": run_id,
        "stage": stage_name,
        "country": country,
        "metrics": metrics,
        "sample": sample
    }
    
    # Create directory if it doesn't exist
    os.makedirs(SNAPSHOT_DIR, exist_ok=True)
    
    # Create country subdirectory if specified
    if country:
        country_dir = os.path.join(SNAPSHOT_DIR, country)
        os.makedirs(country_dir, exist_ok=True)
        filename = f"{country_dir}/{run_id}_{stage_name}.json"
    else:
        filename = f"{SNAPSHOT_DIR}/{run_id}_{stage_name}.json"
    
    # Save as JSON file
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(snapshot, f, indent=2, ensure_ascii=False)
    
    print(f"📊 Snapshot saved: {filename}")
    return filename

def print_ascii_bar(label, value, max_value, width=40):
    """Print a simple ASCII bar chart."""
    if max_value <= 0:
        max_value = 1  # Avoid division by zero
    
    bar_width = int((value / max_value) * width)
    bar = "█" * bar_width + "░" * (width - bar_width)
    print(f"{label.ljust(20)} [{bar}] {value}/{max_value}")

def generate_pipeline_report(country=None, run_id=None, start_date=None, end_date=None):
    """
    Generate a simple report from snapshots.
    
    Args:
        country (str, optional): Filter by country
        run_id (str, optional): Filter by specific run ID
        start_date (str, optional): Start date filter (YYYYMMDD)
        end_date (str, optional): End date filter (YYYYMMDD)
    """
    # Determine which directory to search
    if country:
        search_dir = os.path.join(SNAPSHOT_DIR, country)
        if not os.path.exists(search_dir):
            print(f"No snapshots found for country: {country}")
            return
    else:
        search_dir = SNAPSHOT_DIR
    
    # Get all snapshot files
    snapshot_files = glob.glob(f"{search_dir}/*.json")
    
    if not snapshot_files:
        print("No snapshots found.")
        return
    
    # Filter by run_id if specified
    if run_id:
        snapshot_files = [f for f in snapshot_files if os.path.basename(f).startswith(run_id)]
    
    # Filter by date range if specified
    if start_date or end_date:
        filtered_files = []
        for file in snapshot_files:
            file_date = os.path.basename(file).split('_')[0]
            if start_date and file_date < start_date:
                continue
            if end_date and file_date > end_date:
                continue
            filtered_files.append(file)
        snapshot_files = filtered_files
    
    if not snapshot_files:
        print("No snapshots found matching the specified criteria.")
        return
    
    # Group by pipeline run
    runs = {}
    for file in snapshot_files:
        file_run_id = os.path.basename(file).split('_')[0]
        if file_run_id not in runs:
            runs[file_run_id] = []
        runs[file_run_id].append(file)
    
    # Define stage order for sorting
    stage_order = {
        "solr_retrieval": 1,
        "h5_storage": 2,
        "translation_prep": 3,
        "translation": 4,
        "solr_update": 5
    }
    
    # Print report
    print("\n" + "=" * 60)
    print("📈 PIPELINE EXECUTION REPORT")
    print("=" * 60)
    
    for run_id, files in sorted(runs.items(), reverse=True):  # Most recent first
        print(f"\n📅 Run Date: {run_id[:4]}-{run_id[4:6]}-{run_id[6:8]}")
        
        # Sort files by stage order
        files.sort(key=lambda f: stage_order.get(
            os.path.basename(f).split('_')[1].split('.')[0], 99
        ))
        
        # Load all snapshots for this run
        snapshots = []
        for file in files:
            with open(file, 'r', encoding='utf-8') as f:
                snapshots.append(json.load(f))
        
        # Print document flow through pipeline
        print("\n📊 Document Flow Through Pipeline:")
        
        # Extract counts from each stage
        counts = {}
        for snapshot in snapshots:
            stage = snapshot["stage"]
            metrics = snapshot["metrics"]
            
            # Extract the relevant count metric based on stage
            if stage == "solr_retrieval" and "total_retrieved" in metrics:
                counts[stage] = metrics["total_retrieved"]
            elif stage == "h5_storage" and "stored_count" in metrics:
                counts[stage] = metrics["stored_count"]
            elif stage == "translation_prep" and "batch_size" in metrics:
                counts[stage] = metrics["batch_size"]
            elif stage == "translation" and "translated_count" in metrics:
                counts[stage] = metrics["translated_count"]
            elif stage == "solr_update" and "update_count" in metrics:
                counts[stage] = metrics["update_count"]
        
        # Print ASCII bar chart for document flow
        if counts:
            max_count = max(counts.values())
            for stage in ["solr_retrieval", "h5_storage", "translation_prep", "translation", "solr_update"]:
                if stage in counts:
                    print_ascii_bar(stage, counts[stage], max_count)
        
        # Print detailed metrics for each stage
        print("\n📝 Detailed Metrics by Stage:")
        for snapshot in snapshots:
            stage = snapshot["stage"]
            metrics = snapshot["metrics"]
            country = snapshot.get("country", "unknown")
            
            print(f"\n  🔹 Stage: {stage} (Country: {country})")
            for key, value in metrics.items():
                print(f"    • {key}: {value}")
        
        # Print sample data if available
        print("\n🔍 Sample Data:")
        for snapshot in snapshots:
            stage = snapshot["stage"]
            sample = snapshot.get("sample", [])
            
            if sample and isinstance(sample, list) and len(sample) > 0:
                print(f"\n  🔹 Stage: {stage} - First record sample:")
                record = sample[0]
                for key, value in record.items():
                    if isinstance(value, str) and len(value) > 50:
                        print(f"    • {key}: {value[:50]}...")
                    else:
                        print(f"    • {key}: {value}")
        
        print("\n" + "-" * 60)

def analyze_pipeline_performance(country=None, last_n_runs=5):
    """
    Analyze pipeline performance trends across multiple runs.
    
    Args:
        country (str, optional): Filter by country
        last_n_runs (int): Number of most recent runs to analyze
    """
    # Determine which directory to search
    if country:
        search_dir = os.path.join(SNAPSHOT_DIR, country)
        if not os.path.exists(search_dir):
            print(f"No snapshots found for country: {country}")
            return
    else:
        search_dir = SNAPSHOT_DIR
    
    # Get all snapshot files
    snapshot_files = glob.glob(f"{search_dir}/*.json")
    
    if not snapshot_files:
        print("No snapshots found.")
        return
    
    # Group by pipeline run
    runs = {}
    for file in snapshot_files:
        file_run_id = os.path.basename(file).split('_')[0]
        if file_run_id not in runs:
            runs[file_run_id] = []
        runs[file_run_id].append(file)
    
    # Sort runs by date (most recent first) and limit to last_n_runs
    sorted_runs = sorted(runs.keys(), reverse=True)[:last_n_runs]
    
    if not sorted_runs:
        print("No runs found.")
        return
    
    # Metrics to track across runs
    metrics_by_stage = {
        "solr_retrieval": ["total_retrieved", "valid_offers"],
        "translation": ["translated_count", "success_rate"],
        "solr_update": ["update_count"]
    }
    
    # Collect metrics across runs
    collected_metrics = {}
    for stage, metrics in metrics_by_stage.items():
        collected_metrics[stage] = {metric: [] for metric in metrics}
    
    # Process each run
    for run_id in sorted_runs:
        files = runs[run_id]
        
        for file in files:
            with open(file, 'r', encoding='utf-8') as f:
                snapshot = json.load(f)
            
            stage = snapshot["stage"]
            if stage in metrics_by_stage:
                for metric in metrics_by_stage[stage]:
                    if metric in snapshot["metrics"]:
                        collected_metrics[stage][metric].append(snapshot["metrics"][metric])
    
    # Print performance analysis
    print("\n" + "=" * 60)
    print("📊 PIPELINE PERFORMANCE ANALYSIS")
    print("=" * 60)
    
    print(f"\nAnalyzing the last {len(sorted_runs)} pipeline runs")
    if country:
        print(f"Country: {country}")
    
    # Print date range
    if sorted_runs:
        oldest = sorted_runs[-1]
        newest = sorted_runs[0]
        print(f"Date range: {oldest[:4]}-{oldest[4:6]}-{oldest[6:8]} to {newest[:4]}-{newest[4:6]}-{newest[6:8]}")
    
    # Print metrics analysis
    for stage, metrics in collected_metrics.items():
        print(f"\n🔹 {stage.upper()} STAGE:")
        
        for metric, values in metrics.items():
            if values:
                avg = sum(values) / len(values)
                if len(values) > 1:
                    try:
                        stdev = statistics.stdev(values)
                        print(f"  • {metric}: Avg={avg:.2f}, StdDev={stdev:.2f}, Min={min(values)}, Max={max(values)}")
                    except:
                        print(f"  • {metric}: Avg={avg:.2f}, Min={min(values)}, Max={max(values)}")
                else:
                    print(f"  • {metric}: Value={values[0]}")
    
    print("\n" + "=" * 60)
