{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Verify Source Data in Test Document\n", "\n", "Let's double-check if the test document c8xu1x/node/157221 actually has the source fields.\n", "The previous investigation might have missed them due to field selection issues.\n", "\n", "## Multiple Verification Approaches\n", "1. **Explicit field query** - Request specific source fields\n", "2. **Dynamic field query** - Check for sm_* pattern fields\n", "3. **Full document dump** - Get everything with different parameters\n", "4. **Compare with other documents** - See if the issue is document-specific"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pysolr\n", "import json\n", "import requests\n", "from datetime import datetime\n", "\n", "# Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "COUNTRY = \"maroc\"\n", "CORE_NAME = f\"core_jobsearch_{COUNTRY}\"\n", "SOLR_CORE_URL = f\"{SOLR_BASE_URL}{CORE_NAME}\"\n", "SELECT_URL = f\"{SOLR_CORE_URL}/select\"\n", "\n", "# Test document\n", "TEST_DOC_ID = \"c8xu1x/node/157221\"\n", "\n", "print(\"🔍 Source Data Verification Initialized\")\n", "print(f\"Core: {CORE_NAME}\")\n", "print(f\"Test document: {TEST_DOC_ID}\")\n", "\n", "# Create PySOLR connection\n", "solr = pysolr.Solr(SOLR_CORE_URL)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verification 1: Explicit Source Field Query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def verify_explicit_source_fields():\n", "    \"\"\"\n", "    Explicitly request the source fields we know should exist.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 VERIFICATION 1: EXPLICIT SOURCE FIELD QUERY\")\n", "    print(\"=\"*60)\n", "    \n", "    # Direct HTTP request with explicit field list\n", "    params = {\n", "        'q': f'id:\"{TEST_DOC_ID}\"',\n", "        'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',\n", "        'wt': 'json',\n", "        'indent': 'true'\n", "    }\n", "    \n", "    print(f\"🔗 Direct HTTP query with explicit fields...\")\n", "    print(f\"URL: {SELECT_URL}\")\n", "    print(f\"Params: {params}\")\n", "    \n", "    try:\n", "        response = requests.get(SELECT_URL, params=params, timeout=10)\n", "        \n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            docs = data.get('response', {}).get('docs', [])\n", "            \n", "            if docs:\n", "                doc = docs[0]\n", "                print(f\"✅ Document found via HTTP\")\n", "                print(f\"📋 Document content: {json.dumps(doc, indent=2)}\")\n", "                \n", "                # Check for source fields\n", "                has_desc = 'sm_field_offre_description_poste' in doc\n", "                has_profil = 'sm_field_offre_profil' in doc\n", "                has_entity_id = 'entity_id' in doc\n", "                \n", "                print(f\"\\n📊 Field Analysis:\")\n", "                print(f\"  entity_id: {'✅ FOUND' if has_entity_id else '❌ MISSING'}\")\n", "                print(f\"  sm_field_offre_description_poste: {'✅ FOUND' if has_desc else '❌ MISSING'}\")\n", "                print(f\"  sm_field_offre_profil: {'✅ FOUND' if has_profil else '❌ MISSING'}\")\n", "                \n", "                if has_desc:\n", "                    desc_content = doc['sm_field_offre_description_poste']\n", "                    print(f\"  📄 Description preview: {str(desc_content)[:100]}...\")\n", "                \n", "                if has_profil:\n", "                    profil_content = doc['sm_field_offre_profil']\n", "                    print(f\"  📄 Profil preview: {str(profil_content)[:100]}...\")\n", "                \n", "                return {\n", "                    'method': 'HTTP explicit fields',\n", "                    'found': True,\n", "                    'has_source_fields': has_desc and has_profil,\n", "                    'document': doc\n", "                }\n", "            else:\n", "                print(f\"❌ No documents found via HTTP\")\n", "                return {'method': 'HTTP explicit fields', 'found': False}\n", "        else:\n", "            print(f\"❌ HTTP request failed: {response.status_code}\")\n", "            return {'method': 'HTTP explicit fields', 'error': f'HTTP {response.status_code}'}\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error with HTTP request: {e}\")\n", "        return {'method': 'HTTP explicit fields', 'error': str(e)}\n", "\n", "# Run explicit field verification\n", "explicit_verification = verify_explicit_source_fields()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verification 2: PySOLR with Explicit Fields"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def verify_pysolr_explicit_fields():\n", "    \"\"\"\n", "    Use PySOLR to query with explicit field list.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 VERIFICATION 2: PYSOLR WITH EXPLICIT FIELDS\")\n", "    print(\"=\"*60)\n", "    \n", "    try:\n", "        print(f\"🔗 PySOLR query with explicit fields...\")\n", "        \n", "        # PySOLR query with explicit field list\n", "        results = solr.search(\n", "            f'id:\"{TEST_DOC_ID}\"',\n", "            fl='id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil'\n", "        )\n", "        \n", "        if results.hits > 0:\n", "            doc = results.docs[0]\n", "            print(f\"✅ Document found via PySOLR\")\n", "            print(f\"📋 Document content: {json.dumps(doc, indent=2)}\")\n", "            \n", "            # Check for source fields\n", "            has_desc = 'sm_field_offre_description_poste' in doc\n", "            has_profil = 'sm_field_offre_profil' in doc\n", "            has_entity_id = 'entity_id' in doc\n", "            \n", "            print(f\"\\n📊 Field Analysis:\")\n", "            print(f\"  entity_id: {'✅ FOUND' if has_entity_id else '❌ MISSING'}\")\n", "            print(f\"  sm_field_offre_description_poste: {'✅ FOUND' if has_desc else '❌ MISSING'}\")\n", "            print(f\"  sm_field_offre_profil: {'✅ FOUND' if has_profil else '❌ MISSING'}\")\n", "            \n", "            return {\n", "                'method': 'PySOLR explicit fields',\n", "                'found': True,\n", "                'has_source_fields': has_desc and has_profil,\n", "                'document': doc\n", "            }\n", "        else:\n", "            print(f\"❌ No documents found via PySOLR\")\n", "            return {'method': 'PySOLR explicit fields', 'found': False}\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error with PySOLR request: {e}\")\n", "        return {'method': 'PySOLR explicit fields', 'error': str(e)}\n", "\n", "# Run PySOLR verification\n", "pysolr_verification = verify_pysolr_explicit_fields()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verification 3: Find Documents with Source Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_documents_with_source_data():\n", "    \"\"\"\n", "    Find other documents that definitely have source data to compare.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 VERIFICATION 3: FIND DOCUMENTS WITH SOURCE DATA\")\n", "    print(\"=\"*60)\n", "    \n", "    try:\n", "        print(f\"🔍 Searching for documents with source fields...\")\n", "        \n", "        # Search for documents that have the source fields\n", "        params = {\n", "            'q': 'sm_field_offre_description_poste:[* TO *]',\n", "            'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',\n", "            'rows': 3,\n", "            'wt': 'json'\n", "        }\n", "        \n", "        response = requests.get(SELECT_URL, params=params, timeout=10)\n", "        \n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            docs = data.get('response', {}).get('docs', [])\n", "            total_found = data.get('response', {}).get('numFound', 0)\n", "            \n", "            print(f\"📊 Total documents with source fields: {total_found}\")\n", "            \n", "            if docs:\n", "                print(f\"\\n📋 Sample documents with source data:\")\n", "                \n", "                for i, doc in enumerate(docs, 1):\n", "                    doc_id = doc.get('id', 'unknown')\n", "                    entity_id = doc.get('entity_id', 'unknown')\n", "                    has_desc = 'sm_field_offre_description_poste' in doc\n", "                    has_profil = 'sm_field_offre_profil' in doc\n", "                    \n", "                    print(f\"\\n  📄 Document {i}: {doc_id}\")\n", "                    print(f\"     Entity ID: {entity_id}\")\n", "                    print(f\"     Has description: {'✅' if has_desc else '❌'}\")\n", "                    print(f\"     Has profil: {'✅' if has_profil else '❌'}\")\n", "                    \n", "                    if has_desc:\n", "                        desc_preview = str(doc['sm_field_offre_description_poste'])[:100]\n", "                        print(f\"     Description preview: {desc_preview}...\")\n", "                \n", "                # Check if our test document is in the results\n", "                test_doc_in_results = any(doc.get('id') == TEST_DOC_ID for doc in docs)\n", "                \n", "                if test_doc_in_results:\n", "                    print(f\"\\n✅ Our test document {TEST_DOC_ID} IS in the results with source data!\")\n", "                else:\n", "                    print(f\"\\n❌ Our test document {TEST_DOC_ID} is NOT in the results\")\n", "                    print(f\"🔍 This suggests the test document might not have source fields\")\n", "                \n", "                return {\n", "                    'total_with_source': total_found,\n", "                    'sample_docs': docs,\n", "                    'test_doc_has_source': test_doc_in_results\n", "                }\n", "            else:\n", "                print(f\"❌ No documents found with source fields\")\n", "                return {'total_with_source': 0, 'test_doc_has_source': False}\n", "        else:\n", "            print(f\"❌ Search request failed: {response.status_code}\")\n", "            return {'error': f'HTTP {response.status_code}'}\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error searching for source data: {e}\")\n", "        return {'error': str(e)}\n", "\n", "# Find documents with source data\n", "source_data_search = find_documents_with_source_data()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Final Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*80)\n", "print(\"📋 SOURCE DATA VERIFICATION ANALYSIS\")\n", "print(\"=\"*80)\n", "\n", "# Analyze all verification results\n", "print(f\"\\n🔍 VERIFICATION RESULTS:\")\n", "print(\"-\" * 50)\n", "\n", "# HTTP explicit fields result\n", "if explicit_verification.get('found', False):\n", "    has_source = explicit_verification.get('has_source_fields', False)\n", "    print(f\"✅ HTTP explicit query: Document found\")\n", "    print(f\"   Source fields: {'✅ PRESENT' if has_source else '❌ MISSING'}\")\nelse:\n", "    print(f\"❌ HTTP explicit query: Document not found\")\n", "\n", "# PySOLR explicit fields result\n", "if pysolr_verification.get('found', False):\n", "    has_source = pysolr_verification.get('has_source_fields', False)\n", "    print(f\"✅ PySOLR explicit query: Document found\")\n", "    print(f\"   Source fields: {'✅ PRESENT' if has_source else '❌ MISSING'}\")\nelse:\n", "    print(f\"❌ PySOLR explicit query: Document not found\")\n", "\n", "# Source data search result\n", "total_with_source = source_data_search.get('total_with_source', 0)\n", "test_doc_has_source = source_data_search.get('test_doc_has_source', False)\n", "\n", "print(f\"\\n📊 Database Analysis:\")\n", "print(f\"   Total docs with source fields: {total_with_source}\")\n", "print(f\"   Test doc in source results: {'✅ YES' if test_doc_has_source else '❌ NO'}\")\n", "\n", "# Final conclusion\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"🎯 CONCLUSION\")\n", "print(\"=\"*60)\n", "\n", "if explicit_verification.get('has_source_fields', False) or pysolr_verification.get('has_source_fields', False):\n", "    print(\"\\n✅ CONFIRMED: Test document HAS source data!\")\n", "    print(\"✅ The previous investigation was incorrect\")\n", "    print(\"✅ Your suspicion was right - the document has the required fields\")\n", "    print(\"\\n🔧 NEXT STEPS:\")\n", "    print(\"1. ✅ Test the tm_* dynamic field approach with this document\")\n", "    print(\"2. ✅ The PySOLR approach should work with proper field names\")\n", "    print(\"3. ✅ Use either tm_* dynamic fields or manual schema modification\")\n", "    \nelif test_doc_has_source:\n", "    print(\"\\n⚠️ MIXED RESULTS: Document appears in source search but fields not returned\")\n", "    print(\"🔍 This suggests field access or permission issues\")\n", "    print(\"\\n🔧 NEXT STEPS:\")\n", "    print(\"1. 🔍 Try with a different document ID from the search results\")\n", "    print(\"2. 🔍 Check field permissions and access controls\")\n", "    \nelse:\n", "    print(\"\\n❌ CONFIRMED: Test document does NOT have source data\")\n", "    print(\"✅ The previous investigation was correct\")\n", "    print(\"\\n🔧 NEXT STEPS:\")\n", "    if total_with_source > 0:\n", "        print(\"1. ✅ Use one of the documents found in the search results\")\n", "        print(\"2. ✅ Test the translation pipeline with a document that has source data\")\n", "        print(\"3. ✅ The tm_* dynamic field approach should work with proper documents\")\n", "    else:\n", "        print(\"1. ❌ No documents found with source data - check core configuration\")\n", "        print(\"2. ❌ Verify you're using the correct core (jobsearch vs cvsearch)\")\n", "\n", "print(\"\\n🔧 Ready for next steps based on verification results!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}