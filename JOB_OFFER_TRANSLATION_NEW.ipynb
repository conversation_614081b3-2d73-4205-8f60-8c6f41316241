{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Job Offer Translation - JSON Format Implementation\n", "\n", "This notebook implements the job offer translation using OpenAI's Batch API with a fixed implementation that handles the JSON format requirement. It uses the `gpt-4.1-nano-2025-04-14` model as requested.\n", "\n", "Key features in this implementation:\n", "1. The messages include the word \"json\" to satisfy the requirement for using `response_format` of type `json_object`\n", "2. The `body` field in the batch request is properly formatted as an object (not a string)\n", "3. The batch file is saved with UTF-8 encoding without BOM\n", "4. Direct Solr queries are used to filter documents that need translation\n", "\n", "This notebook is designed to run on the server via X2GO where the Solr connection works."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "try:\n", "    import json\n", "    import logging\n", "    import os\n", "    import time\n", "    import re\n", "    import html\n", "    import shutil\n", "    import glob\n", "    import statistics\n", "    from datetime import datetime\n", "    import uuid\n", "    print(\"✅ Basic Python libraries successfully imported\")\n", "except ImportError as e:\n", "    print(f\"❌ Error importing basic libraries: {e}\")\n", "    \n", "# Create pipeline_monitor.py if it doesn't exist\n", "try:\n", "    from pipeline_monitor import take_snapshot, generate_pipeline_report, analyze_pipeline_performance\n", "    print(\"✅ Pipeline monitoring system successfully imported\")\n", "except ImportError:\n", "    print(\"⚠️ Pipeline monitoring system not found. Creating it now...\")\n", "    # We'll create the file later in a separate cell\n", "\n", "# Try importing external libraries\n", "missing_libraries = []\n", "\n", "try:\n", "    import requests\n", "    print(\"✅ requests library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"requests\")\n", "    print(\"❌ requests library not found\")\n", "\n", "try:\n", "    import h5py\n", "    import numpy as np\n", "    print(\"✅ h5py library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"h5py numpy\")\n", "    print(\"❌ h5py library not found\")\n", "\n", "try:\n", "    import pysolr\n", "    print(\"✅ pysolr library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"pysolr\")\n", "    print(\"❌ pysolr library not found\")\n", "\n", "try:\n", "    from openai import OpenAI\n", "    print(\"✅ openai library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"openai\")\n", "    print(\"❌ openai library not found\")\n", "\n", "# If any libraries are missing, print installation instructions\n", "if missing_libraries:\n", "    print(\"\\n⚠️ Some required libraries are missing. Please install them using pip:\")\n", "    for lib in missing_libraries:\n", "        print(f\"pip install {lib}\")\n", "    print(\"\\nAfter installing, restart the kernel and run this cell again.\")\n", "else:\n", "    print(\"\\n✅ All required libraries are installed!\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('job_offer_translation')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced Configuration System\n", "import json\n", "import logging\n", "import os\n", "from datetime import datetime\n", "\n", "# Base configuration constants\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "CONFIG_DIR = \"config\"\n", "CONFIG_FILE = os.path.join(CONFIG_DIR, \"translation_config.json\")\n", "BATCH_DIR = \"batch_files\"\n", "RESULTS_DIR = \"translation_results\"\n", "H5_DIR = \"h5_storage\"\n", "\n", "# Create necessary directories\n", "for directory in [CONFIG_DIR, BATCH_DIR, RESULTS_DIR, H5_DIR]:\n", "    os.makedirs(directory, exist_ok=True)\n", "\n", "# Fields we're interested in for job offers\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "# Countries to work with - all French-speaking countries with job offers\n", "COUNTRIES = [\n", "    'algerie', 'benin', 'burkina', 'burundi', 'cameroun', 'centrafrique', \n", "    'congo', 'cote_d_ivoire', 'guinee', 'gabon', 'mali', 'mauritanie', \n", "    'maroc', 'niger', 'rdc', 'senegal', 'tchad', 'togo', 'tunisie'\n", "]\n", "\n", "# Default configuration\n", "DEFAULT_CONFIG = {\n", "    \"general\": {\n", "        \"default_batch_size\": 5,  # Default batch size for all countries\n", "        \"config_version\": \"1.0.0\",  # Configuration version for tracking changes\n", "        \"last_updated\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    },\n", "    \"country_specific\": {\n", "        # Country-specific batch sizes (override default_batch_size)\n", "        # Countries with more job offers can have larger batch sizes\n", "        \"batch_sizes\": {\n", "            \"algerie\": 10,\n", "            \"maroc\": 10,\n", "            \"tunisie\": 10,\n", "            \"senegal\": 8,\n", "            \"cote_d_ivoire\": 8,\n", "            # Other countries will use the default_batch_size\n", "        }\n", "    },\n", "    \"openai\": {\n", "        \"models\": {\n", "            \"default\": \"gpt-4.1-nano-2025-04-14\",  # Default model\n", "            \"alternatives\": [  # Alternative models that can be selected\n", "                \"gpt-4.1-mini-2025-04-14\",\n", "                \"gpt-4.1-turbo-2025-04-14\",\n", "                \"gpt-4o-2024-05-13\"\n", "            ]\n", "        },\n", "        \"translation\": {\n", "            \"temperature\": 0.3,  # Lower for more consistent translations\n", "            \"system_prompt\": \"You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text.\",\n", "            \"user_prompt_template\": \"Translate the following French job offer text to English and return as JSON:\\n\\n{text}\"\n", "        },\n", "        \"batch_api\": {\n", "            \"max_wait_time\": 300,  # Maximum time to wait for batch completion (seconds)\n", "            \"wait_interval\": 5,  # Time between status checks (seconds)\n", "            \"retry_count\": 3,  # Number of retries for failed requests\n", "            \"retry_delay\": 10  # Delay between retries (seconds)\n", "        }\n", "    },\n", "    \"solr\": {\n", "        \"connection\": {\n", "            \"timeout\": 30,  # Connection timeout in seconds\n", "            \"base_url\": SOLR_BASE_URL  # Base URL for Solr\n", "        },\n", "        \"query\": {\n", "            \"rows_per_query\": 100,  # Maximum number of rows to return per query\n", "            \"sort_field\": \"entity_id\",  # Field to sort by\n", "            \"sort_order\": \"asc\"  # Sort order (asc or desc)\n", "        },\n", "        \"update\": {\n", "            \"commit_within\": 5000,  # Time in ms to commit within\n", "            \"batch_size\": 50,  # Number of documents to update in one batch\n", "            \"final_commit\": True  # Whether to perform a final commit after all updates\n", "        }\n", "    },\n", "    \"logging\": {\n", "        \"level\": \"INFO\",  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)\n", "        \"format\": \"%(asctime)s - %(levelname)s - %(message)s\",  # Log format\n", "        \"file\": {  # File logging configuration\n", "            \"enabled\": False,  # Whether to log to a file\n", "            \"path\": \"logs/translation.log\",  # Log file path\n", "            \"max_size\": 10485760,  # Maximum log file size (10 MB)\n", "            \"backup_count\": 5  # Number of backup log files to keep\n", "        }\n", "    },\n", "    \"error_handling\": {\n", "        \"max_consecutive_errors\": 5,  # Maximum number of consecutive errors before aborting\n", "        \"error_threshold_percentage\": 20,  # Maximum percentage of errors before aborting\n", "        \"continue_on_error\": True,  # Whether to continue processing after errors\n", "        \"error_log_file\": \"logs/error.log\"  # Error log file path\n", "    }\n", "}\n", "\n", "# Function to load configuration\n", "def load_config():\n", "    \"\"\"\n", "    Load configuration from file or create default if not exists.\n", "    \n", "    Returns:\n", "        dict: Configuration dictionary\n", "    \"\"\"\n", "    try:\n", "        if os.path.exists(CONFIG_FILE):\n", "            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:\n", "                config = json.load(f)\n", "                print(f\"✅ Configuration loaded from {CONFIG_FILE}\")\n", "                return config\n", "        else:\n", "            # Create default configuration file\n", "            save_config(DEFAULT_CONFIG)\n", "            print(f\"✅ Default configuration created at {CONFIG_FILE}\")\n", "            return DEFAULT_CONFIG\n", "    except Exception as e:\n", "        print(f\"❌ Error loading configuration: {e}\")\n", "        print(\"Using default configuration instead.\")\n", "        return DEFAULT_CONFIG\n", "\n", "# Function to save configuration\n", "def save_config(config):\n", "    \"\"\"\n", "    Save configuration to file.\n", "    \n", "    Args:\n", "        config (dict): Configuration dictionary\n", "        \n", "    Returns:\n", "        bool: True if successful, False otherwise\n", "    \"\"\"\n", "    try:\n", "        # Update last_updated timestamp\n", "        config['general']['last_updated'] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "        \n", "        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:\n", "            json.dump(config, f, ensure_ascii=False, indent=2)\n", "        print(f\"✅ Configuration saved to {CONFIG_FILE}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"❌ Error saving configuration: {e}\")\n", "        return False\n", "\n", "# Load the configuration\n", "CONFIG = load_config()\n", "\n", "# Add H5 storage configuration if it doesn't exist\n", "if 'h5_storage' not in CONFIG:\n", "    CONFIG['h5_storage'] = {\n", "        \"enabled\": True,  # Whether to use H5 storage\n", "        \"compression\": \"gzip\",  # Compression type (gzip, lzf, or None)\n", "        \"compression_level\": 4,  # Compression level (1-9, higher = more compression but slower)\n", "        \"chunk_size\": 100,  # Chunk size for datasets\n", "        \"buffer_size\": 1000,  # Maximum number of documents to keep in memory before flushing to disk\n", "        \"file_pattern\": \"{country}.h5\"  # File name pattern for H5 files\n", "    }\n", "    save_config(CONFIG)\n", "    print(\"✅ Added H5 storage configuration\")\n", "    \n", "# Add OpenAI configuration if it doesn't exist\n", "if 'openai' not in CONFIG:\n", "    CONFIG['openai'] = {\n", "        \"models\": {\n", "            \"default\": \"gpt-4.1-nano-2025-04-14\",  # Default model\n", "            \"alternatives\": [  # Alternative models that can be selected\n", "                \"gpt-4.1-mini-2025-04-14\",\n", "                \"gpt-4.1-turbo-2025-04-14\",\n", "                \"gpt-4o-2024-05-13\"\n", "            ]\n", "        },\n", "        \"translation\": {\n", "            \"temperature\": 0.3,  # Lower for more consistent translations\n", "            \"system_prompt\": \"You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text.\",\n", "            \"user_prompt_template\": \"Translate the following French job offer text to English and return as JSON:\\n\\n{text}\"\n", "        },\n", "        \"batch_api\": {\n", "            \"max_wait_time\": 300,  # Maximum time to wait for batch completion (seconds)\n", "            \"wait_interval\": 5,  # Time between status checks (seconds)\n", "            \"retry_count\": 3,  # Number of retries for failed requests\n", "            \"retry_delay\": 10  # Delay between retries (seconds)\n", "        }\n", "    }\n", "    save_config(CONFIG)\n", "    print(\"✅ Added OpenAI configuration\")\n", "    \n", "# Add other required configuration sections if they don't exist\n", "# Check for general section\n", "if 'general' not in CONFIG:\n", "    CONFIG['general'] = {\n", "        \"default_batch_size\": 5,  # Default batch size for all countries\n", "        \"config_version\": \"1.0.0\",  # Configuration version for tracking changes\n", "        \"last_updated\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    }\n", "    save_config(CONFIG)\n", "    print(\"✅ Added general configuration\")\n", "    \n", "# Check for country_specific section\n", "if 'country_specific' not in CONFIG:\n", "    CONFIG['country_specific'] = {\n", "        \"batch_sizes\": {\n", "            \"algerie\": 10,\n", "            \"maroc\": 10,\n", "            \"tunisie\": 10,\n", "            \"senegal\": 8,\n", "            \"cote_d_ivoire\": 8,\n", "        }\n", "    }\n", "    save_config(CONFIG)\n", "    print(\"✅ Added country_specific configuration\")\n", "    \n", "# Check for solr section\n", "if 'solr' not in CONFIG:\n", "    CONFIG['solr'] = {\n", "        \"connection\": {\n", "            \"timeout\": 30,  # Connection timeout in seconds\n", "            \"base_url\": SOLR_BASE_URL  # Base URL for Solr\n", "        },\n", "        \"query\": {\n", "            \"rows_per_query\": 100,  # Maximum number of rows to return per query\n", "            \"sort_field\": \"entity_id\",  # Field to sort by\n", "            \"sort_order\": \"asc\"  # Sort order (asc or desc)\n", "        },\n", "        \"update\": {\n", "            \"commit_within\": 5000,  # Time in ms to commit within\n", "            \"batch_size\": 50,  # Number of documents to update in one batch\n", "            \"final_commit\": True  # Whether to perform a final commit after all updates\n", "        }\n", "    }\n", "    save_config(CONFIG)\n", "    print(\"✅ Added solr configuration\")\n", "    \n", "# Check for error_handling section\n", "if 'error_handling' not in CONFIG:\n", "    CONFIG['error_handling'] = {\n", "        \"max_consecutive_errors\": 5,  # Maximum number of consecutive errors before aborting\n", "        \"error_threshold_percentage\": 20,  # Maximum percentage of errors before aborting\n", "        \"continue_on_error\": True,  # Whether to continue processing after errors\n", "        \"error_log_file\": \"logs/error.log\"  # Error log file path\n", "    }\n", "    save_config(CONFIG)\n", "    print(\"✅ Added error_handling configuration\")\n", "    \n", "# Check for logging section\n", "if 'logging' not in CONFIG:\n", "    CONFIG['logging'] = {\n", "        \"level\": \"INFO\",  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)\n", "        \"format\": \"%(asctime)s - %(levelname)s - %(message)s\",  # Log format\n", "        \"file\": {  # File logging configuration\n", "            \"enabled\": False,  # Whether to log to a file\n", "            \"path\": \"logs/translation.log\",  # Log file path\n", "            \"max_size\": 10485760,  # Maximum log file size (10 MB)\n", "            \"backup_count\": 5  # Number of backup log files to keep\n", "        }\n", "    }\n", "    save_config(CONFIG)\n", "    print(\"✅ Added logging configuration\")\n", "\n", "# Configure logging based on configuration\n", "log_level = getattr(logging, CONFIG['logging']['level'])\n", "logging.basicConfig(level=log_level, format=CONFIG['logging']['format'])\n", "logger = logging.getLogger('job_offer_translation')\n", "\n", "# Add file handler if enabled and file configuration exists\n", "if 'file' in CONFIG['logging'] and CONFIG['logging']['file']['enabled']:\n", "    log_dir = os.path.dirname(CONFIG['logging']['file']['path'])\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    file_handler = logging.FileHandler(CONFIG['logging']['file']['path'])\n", "    file_handler.setFormatter(logging.Formatter(CONFIG['logging']['format']))\n", "    logger.addHandler(file_handler)\n", "elif 'file' not in CONFIG['logging']:\n", "    # Add file configuration if it doesn't exist\n", "    CONFIG['logging']['file'] = {\n", "        \"enabled\": False,  # Whether to log to a file\n", "        \"path\": \"logs/translation.log\",  # Log file path\n", "        \"max_size\": 10485760,  # Maximum log file size (10 MB)\n", "        \"backup_count\": 5  # Number of backup log files to keep\n", "    }\n", "    save_config(CONFIG)\n", "    print(\"✅ Added logging file configuration\")\n", "\n", "# Helper function to get batch size for a country\n", "def get_batch_size(country):\n", "    \"\"\"\n", "    Get the batch size for a specific country.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        int: Batch size for the country\n", "    \"\"\"\n", "    return CONFIG['country_specific']['batch_sizes'].get(\n", "        country, CONFIG['general']['default_batch_size'])\n", "\n", "def validate_job_offer(offer):\n", "    \"\"\"\n", "    Validate a job offer document.\n", "    \n", "    Args:\n", "        offer (dict): Job offer document\n", "        \n", "    Returns:\n", "        tuple: (is_valid, reason)\n", "    \"\"\"\n", "    # Check if the offer has an ID\n", "    if 'id' not in offer:\n", "        return False, \"Missing ID\"\n", "    \n", "    # Check if the offer has an entity_id\n", "    if 'entity_id' not in offer:\n", "        return False, \"Missing entity_id\"\n", "    \n", "    # Check if the offer has at least one of the source fields\n", "    has_source_field = False\n", "    for source_field in SOURCE_FIELDS.keys():\n", "        if source_field in offer:\n", "            has_source_field = True\n", "            break\n", "    \n", "    if not has_source_field:\n", "        return False, \"Missing source fields\"\n", "    \n", "    return True, \"\"\n", "\n", "# Helper function to get current OpenAI model\n", "def get_openai_model():\n", "    \"\"\"\n", "    Get the current OpenAI model.\n", "    \n", "    Returns:\n", "        str: OpenAI model name\n", "    \"\"\"\n", "    return CONFIG['openai']['models']['default']\n", "\n", "# =============================================================================\n", "# CUSTOM CONFIGURATION ADJUSTMENT\n", "# Modify any parameters below to customize the pipeline behavior\n", "# =============================================================================\n", "\n", "# ------ GENERAL SETTINGS ------\n", "CONFIG['general']['default_batch_size'] = 5  # Default batch size for all countries\n", "\n", "# ------ COUNTRY-SPECIFIC BATCH SIZES ------\n", "# Adjust batch sizes for specific countries\n", "CONFIG['country_specific']['batch_sizes'] = {\n", "    'algerie': 10,\n", "    'maroc': 10,\n", "    'tunisie': 10,\n", "    'senegal': 8,\n", "    'cote_d_ivoire': 8,\n", "    'benin': 5,\n", "    'burkina': 5,\n", "    'burundi': 5,\n", "    'cameroun': 5,\n", "    'centrafrique': 5,\n", "    'congo': 5,\n", "    'guinee': 5,\n", "    'gabon': 5,\n", "    'mali': 5,\n", "    'mauritanie': 5,\n", "    'niger': 5,\n", "    'rdc': 5,\n", "    'tchad': 5,\n", "    'togo': 5\n", "}\n", "\n", "# ------ OPENAI API SETTINGS ------\n", "# Ensure all required OpenAI sections exist\n", "if 'models' not in CONFIG['openai']:\n", "    CONFIG['openai']['models'] = {}\n", "if 'translation' not in CONFIG['openai']:\n", "    CONFIG['openai']['translation'] = {}\n", "if 'batch_api' not in CONFIG['openai']:\n", "    CONFIG['openai']['batch_api'] = {}\n", "    \n", "# Model selection\n", "CONFIG['openai']['models']['default'] = \"gpt-4.1-nano-2025-04-14\"  # Options: \"gpt-4.1-nano-2025-04-14\", \"gpt-4.1-mini-2025-04-14\", \"gpt-4.1-turbo-2025-04-14\", \"gpt-4o-2024-05-13\"\n", "if 'alternatives' not in CONFIG['openai']['models']:\n", "    CONFIG['openai']['models']['alternatives'] = [\"gpt-4.1-mini-2025-04-14\", \"gpt-4.1-turbo-2025-04-14\", \"gpt-4o-2024-05-13\"]\n", "\n", "# Translation settings\n", "CONFIG['openai']['translation']['temperature'] = 0.3  # Lower for more consistent translations, higher for more variety (0.0-1.0)\n", "CONFIG['openai']['translation']['system_prompt'] = \"You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text.\"\n", "CONFIG['openai']['translation']['user_prompt_template'] = \"Translate the following French job offer text to English and return as JSON:\\n\\n{text}\"\n", "\n", "# Batch API settings\n", "CONFIG['openai']['batch_api']['max_wait_time'] = 1800  # Maximum time to wait for batch completion (seconds) - 30 minutes\n", "CONFIG['openai']['batch_api']['wait_interval'] = 10  # Time between status checks (seconds)\n", "CONFIG['openai']['batch_api']['retry_count'] = 3  # Number of retries for failed requests\n", "CONFIG['openai']['batch_api']['retry_delay'] = 10  # Delay between retries (seconds)\n", "\n", "# ------ SOLR CONNECTION SETTINGS ------\n", "# Ensure all required Solr sections exist\n", "if 'connection' not in CONFIG['solr']:\n", "    CONFIG['solr']['connection'] = {}\n", "if 'query' not in CONFIG['solr']:\n", "    CONFIG['solr']['query'] = {}\n", "if 'update' not in CONFIG['solr']:\n", "    CONFIG['solr']['update'] = {}\n", "    \n", "CONFIG['solr']['connection']['timeout'] = 30  # Connection timeout in seconds\n", "CONFIG['solr']['connection']['base_url'] = SOLR_BASE_URL  # Base URL for Solr\n", "\n", "# Solr query settings\n", "CONFIG['solr']['query']['rows_per_query'] = 100  # Maximum number of rows to return per query\n", "CONFIG['solr']['query']['sort_field'] = \"entity_id\"  # Field to sort by\n", "CONFIG['solr']['query']['sort_order'] = \"asc\"  # Sort order (asc or desc)\n", "\n", "# Solr update settings\n", "CONFIG['solr']['update']['commit_within'] = 5000  # Time in ms to commit within\n", "CONFIG['solr']['update']['batch_size'] = 50  # Number of documents to update in one batch\n", "CONFIG['solr']['update']['final_commit'] = True  # Whether to perform a final commit after all updates\n", "\n", "# ------ H5 STORAGE SETTINGS ------\n", "# Ensure all H5 storage settings exist\n", "if 'enabled' not in CONFIG['h5_storage']:\n", "    CONFIG['h5_storage']['enabled'] = True\n", "if 'compression' not in CONFIG['h5_storage']:\n", "    CONFIG['h5_storage']['compression'] = \"gzip\"\n", "if 'compression_level' not in CONFIG['h5_storage']:\n", "    CONFIG['h5_storage']['compression_level'] = 4\n", "if 'chunk_size' not in CONFIG['h5_storage']:\n", "    CONFIG['h5_storage']['chunk_size'] = 100\n", "if 'buffer_size' not in CONFIG['h5_storage']:\n", "    CONFIG['h5_storage']['buffer_size'] = 1000\n", "if 'file_pattern' not in CONFIG['h5_storage']:\n", "    CONFIG['h5_storage']['file_pattern'] = \"{country}.h5\"\n", "    \n", "CONFIG['h5_storage']['enabled'] = True  # Whether to use H5 storage\n", "CONFIG['h5_storage']['compression'] = \"gzip\"  # Compression type (gzip, lzf, or None)\n", "CONFIG['h5_storage']['compression_level'] = 4  # Compression level (1-9, higher = more compression but slower)\n", "CONFIG['h5_storage']['chunk_size'] = 100  # Chunk size for datasets\n", "CONFIG['h5_storage']['buffer_size'] = 1000  # Maximum number of documents to keep in memory before flushing to disk\n", "CONFIG['h5_storage']['file_pattern'] = \"{country}.h5\"  # File name pattern for H5 files\n", "\n", "# ------ LOGGING SETTINGS ------\n", "# Ensure all logging settings exist\n", "if 'level' not in CONFIG['logging']:\n", "    CONFIG['logging']['level'] = \"INFO\"\n", "if 'format' not in CONFIG['logging']:\n", "    CONFIG['logging']['format'] = \"%(asctime)s - %(levelname)s - %(message)s\"\n", "if 'file' not in CONFIG['logging']:\n", "    CONFIG['logging']['file'] = {}\n", "if 'enabled' not in CONFIG['logging']['file']:\n", "    CONFIG['logging']['file']['enabled'] = False\n", "if 'path' not in CONFIG['logging']['file']:\n", "    CONFIG['logging']['file']['path'] = \"logs/translation.log\"\n", "if 'max_size' not in CONFIG['logging']['file']:\n", "    CONFIG['logging']['file']['max_size'] = 10485760\n", "if 'backup_count' not in CONFIG['logging']['file']:\n", "    CONFIG['logging']['file']['backup_count'] = 5\n", "    \n", "CONFIG['logging']['level'] = \"INFO\"  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)\n", "CONFIG['logging']['format'] = \"%(asctime)s - %(levelname)s - %(message)s\"  # Log format\n", "\n", "# File logging settings\n", "CONFIG['logging']['file']['enabled'] = False  # Whether to log to a file\n", "CONFIG['logging']['file']['path'] = \"logs/translation.log\"  # Log file path\n", "CONFIG['logging']['file']['max_size'] = 10485760  # Maximum log file size (10 MB)\n", "CONFIG['logging']['file']['backup_count'] = 5  # Number of backup log files to keep\n", "\n", "# ------ <PERSON><PERSON><PERSON> HANDLING SETTINGS ------\n", "# Ensure all error handling settings exist\n", "if 'max_consecutive_errors' not in CONFIG['error_handling']:\n", "    CONFIG['error_handling']['max_consecutive_errors'] = 5\n", "if 'error_threshold_percentage' not in CONFIG['error_handling']:\n", "    CONFIG['error_handling']['error_threshold_percentage'] = 20\n", "if 'continue_on_error' not in CONFIG['error_handling']:\n", "    CONFIG['error_handling']['continue_on_error'] = True\n", "if 'error_log_file' not in CONFIG['error_handling']:\n", "    CONFIG['error_handling']['error_log_file'] = \"logs/error.log\"\n", "    \n", "CONFIG['error_handling']['max_consecutive_errors'] = 5  # Maximum number of consecutive errors before aborting\n", "CONFIG['error_handling']['error_threshold_percentage'] = 20  # Maximum percentage of errors before aborting\n", "CONFIG['error_handling']['continue_on_error'] = True  # Whether to continue processing after errors\n", "CONFIG['error_handling']['error_log_file'] = \"logs/error.log\"  # Error log file path\n", "\n", "# Save the updated configuration\n", "save_config(CONFIG)\n", "\n", "# Print confirmation of key settings\n", "print(\"\\n===== CONFIGURATION UPDATED =====\")\n", "print(f\"Default batch size: {CONFIG['general']['default_batch_size']}\")\n", "print(f\"OpenAI model: {CONFIG['openai']['models']['default']}\")\n", "print(f\"H5 storage: {'Enabled' if CONFIG['h5_storage']['enabled'] else 'Disabled'}\")\n", "print(f\"Logging level: {CONFIG['logging']['level']}\")\n", "print(\"=================================\\n\")\n", "\n", "# Print configuration summary\n", "print(f\"📋 Configuration loaded (version {CONFIG['general']['config_version']})\")\n", "print(f\"📅 Last updated: {CONFIG['general']['last_updated']}\")\n", "print(f\"🤖 Using OpenAI model: {get_openai_model()}\")\n", "print(f\"📊 Default batch size: {CONFIG['general']['default_batch_size']}\")\n", "print(f\"🔍 Log level: {CONFIG['logging']['level']}\")\n", "print(f\"💾 H5 storage: {'Enabled' if CONFIG['h5_storage']['enabled'] else 'Disabled'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. H5 Storage Functions\n", "\n", "These functions handle storing and retrieving data from H5 files."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dictionary to cache open H5 file handles\n", "h5_files = {}\n", "\n", "def get_h5_file_path(country):\n", "    \"\"\"\n", "    Get the path to the H5 file for a specific country.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        str: Path to the H5 file\n", "    \"\"\"\n", "    file_pattern = CONFIG['h5_storage']['file_pattern']\n", "    file_name = file_pattern.format(country=country)\n", "    return os.path.join(H5_DIR, file_name)\n", "\n", "def open_h5_file(country, mode='a'):\n", "    \"\"\"\n", "    Open an H5 file for a specific country.\n", "    Uses connection pooling to reuse existing file handles.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        mode (str): File mode ('r' for read-only, 'a' for read/write)\n", "        \n", "    Returns:\n", "        h5py.File: H5 file handle\n", "    \"\"\"\n", "    try:\n", "        # Check if we already have an open file handle for this country\n", "        if country in h5_files and h5_files[country] is not None:\n", "            # Check if the file is still open\n", "            if h5_files[country].id.valid:\n", "                logger.debug(f\"Using existing H5 file handle for {country}\")\n", "                return h5_files[country]\n", "            else:\n", "                # File was closed, remove it from the cache\n", "                logger.debug(f\"H5 file handle for {country} was closed, removing from cache\")\n", "                h5_files.pop(country, None)\n", "        \n", "        # Get the file path\n", "        file_path = get_h5_file_path(country)\n", "        logger.info(f\"Opening H5 file: {file_path} (mode={mode})\")\n", "        \n", "        # Create the file if it doesn't exist\n", "        if mode == 'a' and not os.path.exists(file_path):\n", "            logger.info(f\"Creating new H5 file: {file_path}\")\n", "            # Create parent directory if it doesn't exist\n", "            os.makedirs(os.path.dirname(file_path), exist_ok=True)\n", "        \n", "        # Open the file\n", "        h5_file = h5py.File(file_path, mode)\n", "        \n", "        # Store in connection pool\n", "        h5_files[country] = h5_file\n", "        \n", "        return h5_file\n", "    except Exception as e:\n", "        logger.error(f\"Error opening H5 file for {country}: {e}\")\n", "        return None\n", "\n", "def close_h5_file(country):\n", "    \"\"\"\n", "    Close an H5 file for a specific country.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        bool: True if successful, False otherwise\n", "    \"\"\"\n", "    try:\n", "        if country in h5_files and h5_files[country] is not None:\n", "            if h5_files[country].id.valid:\n", "                logger.info(f\"Closing H5 file for {country}\")\n", "                h5_files[country].close()\n", "            h5_files.pop(country, None)\n", "            return True\n", "        return False\n", "    except Exception as e:\n", "        logger.error(f\"Error closing H5 file for {country}: {e}\")\n", "        return False\n", "\n", "def close_all_h5_files():\n", "    \"\"\"\n", "    Close all open H5 files.\n", "    \n", "    Returns:\n", "        int: Number of files closed\n", "    \"\"\"\n", "    count = 0\n", "    for country in list(h5_files.keys()):\n", "        if close_h5_file(country):\n", "            count += 1\n", "    return count\n", "\n", "def store_job_offers_in_h5(job_offers, country):\n", "    \"\"\"\n", "    Store job offers in an H5 file.\n", "    \n", "    Args:\n", "        job_offers (list): List of job offer documents\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        bool: True if successful, False otherwise\n", "    \"\"\"\n", "    h5_file = None\n", "    try:\n", "        if not job_offers:\n", "            logger.warning(\"No job offers to store\")\n", "            return False\n", "        \n", "        if not CONFIG['h5_storage']['enabled']:\n", "            logger.info(\"H5 storage is disabled, skipping\")\n", "            return False\n", "        \n", "        # Close any existing file handle first to avoid write intent issues\n", "        close_h5_file(country)\n", "        \n", "        # Make sure the directory exists\n", "        file_path = get_h5_file_path(country)\n", "        os.makedirs(os.path.dirname(file_path), exist_ok=True)\n", "        \n", "        # Open the H5 file with write intent\n", "        h5_file = open_h5_file(country, mode='a')\n", "        if h5_file is None:\n", "            logger.error(f\"Failed to open H5 file for {country}\")\n", "            return False\n", "        \n", "        # Get compression settings\n", "        compression = CONFIG['h5_storage']['compression']\n", "        compression_level = CONFIG['h5_storage']['compression_level']\n", "        \n", "        # Create the main group if it doesn't exist\n", "        if 'job_offers' not in h5_file:\n", "            logger.info(f\"Creating 'job_offers' group in H5 file for {country}\")\n", "            h5_file.create_group('job_offers')\n", "        \n", "        # Get the job_offers group\n", "        job_offers_group = h5_file['job_offers']\n", "        \n", "        # Store each job offer\n", "        stored_count = 0\n", "        for offer in job_offers:\n", "            try:\n", "                # Validate the job offer\n", "                is_valid, reason = validate_job_offer(offer)\n", "                if not is_valid:\n", "                    logger.warning(f\"Skipping invalid offer: {reason}\")\n", "                    continue\n", "                \n", "                # Use the full ID as the key\n", "                offer_id = offer['id']\n", "                \n", "                # Create a group for this offer if it doesn't exist\n", "                if offer_id not in job_offers_group:\n", "                    offer_group = job_offers_group.create_group(offer_id)\n", "                else:\n", "                    offer_group = job_offers_group[offer_id]\n", "                \n", "                # Store each field as a dataset\n", "                for field, value in offer.items():\n", "                    try:\n", "                        # Skip fields that are already stored and haven't changed\n", "                        if field in offer_group:\n", "                            try:\n", "                                # For list fields, compare the first item\n", "                                if isinstance(value, list) and len(value) > 0:\n", "                                    stored_value = offer_group[field][()]\n", "                                    if isinstance(stored_value, bytes):\n", "                                        stored_value = stored_value.decode('utf-8')\n", "                                    if stored_value == value[0]:\n", "                                        continue\n", "                                # For string fields, compare directly\n", "                                elif isinstance(value, str):\n", "                                    stored_value = offer_group[field][()]\n", "                                    if isinstance(stored_value, bytes):\n", "                                        stored_value = stored_value.decode('utf-8')\n", "                                    if stored_value == value:\n", "                                        continue\n", "                                # For other fields, delete and recreate\n", "                                del offer_group[field]\n", "                            except Exception as compare_error:\n", "                                logger.warning(f\"Error comparing field {field} for offer {offer_id}: {compare_error}\")\n", "                                # If there's an error comparing, delete and recreate\n", "                                try:\n", "                                    del offer_group[field]\n", "                                except Exception:\n", "                                    pass  # Ignore if deletion fails\n", "                        \n", "                        # Convert value to string for storage\n", "                        if isinstance(value, list) and len(value) > 0:\n", "                            # Store only the first item for list fields\n", "                            string_value = str(value[0])\n", "                        elif isinstance(value, (int, float, bool)):\n", "                            # Store numeric values as strings\n", "                            string_value = str(value)\n", "                        elif isinstance(value, str):\n", "                            string_value = value\n", "                        else:\n", "                            # Skip complex types\n", "                            continue\n", "                        \n", "                        # Create the dataset - handle scalar dataset issues\n", "                        try:\n", "                            # Always store as a fixed-length string to avoid scalar dataset issues\n", "                            # Create a string dtype with a fixed length that can accommodate the data\n", "                            # Use utf-8 encoding to handle all Unicode characters\n", "                            string_dtype = h5py.string_dtype('utf-8', len(string_value.encode('utf-8')) + 50)  # Add extra padding for encoding\n", "                            \n", "                            # For longer strings, use compression\n", "                            if len(string_value) > 100:\n", "                                offer_group.create_dataset(\n", "                                    field,\n", "                                    data=string_value,\n", "                                    dtype=string_dtype,\n", "                                    compression=compression,\n", "                                    compression_opts=compression_level if compression == 'gzip' else None\n", "                                )\n", "                            else:\n", "                                # For short strings, don't use compression\n", "                                offer_group.create_dataset(field, data=string_value, dtype=string_dtype)\n", "                        except Exception as dataset_error:\n", "                            logger.warning(f\"Error creating dataset for field {field} in offer {offer_id}: {dataset_error}\")\n", "                            # Try a fallback method with explicit UTF-8 encoding\n", "                            try:\n", "                                # First try with explicit UTF-8 encoding and variable length\n", "                                offer_group.create_dataset(field, data=string_value, dtype=h5py.string_dtype('utf-8'))\n", "                            except Exception as fallback_error1:\n", "                                logger.warning(f\"First fallback failed for field {field} in offer {offer_id}: {fallback_error1}\")\n", "                                try:\n", "                                    # Second fallback: store as bytes with explicit encoding\n", "                                    encoded_value = string_value.encode('utf-8', errors='replace')\n", "                                    offer_group.create_dataset(field, data=encoded_value)\n", "                                except Exception as fallback_error2:\n", "                                    logger.warning(f\"All fallbacks failed for field {field} in offer {offer_id}: {fallback_error2}\")\n", "                                    continue\n", "                    except Exception as field_error:\n", "                        logger.warning(f\"Error processing field {field} for offer {offer_id}: {field_error}\")\n", "                        continue\n", "                \n", "                stored_count += 1\n", "                # Flush every 10 offers to avoid memory issues\n", "                if stored_count % 10 == 0:\n", "                    h5_file.flush()\n", "                    \n", "            except Exception as offer_error:\n", "                logger.warning(f\"Error storing offer {offer.get('id', 'unknown')}: {offer_error}\")\n", "                continue\n", "        \n", "        # Flush the file to disk\n", "        h5_file.flush()\n", "        \n", "        logger.info(f\"Stored {stored_count} job offers in H5 file for {country}\")\n", "        return True\n", "    except Exception as e:\n", "        logger.error(f\"Error storing job offers in H5 file for {country}: {e}\")\n", "        return False\n", "    finally:\n", "        # Make sure to close the file handle\n", "        if h5_file is not None and hasattr(h5_file, 'id') and h5_file.id.valid:\n", "            try:\n", "                h5_file.close()\n", "                logger.debug(f\"Closed H5 file for {country} after storing\")\n", "            except Exception as close_error:\n", "                logger.warning(f\"Error closing H5 file for {country}: {close_error}\")\n", "\n", "def get_job_offers_from_h5(country, num_offers=None, start=0, skip_translated=True):\n", "    \"\"\"\n", "    Get job offers from an H5 file.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        num_offers (int, optional): Number of job offers to retrieve. If None, uses the country-specific batch size.\n", "        start (int): Starting offset for pagination\n", "        skip_translated (bool): Whether to skip documents that already have translated fields\n", "        \n", "    Returns:\n", "        list: List of job offer documents\n", "    \"\"\"\n", "    try:\n", "        if not CONFIG['h5_storage']['enabled']:\n", "            logger.info(\"H5 storage is disabled, skipping\")\n", "            return []\n", "        \n", "        # Use country-specific batch size if not specified\n", "        if num_offers is None:\n", "            num_offers = get_batch_size(country)\n", "            logger.info(f\"Using country-specific batch size for {country}: {num_offers}\")\n", "        \n", "        # Check if the H5 file exists\n", "        file_path = get_h5_file_path(country)\n", "        if not os.path.exists(file_path):\n", "            logger.info(f\"H5 file for {country} does not exist: {file_path}\")\n", "            return []\n", "        \n", "        # Open the H5 file\n", "        h5_file = open_h5_file(country, mode='r')\n", "        if h5_file is None:\n", "            logger.error(f\"Failed to open H5 file for {country}\")\n", "            return []\n", "        \n", "        # Check if the job_offers group exists\n", "        if 'job_offers' not in h5_file:\n", "            logger.info(f\"No job offers found in H5 file for {country}\")\n", "            return []\n", "        \n", "        # Get the job_offers group\n", "        job_offers_group = h5_file['job_offers']\n", "        \n", "        # Get all offer IDs\n", "        offer_ids = list(job_offers_group.keys())\n", "        \n", "        # Apply pagination\n", "        end = start + num_offers if num_offers is not None else len(offer_ids)\n", "        paginated_ids = offer_ids[start:end]\n", "        \n", "        # Load job offers\n", "        job_offers = []\n", "        for offer_id in paginated_ids:\n", "            try:\n", "                # Convert offer_id to string if it's not already\n", "                if not isinstance(offer_id, (str, bytes)):\n", "                    offer_id = str(offer_id)\n", "                    \n", "                offer_group = job_offers_group[offer_id]\n", "                \n", "                # Create a dictionary for this offer\n", "                offer = {'id': offer_id if isinstance(offer_id, str) else offer_id.decode('utf-8')}\n", "                \n", "                # Load all fields\n", "                for field_name in offer_group.keys():\n", "                    try:\n", "                        # Load the dataset\n", "                        dataset = offer_group[field_name]\n", "                        value = dataset[()]\n", "                        \n", "                        # Convert bytes to string\n", "                        if isinstance(value, bytes):\n", "                            value = value.decode('utf-8')\n", "                        \n", "                        # Convert numeric strings back to numbers\n", "                        if field_name == 'entity_id' and isinstance(value, str) and value.isdigit():\n", "                            value = int(value)\n", "                        \n", "                        # Add to the offer dictionary\n", "                        offer[field_name] = value\n", "                    except Exception as field_error:\n", "                        logger.warning(f\"Error loading field {field_name} for offer {offer_id}: {field_error}\")\n", "                        continue\n", "                \n", "                # Check if this offer has translations\n", "                has_translations = any(target_field in offer for target_field in SOURCE_FIELDS.values())\n", "                \n", "                # Skip if it has translations and we're skipping translated documents\n", "                if skip_translated and has_translations:\n", "                    continue\n", "                \n", "                # Validate the offer\n", "                is_valid, reason = validate_job_offer(offer)\n", "                if not is_valid:\n", "                    logger.warning(f\"Skipping invalid offer from H5: {reason}\")\n", "                    continue\n", "                \n", "                job_offers.append(offer)\n", "            except Exception as offer_error:\n", "                logger.warning(f\"Error processing offer {offer_id} from H5: {offer_error}\")\n", "                continue\n", "        \n", "        logger.info(f\"Retrieved {len(job_offers)} job offers from H5 file for {country}\")\n", "        return job_offers\n", "    except Exception as e:\n", "        logger.error(f\"Error retrieving job offers from H5 file for {country}: {e}\")\n", "        return []\n", "\n", "def update_job_offers_in_h5(translated_offers, country):\n", "    \"\"\"\n", "    Update job offers in an H5 file with translations.\n", "    \n", "    Args:\n", "        translated_offers (list): List of job offers with translations\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        bool: True if successful, False otherwise\n", "    \"\"\"\n", "    try:\n", "        if not translated_offers:\n", "            logger.warning(\"No translated offers to update\")\n", "            return False\n", "        \n", "        if not CONFIG['h5_storage']['enabled']:\n", "            logger.info(\"H5 storage is disabled, skipping\")\n", "            return False\n", "        \n", "        # Store the translated offers in the H5 file\n", "        return store_job_offers_in_h5(translated_offers, country)\n", "    except Exception as e:\n", "        logger.error(f\"Error updating job offers in H5 file for {country}: {e}\")\n", "        return False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Solr Connection Functions\n", "\n", "These functions handle connecting to the Solr JobSearch cores and retrieving job offers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a connection pool for reusing Solr connections\n", "solr_connections = {}\n", "\n", "def get_solr_info():\n", "    \"\"\"\n", "    Get information about the Solr server.\n", "    \n", "    Returns:\n", "        dict: Solr server information\n", "    \"\"\"\n", "    try:\n", "        response = requests.get(f\"{SOLR_BASE_URL}admin/info/system?wt=json\", timeout=30)\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            logger.error(f\"Failed to get Solr info: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error getting Solr info: {e}\")\n", "        return None\n", "\n", "def get_jobsearch_connection(country, timeout=None):\n", "    \"\"\"\n", "    Get a connection to the JobSearch Solr core for the specified country.\n", "    Uses connection pooling to reuse existing connections.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        timeout (int, optional): Connection timeout in seconds. If None, uses the value from config.\n", "        \n", "    Returns:\n", "        pysolr.Solr: Solr connection\n", "    \"\"\"\n", "    try:\n", "        # Use timeout from config if not specified\n", "        if timeout is None:\n", "            timeout = CONFIG['solr']['connection']['timeout']\n", "            \n", "        # Check if we already have a connection for this country\n", "        if country in solr_connections:\n", "            logger.info(f\"Using existing connection for {country}\")\n", "            return solr_connections[country]\n", "        \n", "        # Use the correct core pattern: core_jobsearch_[country]\n", "        # Ensure the URL format is exactly as expected\n", "        solr_url = f\"{CONFIG['solr']['connection']['base_url']}core_jobsearch_{country}\"\n", "        logger.info(f\"Creating new connection to JobSearch Solr core at {solr_url}\")\n", "        \n", "        # Log the exact URL format that will be used for queries\n", "        sort_param = f\"{CONFIG['solr']['query']['sort_field']}+{CONFIG['solr']['query']['sort_order']}\"\n", "        rows = CONFIG['solr']['query']['rows_per_query']\n", "        example_query_url = f\"{solr_url}/select?q=*:*&fq=*:*&fl=id,entity_id&rows={rows}&start=0&sort={sort_param}&wt=json\"\n", "        logger.debug(f\"Example query URL: {example_query_url}\")\n", "        \n", "        # Create PySOLR connection with timeout\n", "        solr = pysolr.Solr(solr_url, timeout=timeout)\n", "        \n", "        # Store in connection pool\n", "        solr_connections[country] = solr\n", "        return solr\n", "    except Exception as e:\n", "        logger.error(f\"Error connecting to JobSearch Solr for {country}: {e}\")\n", "        return None\n", "\n", "def query_solr_direct(country, params=None):\n", "    \"\"\"\n", "    Query Solr directly using HTTP requests instead of PySOLR.\n", "    This is useful for testing and debugging Solr queries.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        params (dict): Query parameters\n", "        \n", "    Returns:\n", "        dict: Solr response as JSON\n", "    \"\"\"\n", "    try:\n", "        # Set default parameters if none provided\n", "        if params is None:\n", "            sort_param = f\"{CONFIG['solr']['query']['sort_field']} {CONFIG['solr']['query']['sort_order']}\"\n", "            params = {\n", "                'q': '*:*',\n", "                'fq': '*:*',\n", "                'fl': 'id,entity_id',\n", "                'rows': CONFIG['solr']['query']['rows_per_query'],\n", "                'start': 0,\n", "                'sort': sort_param,\n", "                'wt': 'json'\n", "            }\n", "        \n", "        # Construct the URL\n", "        solr_url = f\"{CONFIG['solr']['connection']['base_url']}core_jobsearch_{country}/select\"\n", "        \n", "        # Make the request\n", "        logger.info(f\"Making direct HTTP request to Solr: {solr_url}\")\n", "        response = requests.get(solr_url, params=params, timeout=CONFIG['solr']['connection']['timeout'])\n", "        \n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            # Parse the JSON response\n", "            result = response.json()\n", "            logger.info(f\"Direct Solr query successful. Found {result.get('response', {}).get('numFound', 0)} documents.\")\n", "            return result\n", "        else:\n", "            logger.error(f\"Direct Solr query failed with status code {response.status_code}: {response.text}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error in direct Solr query: {e}\")\n", "        return None\n", "\n", "def validate_job_offer(offer):\n", "    \"\"\"\n", "    Validate a job offer document to ensure it has all required fields and proper ID format.\n", "    \n", "    Args:\n", "        offer (dict): Job offer document\n", "        \n", "    Returns:\n", "        tuple: (is_valid, reason)\n", "    \"\"\"\n", "    # Validate ID format\n", "    if 'id' not in offer or not isinstance(offer['id'], str) or '/node/' not in offer['id']:\n", "        return False, f\"Invalid ID format: {offer.get('id', 'unknown')}\"\n", "    \n", "    # Validate entity_id field\n", "    if 'entity_id' not in offer:\n", "        return False, f\"Missing entity_id field for offer {offer['id']}\"\n", "    \n", "    # Cross-validate id and entity_id fields\n", "    try:\n", "        id_parts = offer['id'].split('/node/')\n", "        if len(id_parts) == 2 and id_parts[1].isdigit():\n", "            numeric_id = int(id_parts[1])\n", "            if numeric_id != offer['entity_id']:\n", "                return False, f\"Mismatched IDs: id={offer['id']}, entity_id={offer['entity_id']}\"\n", "        else:\n", "            return False, f\"Unparseable ID format: {offer['id']}\"\n", "    except Exception as e:\n", "        return False, f\"Error validating offer IDs: {e}\"\n", "    \n", "    # Check if all source fields exist\n", "    has_source_fields = all(source_field in offer for source_field in SOURCE_FIELDS.keys())\n", "    if not has_source_fields:\n", "        return False, f\"Missing source fields for offer {offer['id']}\"\n", "    \n", "    return <PERSON>, \"Valid\"\n", "\n", "def get_job_offers(country, num_offers=None, start=0, skip_translated=True):\n", "    \"\"\"\n", "    Get job offers from the JobSearch Solr core with direct query filtering for documents needing translation.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        num_offers (int, optional): Number of job offers to retrieve. If None, uses the country-specific batch size.\n", "        start (int): Starting offset for pagination\n", "        skip_translated (bool): Whether to skip documents that already have translated fields\n", "        \n", "    Returns:\n", "        list: List of job offer documents that need translation\n", "    \"\"\"\n", "    try:\n", "        # Use country-specific batch size if not specified\n", "        if num_offers is None:\n", "            num_offers = get_batch_size(country)\n", "            logger.info(f\"Using country-specific batch size for {country}: {num_offers}\")\n", "            \n", "        solr = get_jobsearch_connection(country)\n", "        if not solr:\n", "            logger.error(f\"Failed to get JobSearch Solr connection for {country}\")\n", "            return []\n", "        \n", "        # Fields to retrieve - we no longer need to include translated fields\n", "        # since we're filtering at the query level\n", "        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Prepare query parameters\n", "        sort_param = f\"{CONFIG['solr']['query']['sort_field']} {CONFIG['solr']['query']['sort_order']}\"\n", "        params = {\n", "            'q': '*:*',     # Main query\n", "            'fl': ','.join(fields),  # Fields to return\n", "            'rows': num_offers,      # Number of rows to return\n", "            'start': start,          # Starting offset\n", "            'sort': sort_param,      # Sort by configured field and order\n", "            'wt': 'json'             # Response format\n", "        }\n", "        \n", "        # Add filter query to exclude documents with translations if requested\n", "        if skip_translated:\n", "            # Create a filter query that excludes documents with any translated fields\n", "            translation_filters = []\n", "            for target_field in SOURCE_FIELDS.values():\n", "                translation_filters.append(f\"-{target_field}:[* TO *]\")\n", "            \n", "            # Combine filters with AND\n", "            params['fq'] = \" AND \".join(translation_filters)\n", "            logger.info(f\"Using filter query to exclude documents with translations: {params['fq']}\")\n", "        else:\n", "            # If not skipping translated documents, use a simple filter query\n", "            params['fq'] = '*:*'\n", "        \n", "        # Construct the exact URL for logging/debugging\n", "        solr_url = f\"{CONFIG['solr']['connection']['base_url']}core_jobsearch_{country}/select\"\n", "        query_params = '&'.join([f\"{k}={v}\" for k, v in params.items()])\n", "        full_url = f\"{solr_url}?{query_params}\"\n", "        logger.debug(f\"Full Solr query URL: {full_url}\")\n", "        \n", "        # Execute the query\n", "        logger.info(f\"Retrieving job offers from {country} (start={start}, rows={num_offers})\")\n", "        # Note: PySOLR's search method takes q as a positional argument and the rest as kwargs\n", "        q = params.pop('q')  # Remove q from params to pass it separately\n", "        results = solr.search(q, **params)\n", "        \n", "        # Convert to list of dictionaries\n", "        all_offers = [dict(doc) for doc in results]\n", "        logger.info(f\"Retrieved {len(all_offers)} job offers from {country} that need translation\")\n", "        \n", "        # We still need to validate the documents, but we don't need to check for translations\n", "        valid_offers = []\n", "        for offer in all_offers:\n", "            # Validate the job offer using our validation function\n", "            is_valid, reason = validate_job_offer(offer)\n", "            if not is_valid:\n", "                logger.warning(f\"Skipping offer: {reason}\")\n", "                continue\n", "            \n", "            valid_offers.append(offer)\n", "        \n", "        logger.info(f\"{len(valid_offers)} out of {len(all_offers)} job offers are valid and need translation\")\n", "        return valid_offers\n", "    except Exception as e:\n", "        logger.error(f\"Error retrieving job offers from {country}: {e}\")\n", "        return []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Batch Processing Functions\n", "\n", "These functions handle preparing and processing batch files for the OpenAI Batch API."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_batch_file(job_offers, country):\n", "    \"\"\"\n", "    Prepare a batch file for OpenAI Batch API with the correct format.\n", "    \n", "    Args:\n", "        job_offers (list): List of job offer documents\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        str: Path to the batch file\n", "    \"\"\"\n", "    try:\n", "        if not job_offers:\n", "            logger.warning(\"No job offers to process\")\n", "            return None\n", "        \n", "        # Create a unique batch file name\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        batch_file_path = os.path.join(BATCH_DIR, f\"{country}_batch_{timestamp}.jsonl\")\n", "        \n", "        # Prepare batch requests\n", "        batch_requests = []\n", "        \n", "        # Get OpenAI configuration\n", "        model = get_openai_model()\n", "        temperature = CONFIG['openai']['translation']['temperature']\n", "        system_prompt = CONFIG['openai']['translation']['system_prompt']\n", "        user_prompt_template = CONFIG['openai']['translation']['user_prompt_template']\n", "        \n", "        for offer in job_offers:\n", "            offer_id = offer.get('id', str(uuid.uuid4()))\n", "            \n", "            # Process each source field\n", "            for source_field, target_field in SOURCE_FIELDS.items():\n", "                if source_field in offer:\n", "                    value = offer[source_field]\n", "                    \n", "                    # Handle list values\n", "                    if isinstance(value, list):\n", "                        if not value:\n", "                            continue\n", "                        text = value[0]  # Use the first item\n", "                    elif isinstance(value, str):\n", "                        text = value\n", "                    else:\n", "                        continue\n", "                    \n", "                    # Skip empty text\n", "                    if not text or not text.strip():\n", "                        continue\n", "                    \n", "                    # Decode HTML entities\n", "                    decoded_text = html.unescape(text)\n", "                    \n", "                    # Create a unique custom ID for this request that avoids parsing issues\n", "                    # Use a separator that won't appear in the field name\n", "                    # For source fields with underscores, use a special format\n", "                    if '_' in source_field:\n", "                        # Use a special format for fields with underscores\n", "                        field_part = source_field.replace('_', '-')\n", "                        custom_id = f\"{offer_id}__FIELD__{field_part}\"\n", "                    else:\n", "                        custom_id = f\"{offer_id}_{source_field}\"\n", "                    \n", "                    # Format the user prompt with the text\n", "                    user_prompt = user_prompt_template.format(text=decoded_text)\n", "                    \n", "                    # Create the batch request with body as an object (not a string)\n", "                    # Include the word \"json\" in the messages to satisfy the requirement\n", "                    batch_request = {\n", "                        \"custom_id\": custom_id,\n", "                        \"method\": \"POST\",\n", "                        \"url\": \"/v1/chat/completions\",\n", "                        \"body\": {\n", "                            \"model\": model,\n", "                            \"messages\": [\n", "                                {\"role\": \"system\", \"content\": system_prompt},\n", "                                {\"role\": \"user\", \"content\": user_prompt}\n", "                            ],\n", "                            \"response_format\": {\"type\": \"json_object\"},\n", "                            \"temperature\": temperature\n", "                        }\n", "                    }\n", "                    \n", "                    batch_requests.append(batch_request)\n", "        \n", "        # Write batch requests to file\n", "        if batch_requests:\n", "            with open(batch_file_path, 'w', encoding='utf-8', newline='\\n') as f:\n", "                for request in batch_requests:\n", "                    f.write(json.dumps(request, ensure_ascii=False) + '\\n')\n", "            \n", "            logger.info(f\"Created batch file with {len(batch_requests)} requests: {batch_file_path}\")\n", "            return batch_file_path\n", "        else:\n", "            logger.warning(\"No valid requests to process\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error preparing batch file: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_batch_job_info(batch_job_id, batch_file_path, country):\n", "    \"\"\"\n", "    Save batch job information to a JSON file for later processing.\n", "    \n", "    Args:\n", "        batch_job_id (str): The batch job ID\n", "        batch_file_path (str): Path to the original batch file\n", "        country (str): The country code\n", "        \n", "    Returns:\n", "        str: Path to the info file\n", "    \"\"\"\n", "    try:\n", "        # Create the pending jobs directory if it doesn't exist\n", "        pending_dir = os.path.join(os.path.dirname(batch_file_path), \"pending_jobs\")\n", "        os.makedirs(pending_dir, exist_ok=True)\n", "        \n", "        # Create a unique filename based on the batch job ID\n", "        info_file_path = os.path.join(pending_dir, f\"{batch_job_id}.json\")\n", "        \n", "        # Create the info dictionary\n", "        info = {\n", "            \"batch_job_id\": batch_job_id,\n", "            \"batch_file_path\": batch_file_path,\n", "            \"country\": country,\n", "            \"created_at\": datetime.now().isoformat(),\n", "            \"status\": \"pending\"\n", "        }\n", "        \n", "        # Save the info to a file\n", "        with open(info_file_path, 'w', encoding='utf-8') as f:\n", "            json.dump(info, f, indent=2)\n", "        \n", "        logger.info(f\"Saved batch job info to {info_file_path}\")\n", "        return info_file_path\n", "    except Exception as e:\n", "        logger.error(f\"Error saving batch job info: {e}\")\n", "        return None\n", "\n", "def process_batch(batch_file_path, client, country=None, wait_for_completion=False):\n", "    \"\"\"\n", "    Process a batch file using OpenAI's Batch API.\n", "    \n", "    Args:\n", "        batch_file_path (str): Path to the batch file\n", "        client (OpenAI): OpenAI client\n", "        country (str, optional): Country code for the batch\n", "        wait_for_completion (bool): Whether to wait for the batch to complete\n", "        \n", "    Returns:\n", "        str: Path to the output file if wait_for_completion=True and batch completes,\n", "             or path to the batch job info file if wait_for_completion=False\n", "    \"\"\"\n", "    try:\n", "        if not batch_file_path or not os.path.exists(batch_file_path):\n", "            logger.error(\"Invalid batch file path\")\n", "            return None\n", "        \n", "        logger.info(f\"Processing batch file: {batch_file_path}\")\n", "        \n", "        # Get batch API configuration\n", "        max_wait_time = CONFIG['openai']['batch_api']['max_wait_time']\n", "        wait_interval = CONFIG['openai']['batch_api']['wait_interval']\n", "        retry_count = CONFIG['openai']['batch_api']['retry_count']\n", "        retry_delay = CONFIG['openai']['batch_api']['retry_delay']\n", "        \n", "        # Step 1: Upload the batch file to OpenAI\n", "        uploaded_file = None\n", "        for attempt in range(retry_count + 1):\n", "            try:\n", "                logger.info(f\"Uploading batch file to OpenAI: {batch_file_path}\")\n", "                with open(batch_file_path, 'rb') as f:\n", "                    uploaded_file = client.files.create(\n", "                        file=f,\n", "                        purpose=\"batch\"\n", "                    )\n", "                logger.info(f\"Successfully uploaded file with ID: {uploaded_file.id}\")\n", "                break\n", "            except Exception as e:\n", "                if attempt < retry_count:\n", "                    logger.warning(f\"File upload failed (attempt {attempt+1}/{retry_count+1}): {e}\")\n", "                    logger.info(f\"Retrying in {retry_delay} seconds...\")\n", "                    time.sleep(retry_delay)\n", "                else:\n", "                    logger.error(f\"File upload failed after {retry_count+1} attempts: {e}\")\n", "                    return None\n", "        \n", "        if not uploaded_file:\n", "            logger.error(\"Failed to upload batch file\")\n", "            return None\n", "            \n", "        # Step 2: Create a batch job with the uploaded file\n", "        batch_job = None\n", "        for attempt in range(retry_count + 1):\n", "            try:\n", "                logger.info(f\"Creating batch job with file ID: {uploaded_file.id}\")\n", "                batch_job = client.batches.create(\n", "                    input_file_id=uploaded_file.id,\n", "                    endpoint=\"/v1/chat/completions\",\n", "                    completion_window=\"24h\"\n", "                )\n", "                logger.info(f\"Successfully created batch job with ID: {batch_job.id}\")\n", "                break\n", "            except Exception as e:\n", "                if attempt < retry_count:\n", "                    logger.warning(f\"Batch job creation failed (attempt {attempt+1}/{retry_count+1}): {e}\")\n", "                    logger.info(f\"Retrying in {retry_delay} seconds...\")\n", "                    time.sleep(retry_delay)\n", "                else:\n", "                    logger.error(f\"Batch job creation failed after {retry_count+1} attempts: {e}\")\n", "                    return None\n", "        \n", "        if not batch_job:\n", "            logger.error(\"Failed to create batch job\")\n", "            return None\n", "        \n", "        job_id = batch_job.id\n", "        logger.info(f\"Created batch job with ID: {job_id}\")\n", "        \n", "        # If we're not waiting for completion, save the job info and return\n", "        if not wait_for_completion:\n", "            info_file_path = save_batch_job_info(job_id, batch_file_path, country)\n", "            logger.info(f\"Batch job {job_id} submitted and will be processed later. Info saved to {info_file_path}\")\n", "            return info_file_path\n", "        \n", "        # Step 3: Wait for the job to complete (only if wait_for_completion=True)\n", "        elapsed_time = 0\n", "        \n", "        while elapsed_time < max_wait_time:\n", "            # Get the job status\n", "            job_status = client.batches.retrieve(job_id)\n", "            status = job_status.status\n", "            \n", "            # Log the current status and request counts\n", "            request_counts = job_status.request_counts\n", "            logger.info(f\"Batch job status: {status} (Total: {request_counts.total}, Completed: {request_counts.completed}, Failed: {request_counts.failed})\")\n", "            \n", "            if status == \"completed\":\n", "                # Step 4: Get the output file ID\n", "                output_file_id = job_status.output_file_id\n", "                if not output_file_id:\n", "                    logger.error(\"No output file ID found in completed batch\")\n", "                    return None\n", "                \n", "                logger.info(f\"Batch completed with output file ID: {output_file_id}\")\n", "                \n", "                # Check if there were any errors\n", "                if job_status.error_file_id:\n", "                    logger.warning(f\"Batch completed with errors. Error file ID: {job_status.error_file_id}\")\n", "                    # Optionally download and process error file here\n", "                \n", "                # Step 5: Download the output file content\n", "                for attempt in range(retry_count + 1):\n", "                    try:\n", "                        logger.info(f\"Downloading output file content for file ID: {output_file_id}\")\n", "                        file_response = client.files.content(output_file_id)\n", "                        \n", "                        # Save the output file\n", "                        output_file_path = batch_file_path.replace(\".jsonl\", \"_output.jsonl\")\n", "                        with open(output_file_path, 'wb') as f:\n", "                            # Write the content to the file\n", "                            for chunk in file_response.iter_bytes():\n", "                                f.write(chunk)\n", "                        \n", "                        logger.info(f\"Downloaded output file: {output_file_path}\")\n", "                        return output_file_path\n", "                    except Exception as e:\n", "                        if attempt < retry_count:\n", "                            logger.warning(f\"Error downloading output file (attempt {attempt+1}/{retry_count+1}): {e}\")\n", "                            logger.info(f\"Retrying in {retry_delay} seconds...\")\n", "                            time.sleep(retry_delay)\n", "                        else:\n", "                            logger.error(f\"Error downloading output file after {retry_count+1} attempts: {e}\")\n", "                            return None\n", "            elif status == \"failed\":\n", "                error_message = getattr(job_status, 'error', 'No error details available')\n", "                logger.error(f\"Batch job failed: {error_message}\")\n", "                return None\n", "            elif status == \"expired\":\n", "                logger.error(\"Bat<PERSON> job expired (not completed within 24-hour window)\")\n", "                return None\n", "            elif status == \"cancelled\":\n", "                logger.error(\"<PERSON><PERSON> job was cancelled\")\n", "                return None\n", "            elif status in [\"validating\", \"in_progress\", \"finalizing\", \"cancelling\"]:\n", "                # Wait and check again\n", "                time.sleep(wait_interval)\n", "                elapsed_time += wait_interval\n", "            else:\n", "                logger.error(f\"Unknown batch job status: {status}\")\n", "                return None\n", "        \n", "        # If we reach here, the job timed out but we'll save it for later processing\n", "        logger.warning(f\"Batch job monitoring timed out after {max_wait_time} seconds, saving for later processing\")\n", "        info_file_path = save_batch_job_info(job_id, batch_file_path, country)\n", "        return None\n", "    except Exception as e:\n", "        logger.error(f\"Error processing batch: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_batch_results(output_file_path, job_offers):\n", "    \"\"\"\n", "    Process the results of a batch job and map them back to the job offers.\n", "    \n", "    Args:\n", "        output_file_path (str): Path to the output file\n", "        job_offers (list): List of job offer documents\n", "        \n", "    Returns:\n", "        list: List of job offers with translations\n", "    \"\"\"\n", "    try:\n", "        if not output_file_path or not os.path.exists(output_file_path):\n", "            logger.error(\"Invalid output file path\")\n", "            return []\n", "        \n", "        # Create a map of job offers by ID for quick lookup\n", "        job_offers_map = {offer['id']: offer for offer in job_offers}\n", "        \n", "        # Read the output file\n", "        with open(output_file_path, 'r', encoding='utf-8') as f:\n", "            results = [json.loads(line) for line in f if line.strip()]\n", "        \n", "        logger.info(f\"Processing {len(results)} batch results\")\n", "        \n", "        # Process each result\n", "        translated_offers = []\n", "        for result in results:\n", "            # Check if the request has an error\n", "            if result.get('error'):\n", "                logger.warning(f\"Request failed: {result.get('error')}\")\n", "                continue\n", "                \n", "            # Check if the response exists and has a valid status code\n", "            response = result.get('response')\n", "            if not response or response.get('status_code') != 200:\n", "                status_code = response.get('status_code') if response else 'No response'\n", "                logger.warning(f\"Request failed with status code: {status_code}\")\n", "                continue\n", "            \n", "            # Get the custom ID to map back to the job offer\n", "            custom_id = result.get('custom_id')\n", "            if not custom_id or '_' not in custom_id:\n", "                logger.warning(f\"Invalid custom ID: {custom_id}\")\n", "                continue\n", "            \n", "            # Parse the custom ID to get the job offer ID and field\n", "            try:\n", "                # Handle different custom ID formats\n", "                if '__FIELD__' in custom_id:\n", "                    # This is our special format for fields with underscores\n", "                    offer_id, field_part = custom_id.split('__FIELD__')\n", "                    # Convert hyphens back to underscores\n", "                    source_field = field_part.replace('-', '_')\n", "                elif '_sm_field_offre_' in custom_id:\n", "                    # Handle the case where the field name contains underscores (old format)\n", "                    parts = custom_id.split('_sm_field_offre_')\n", "                    offer_id = parts[0]\n", "                    source_field = 'sm_field_offre_' + parts[1]\n", "                else:\n", "                    # Default case - split on the last underscore\n", "                    offer_id, source_field = custom_id.rsplit('_', 1)\n", "                \n", "                # Get the job offer\n", "                offer = job_offers_map.get(offer_id)\n", "                \n", "                # If not found, try alternative formats\n", "                if not offer:\n", "                    # Try with just the entity_id part\n", "                    if '/' in offer_id:\n", "                        entity_id = offer_id.split('/')[-1]\n", "                        # Look for any offer with this entity_id\n", "                        for potential_offer in job_offers:\n", "                            if str(potential_offer.get('entity_id')) == entity_id or potential_offer.get('id').endswith('/' + entity_id):\n", "                                offer = potential_offer\n", "                                break\n", "                \n", "                if not offer:\n", "                    logger.warning(f\"Job offer not found for ID: {offer_id} (from custom_id: {custom_id})\")\n", "                    continue\n", "            except Exception as e:\n", "                logger.warning(f\"Error parsing custom ID {custom_id}: {e}\")\n", "                continue\n", "            \n", "            # Get the target field\n", "            target_field = SOURCE_FIELDS.get(source_field)\n", "            if not target_field:\n", "                logger.warning(f\"Target field not found for source field: {source_field}\")\n", "                continue\n", "            \n", "            # Get the response body\n", "            response_body = response.get('body')\n", "            if not response_body:\n", "                logger.warning(f\"No response body for request: {custom_id}\")\n", "                continue\n", "            \n", "            # Parse the response body\n", "            try:\n", "                # Extract the assistant's message from the choices\n", "                choices = response_body.get('choices', [])\n", "                if not choices or len(choices) == 0:\n", "                    logger.warning(f\"No choices found in response for request: {custom_id}\")\n", "                    continue\n", "                    \n", "                # Get the message content from the first choice\n", "                message = choices[0].get('message', {})\n", "                content = message.get('content', '')\n", "                \n", "                if not content:\n", "                    logger.warning(f\"No content found in response message for request: {custom_id}\")\n", "                    continue\n", "                    \n", "                # Parse the JSON content to extract the translation\n", "                try:\n", "                    content_json = json.loads(content)\n", "                    translation = content_json.get('translation')\n", "                    \n", "                    if not translation:\n", "                        logger.warning(f\"No translation field found in JSON response for request: {custom_id}\")\n", "                        continue\n", "                        \n", "                    # Add the translation to the job offer as an array to match the original format\n", "                    offer[target_field] = [translation]\n", "                    \n", "                    # Add the job offer to the list of translated offers if not already there\n", "                    if offer not in translated_offers:\n", "                        translated_offers.append(offer)\n", "                        \n", "                except json.JSONDecodeError as e:\n", "                    logger.warning(f\"Failed to parse JSON content from response for request {custom_id}: {e}\")\n", "                    continue\n", "            except Exception as e:\n", "                logger.warning(f\"Failed to process response for request {custom_id}: {e}\")\n", "                continue\n", "        \n", "        logger.info(f\"Processed {len(translated_offers)} job offers with translations\")\n", "        return translated_offers\n", "    except Exception as e:\n", "        logger.error(f\"Error processing batch results: {e}\")\n", "        return []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_translations_to_json(translated_offers, country):\n", "    \"\"\"\n", "    Save translated job offers to a JSON file.\n", "    \n", "    Args:\n", "        translated_offers (list): List of job offers with translations\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        str: Path to the saved file\n", "    \"\"\"\n", "    try:\n", "        if not translated_offers:\n", "            logger.warning(\"No translated offers to save\")\n", "            return None\n", "        \n", "        # Create a unique file name\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        file_path = os.path.join(RESULTS_DIR, f\"{country}_translated_offers_{timestamp}.json\")\n", "        \n", "        # Save the translated offers\n", "        with open(file_path, 'w', encoding='utf-8') as f:\n", "            json.dump(translated_offers, f, ensure_ascii=False, indent=2)\n", "        \n", "        logger.info(f\"Saved {len(translated_offers)} translated offers to {file_path}\")\n", "        return file_path\n", "    except Exception as e:\n", "        logger.error(f\"Error saving translations to JSON: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Solr Update Functions\n", "\n", "These functions handle updating the Solr database with the translated fields.\n", "\n", "**Note**: This implementation uses the working atomic update logic from SOLR_UPDATE_TESTING.ipynb,\n", "which uses direct HTTP requests with atomic update syntax instead of PySOLR's add() method."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_atomic_update_document(doc_id, translations):\n", "    \"\"\"\n", "    Prepare an update document for Solr using atomic updates.\n", "    \n", "    Args:\n", "        doc_id (str): Document ID\n", "        translations (dict): Dictionary of translated fields\n", "        \n", "    Returns:\n", "        dict: Update document with atomic update syntax\n", "    \"\"\"\n", "    # Create update document with ID field\n", "    update_doc = {\n", "        \"id\": doc_id\n", "    }\n", "    \n", "    # Add translated fields using atomic update syntax\n", "    for field, value in translations.items():\n", "        # Ensure the value is an array to match the original format\n", "        if isinstance(value, list):\n", "            update_doc[field] = {\"set\": value}\n", "        else:\n", "            update_doc[field] = {\"set\": [value]}\n", "    \n", "    return update_doc\n", "\n", "def update_solr_with_translations(translated_offers, country, batch_size=None):\n", "    \"\"\"\n", "    Update Solr with the translated fields using atomic updates via direct HTTP requests.\n", "    This implementation uses the working logic from SOLR_UPDATE_TESTING.ipynb.\n", "    \n", "    Args:\n", "        translated_offers (list): List of job offers with translations\n", "        country (str): Country code\n", "        batch_size (int, optional): Number of documents to update in one batch. If None, uses the configured value.\n", "        \n", "    Returns:\n", "        bool: True if successful, False otherwise\n", "    \"\"\"\n", "    try:\n", "        if not translated_offers:\n", "            logger.warning(\"No translated offers to update\")\n", "            return False\n", "        \n", "        # Use configured batch size if not specified\n", "        if batch_size is None:\n", "            batch_size = CONFIG['solr']['update']['batch_size']\n", "            logger.info(f\"Using configured batch size for Solr updates: {batch_size}\")\n", "        \n", "        logger.info(f\"Updating {len(translated_offers)} job offers in Solr for {country} using atomic updates\")\n", "        \n", "        # Get Solr update configuration\n", "        commit_within = CONFIG['solr']['update']['commit_within']\n", "        final_commit = CONFIG['solr']['update']['final_commit']\n", "        timeout = CONFIG['solr']['connection']['timeout']\n", "        \n", "        # Construct the update URL\n", "        core_name = f\"core_jobsearch_{country}\"\n", "        update_url = f\"{CONFIG['solr']['connection']['base_url']}{core_name}/update\"\n", "        \n", "        # Process in batches\n", "        total_offers = len(translated_offers)\n", "        success_count = 0\n", "        failure_count = 0\n", "        consecutive_errors = 0\n", "        max_consecutive_errors = CONFIG['error_handling']['max_consecutive_errors']\n", "        continue_on_error = CONFIG['error_handling']['continue_on_error']\n", "        error_threshold_percentage = CONFIG['error_handling']['error_threshold_percentage']\n", "        \n", "        for i in range(0, total_offers, batch_size):\n", "            batch = translated_offers[i:i+batch_size]\n", "            update_batch = []\n", "            \n", "            for offer in batch:\n", "                # Validate the offer\n", "                is_valid, reason = validate_job_offer(offer)\n", "                if not is_valid:\n", "                    logger.warning(f\"Skipping invalid offer: {reason}\")\n", "                    failure_count += 1\n", "                    continue\n", "                \n", "                # Prepare translations dictionary\n", "                translations = {}\n", "                for source_field, target_field in SOURCE_FIELDS.items():\n", "                    if target_field in offer:\n", "                        # Ensure the value is an array to match the original format\n", "                        if isinstance(offer[target_field], list):\n", "                            translations[target_field] = offer[target_field]\n", "                        else:\n", "                            translations[target_field] = [offer[target_field]]\n", "                \n", "                # Skip if no translated fields\n", "                if not translations:\n", "                    logger.warning(f\"Skipping offer with no translated fields: {offer['id']}\")\n", "                    failure_count += 1\n", "                    continue\n", "                \n", "                # Prepare atomic update document\n", "                update_doc = prepare_atomic_update_document(offer[\"id\"], translations)\n", "                update_batch.append(update_doc)\n", "            \n", "            # Update documents in batch using direct HTTP requests with atomic updates\n", "            if update_batch:\n", "                try:\n", "                    # Prepare parameters\n", "                    params = {\n", "                        'commit': 'true' if final_commit else 'false',\n", "                        'wt': 'json'\n", "                    }\n", "                    \n", "                    # Add commitWithin if specified\n", "                    if commit_within:\n", "                        params['commitWithin'] = str(commit_within)\n", "                    \n", "                    # Prepare headers\n", "                    headers = {\n", "                        'Content-Type': 'application/json'\n", "                    }\n", "                    \n", "                    # Log the update URL for debugging\n", "                    param_str = \"&\".join([f\"{k}={v}\" for k, v in params.items()])\n", "                    full_url = f\"{update_url}?{param_str}\"\n", "                    logger.debug(f\"Solr update URL: {full_url}\")\n", "                    logger.debug(f\"Updating batch of {len(update_batch)} documents\")\n", "                    \n", "                    # Make the HTTP request with atomic update syntax\n", "                    response = requests.post(\n", "                        update_url,\n", "                        params=params,\n", "                        headers=headers,\n", "                        data=json.dumps(update_batch),\n", "                        timeout=timeout\n", "                    )\n", "                    \n", "                    # Check if the request was successful\n", "                    if response.status_code == 200:\n", "                        response_json = response.json()\n", "                        if response_json.get('responseHeader', {}).get('status') == 0:\n", "                            success_count += len(update_batch)\n", "                            consecutive_errors = 0  # Reset consecutive errors counter on success\n", "                            logger.info(f\"Successfully updated batch of {len(update_batch)} documents\")\n", "                        else:\n", "                            logger.error(f\"Solr returned error status: {response_json}\")\n", "                            failure_count += len(update_batch)\n", "                            consecutive_errors += 1\n", "                    else:\n", "                        logger.error(f\"HTTP request failed with status {response.status_code}: {response.text}\")\n", "                        failure_count += len(update_batch)\n", "                        consecutive_errors += 1\n", "                        \n", "                except Exception as e:\n", "                    logger.error(f\"Error updating batch: {e}\")\n", "                    failure_count += len(update_batch)\n", "                    consecutive_errors += 1\n", "                \n", "                # Check if we should abort due to too many consecutive errors\n", "                if consecutive_errors >= max_consecutive_errors:\n", "                    logger.error(f\"Aborting after {consecutive_errors} consecutive errors\")\n", "                    break\n", "                \n", "                # Check if we should abort due to error threshold percentage\n", "                if success_count + failure_count > 0:\n", "                    error_rate = (failure_count / (success_count + failure_count)) * 100\n", "                    if error_rate > error_threshold_percentage:\n", "                        logger.error(f\"Aborting due to error rate exceeding threshold: {error_rate:.1f}% > {error_threshold_percentage}%\")\n", "                        break\n", "                \n", "                # Check if we should continue after errors\n", "                if consecutive_errors > 0 and not continue_on_error:\n", "                    logger.error(\"Aborting due to error (continue_on_error=False)\")\n", "                    break\n", "        \n", "        # Final commit if configured and not already committed\n", "        if final_commit and success_count > 0 and not CONFIG['solr']['update'].get('commit_within'):\n", "            try:\n", "                commit_params = {'commit': 'true', 'wt': 'json'}\n", "                commit_response = requests.post(update_url, params=commit_params, timeout=timeout)\n", "                if commit_response.status_code == 200:\n", "                    logger.info(\"Final commit successful\")\n", "                else:\n", "                    logger.error(f\"Final commit failed: {commit_response.status_code} - {commit_response.text}\")\n", "            except Exception as e:\n", "                logger.error(f\"Error during final commit: {e}\")\n", "        \n", "        logger.info(f\"Solr update complete: {success_count} successful, {failure_count} failed\")\n", "        return success_count > 0\n", "    except Exception as e:\n", "        logger.error(f\"Error updating <PERSON><PERSON> with translations: {e}\")\n", "        return False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Main Pipeline Functions\n", "\n", "These functions orchestrate the entire translation pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_pending_batch_jobs():\n", "    \"\"\"\n", "    Check for pending batch jobs and process any that are completed.\n", "    \"\"\"\n", "    try:\n", "        print(\"\\n🔍 Checking for pending batch jobs...\")\n", "        \n", "        # Create the OpenAI client\n", "        client = OpenAI()\n", "        \n", "        # Check for pending jobs\n", "        counts = check_pending_batch_jobs(client)\n", "        \n", "        if counts.get(\"total\", 0) == 0:\n", "            print(\"No pending batch jobs found.\")\n", "            return\n", "        \n", "        print(f\"\\nFound {counts.get('total', 0)} pending batch jobs:\")\n", "        print(f\"- Completed: {counts.get('completed', 0)}\")\n", "        print(f\"- In progress: {counts.get('in_progress', 0)}\")\n", "        print(f\"- Failed: {counts.get('failed', 0)}\")\n", "        print(f\"- Other: {counts.get('other', 0)}\")\n", "        \n", "        # Process completed jobs\n", "        if counts.get('completed', 0) > 0:\n", "            print(f\"\\n✅ {counts.get('completed', 0)} jobs completed and processed.\")\n", "            print(\"Check the 'batch_files/completed_jobs' directory for details.\")\n", "        \n", "        # Show in-progress jobs\n", "        if counts.get('in_progress', 0) > 0:\n", "            print(f\"\\n⏳ {counts.get('in_progress', 0)} jobs still in progress.\")\n", "            print(\"Run this function again later to check their status.\")\n", "        \n", "        # Show failed jobs\n", "        if counts.get('failed', 0) > 0:\n", "            print(f\"\\n❌ {counts.get('failed', 0)} jobs failed.\")\n", "            print(\"Check the 'batch_files/failed_jobs' directory for details.\")\n", "    except Exception as e:\n", "        print(f\"\\n❌ Error checking pending batch jobs: {e}\")\n", "\n", "def run_batch_translation(country, batch_size=None, update_solr=False, wait_for_completion=False):\n", "    \"\"\"\n", "    Run the batch translation pipeline for a country.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        batch_size (int, optional): Number of job offers to process in one batch. If None, uses the country-specific batch size.\n", "        update_solr (bool): Whether to update Solr with the translations\n", "        wait_for_completion (bool): Whether to wait for the batch job to complete\n", "        \n", "    Returns:\n", "        tuple: (success, results_path)\n", "    \"\"\"\n", "    try:\n", "        # Create a unique run ID for this pipeline execution\n", "        run_id = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        \n", "        # Use country-specific batch size if not specified\n", "        if batch_size is None:\n", "            batch_size = get_batch_size(country)\n", "            \n", "        logger.info(f\"Starting batch translation for {country} with batch size {batch_size}\")\n", "        \n", "        # Take snapshot of pipeline start\n", "        take_snapshot(\"pipeline_start\", [], {\n", "            \"country\": country,\n", "            \"batch_size\": batch_size,\n", "            \"update_solr\": update_solr,\n", "            \"wait_for_completion\": wait_for_completion,\n", "            \"model\": get_openai_model(),\n", "            \"temperature\": CONFIG['openai']['translation']['temperature']\n", "        }, country=country, run_id=run_id)\n", "        \n", "        # Step 1: Get job offers that need translation\n", "        # First try to get from H5 storage if enabled\n", "        job_offers = []\n", "        if CONFIG['h5_storage']['enabled']:\n", "            logger.info(f\"Trying to get job offers from H5 storage for {country}\")\n", "            job_offers = get_job_offers_from_h5(country, num_offers=batch_size, start=0, skip_translated=True)\n", "            \n", "        # If not enough offers found in H5 or H5 is disabled, get more from Solr\n", "        if len(job_offers) < batch_size:\n", "            remaining = batch_size - len(job_offers)\n", "            if job_offers:\n", "                logger.info(f\"Found {len(job_offers)} offers in H5, getting {remaining} more from Solr for {country}\")\n", "            else:\n", "                logger.info(f\"No offers found in H5, getting {remaining} from Solr for {country}\")\n", "                \n", "            solr_offers = get_job_offers(country, num_offers=remaining, start=0, skip_translated=True)\n", "            \n", "            # Store new offers in H5 if enabled\n", "            if solr_offers and CONFIG['h5_storage']['enabled']:\n", "                logger.info(f\"Storing {len(solr_offers)} new job offers in H5 for {country}\")\n", "                store_job_offers_in_h5(solr_offers, country)\n", "            \n", "            # Combine offers from H5 and Solr\n", "            job_offers.extend(solr_offers)\n", "        \n", "        if not job_offers:\n", "            logger.info(f\"No job offers found for {country} that need translation\")\n", "            return False, None\n", "        \n", "        # Step 2: Prepare batch file\n", "        batch_file_path = prepare_batch_file(job_offers, country)\n", "        if not batch_file_path:\n", "            logger.error(\"Failed to prepare batch file\")\n", "            return False, None\n", "        \n", "        # Take snapshot of batch preparation\n", "        # Count the number of requests in the batch file\n", "        request_count = 0\n", "        try:\n", "            with open(batch_file_path, 'r', encoding='utf-8') as f:\n", "                request_count = sum(1 for _ in f)\n", "        except Exception as e:\n", "            logger.warning(f\"Error counting requests in batch file: {e}\")\n", "            \n", "        take_snapshot(\"translation_prep\", job_offers, {\n", "            \"batch_file\": os.path.basename(batch_file_path),\n", "            \"job_offers_count\": len(job_offers),\n", "            \"request_count\": request_count,\n", "            \"avg_description_length\": sum(len(o.get(\"sm_field_offre_description_poste\", [\"\"])[0]) \n", "                                       for o in job_offers if o.get(\"sm_field_offre_description_poste\")) / \n", "                                       max(sum(1 for o in job_offers if o.get(\"sm_field_offre_description_poste\")), 1),\n", "            \"avg_profile_length\": sum(len(o.get(\"sm_field_offre_profil\", [\"\"])[0]) \n", "                                   for o in job_offers if o.get(\"sm_field_offre_profil\")) / \n", "                                   max(sum(1 for o in job_offers if o.get(\"sm_field_offre_profil\")), 1)\n", "        }, country=country, run_id=run_id)\n", "        \n", "        # Step 3: Process batch\n", "        client = OpenAI()\n", "        result = process_batch(batch_file_path, client, country=country, wait_for_completion=wait_for_completion)\n", "        \n", "        if not result:\n", "            logger.error(\"Failed to process batch\")\n", "            return False, None\n", "        \n", "        # If we're not waiting for completion, return success with the info file path\n", "        if not wait_for_completion:\n", "            logger.info(f\"Batch job submitted successfully for {country}. It will be processed in the background.\")\n", "            logger.info(f\"Run the 'check_pending_batch_jobs()' function later to check its status.\")\n", "            return True, result\n", "        \n", "        # If we are waiting for completion, continue with processing the results\n", "        output_file_path = result\n", "        \n", "        # Step 4: Process batch results\n", "        translated_offers = process_batch_results(output_file_path, job_offers)\n", "        if not translated_offers:\n", "            logger.error(\"Failed to process batch results\")\n", "            return False, None\n", "        \n", "        # Take snapshot of translation results\n", "        take_snapshot(\"translation\", translated_offers, {\n", "            \"translated_count\": len(translated_offers),\n", "            \"original_count\": len(job_offers),\n", "            \"success_rate\": len(translated_offers) / max(len(job_offers), 1) * 100,\n", "            \"avg_description_translation_length\": sum(len(o.get(\"tr_field_offre_description_poste\", [\"\"])[0]) \n", "                                                for o in translated_offers if o.get(\"tr_field_offre_description_poste\")) / \n", "                                                max(sum(1 for o in translated_offers if o.get(\"tr_field_offre_description_poste\")), 1),\n", "            \"avg_profile_translation_length\": sum(len(o.get(\"tr_field_offre_profil\", [\"\"])[0]) \n", "                                            for o in translated_offers if o.get(\"tr_field_offre_profil\")) / \n", "                                            max(sum(1 for o in translated_offers if o.get(\"tr_field_offre_profil\")), 1)\n", "        }, country=country, run_id=run_id)\n", "        \n", "        # Step 5: Save translations to JSON\n", "        results_path = save_translations_to_json(translated_offers, country)\n", "        if not results_path:\n", "            logger.error(\"Failed to save translations to JSON\")\n", "            return False, None\n", "        \n", "        # Step 6: Update H5 storage with translations if enabled\n", "        if CONFIG['h5_storage']['enabled']:\n", "            logger.info(f\"Updating H5 storage with {len(translated_offers)} translated offers for {country}\")\n", "            update_result = update_job_offers_in_h5(translated_offers, country)\n", "            if not update_result:\n", "                logger.warning(f\"Failed to update H5 storage for {country}\")\n", "        \n", "        # Step 7: Update Solr (if requested)\n", "        if update_solr:\n", "            # Use the configured batch size for Solr updates\n", "            solr_batch_size = CONFIG['solr']['update']['batch_size']\n", "            result = update_solr_with_translations(translated_offers, country, batch_size=solr_batch_size)\n", "            \n", "            # Take snapshot of Solr update\n", "            take_snapshot(\"solr_update\", translated_offers, {\n", "                \"update_count\": len(translated_offers),\n", "                \"success\": result is True,\n", "                \"batch_size\": solr_batch_size,\n", "                \"commit_within\": CONFIG['solr']['update']['commit_within'],\n", "                \"final_commit\": CONFIG['solr']['update']['final_commit']\n", "            }, country=country, run_id=run_id)\n", "            \n", "            if not result:\n", "                logger.error(\"Failed to update Solr with translations\")\n", "                return False, results_path\n", "        \n", "        # Take snapshot of pipeline completion\n", "        take_snapshot(\"pipeline_complete\", [], {\n", "            \"success\": True,\n", "            \"total_time\": (datetime.now() - datetime.strptime(run_id, \"%Y%m%d_%H%M%S\")).total_seconds(),\n", "            \"results_path\": os.path.basename(results_path) if results_path else None,\n", "            \"job_offers_processed\": len(job_offers),\n", "            \"translations_created\": len(translated_offers),\n", "            \"solr_updated\": update_solr\n", "        }, country=country, run_id=run_id)\n", "        \n", "        logger.info(f\"Batch translation completed successfully for {country}\")\n", "        return True, results_path\n", "    except Exception as e:\n", "        logger.error(f\"Error running batch translation: {e}\")\n", "        return False, None\n", "    finally:\n", "        # Close H5 file for this country if open\n", "        if CONFIG['h5_storage']['enabled']:\n", "            close_h5_file(country)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. User Interface and Testing\n", "\n", "These cells provide a user interface for running the translation pipeline and testing its components."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_direct_solr_query():\n", "    \"\"\"\n", "    Test the direct Solr query function.\n", "    \"\"\"\n", "    print(\"Testing direct Solr query...\\n\")\n", "    \n", "    # Test with a specific country\n", "    country = 'algerie'  # Change to the country you want to test\n", "    \n", "    # Set up query parameters exactly matching the working URL format\n", "    params = {\n", "        'q': '*:*',\n", "        'fq': '*:*',\n", "        'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',\n", "        'rows': 5,\n", "        'start': 0,\n", "        'sort': 'entity_id asc',\n", "        'wt': 'json'\n", "    }\n", "    \n", "    # Construct the URL manually for logging\n", "    solr_url = f\"{SOLR_BASE_URL}core_jobsearch_{country}/select\"\n", "    query_params = '&'.join([f\"{k}={v}\" for k, v in params.items()])\n", "    full_url = f\"{solr_url}?{query_params}\"\n", "    print(f\"Full query URL: {full_url}\\n\")\n", "    \n", "    # Execute the query\n", "    result = query_solr_direct(country, params)\n", "    \n", "    if result:\n", "        response = result.get('response', {})\n", "        num_found = response.get('numFound', 0)\n", "        docs = response.get('docs', [])\n", "        \n", "        print(f\"Query successful! Found {num_found} documents.\\n\")\n", "        \n", "        if docs:\n", "            print(f\"First {len(docs)} documents:\")\n", "            for i, doc in enumerate(docs):\n", "                print(f\"\\nDocument {i+1}:\")\n", "                print(f\"ID: {doc.get('id', 'unknown')}\")\n", "                print(f\"Entity ID: {doc.get('entity_id', 'unknown')}\")\n", "                \n", "                # Check if translated fields already exist\n", "                has_translations = any(target_field in doc for target_field in SOURCE_FIELDS.values())\n", "                print(f\"Has translations: {has_translations}\")\n", "                \n", "                # Show a sample of the source fields\n", "                for source_field in SOURCE_FIELDS.keys():\n", "                    if source_field in doc:\n", "                        value = doc[source_field]\n", "                        if isinstance(value, list) and value:\n", "                            sample = value[0][:100] + \"...\" if len(value[0]) > 100 else value[0]\n", "                            print(f\"\\n{source_field}: {sample}\")\n", "    else:\n", "        print(\"Query failed. Check the logs for details.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_filter_query():\n", "    \"\"\"\n", "    Test the filter query for excluding documents with translations.\n", "    \"\"\"\n", "    print(\"Testing filter query for excluding documents with translations...\\n\")\n", "    \n", "    # Test with a specific country\n", "    country = 'algerie'  # Change to the country you want to test\n", "    \n", "    # Create filter query\n", "    translation_filters = []\n", "    for target_field in SOURCE_FIELDS.values():\n", "        translation_filters.append(f\"-{target_field}:[* TO *]\")\n", "    \n", "    filter_query = \" AND \".join(translation_filters)\n", "    print(f\"Filter query: {filter_query}\\n\")\n", "    \n", "    # Set up query parameters\n", "    params = {\n", "        'q': '*:*',\n", "        'fq': filter_query,\n", "        'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',\n", "        'rows': 5,\n", "        'start': 0,\n", "        'sort': 'entity_id asc',\n", "        'wt': 'json'\n", "    }\n", "    \n", "    # Construct the URL manually for logging\n", "    solr_url = f\"{SOLR_BASE_URL}core_jobsearch_{country}/select\"\n", "    query_params = '&'.join([f\"{k}={v}\" for k, v in params.items()])\n", "    full_url = f\"{solr_url}?{query_params}\"\n", "    print(f\"Full query URL: {full_url}\\n\")\n", "    \n", "    # Execute the query\n", "    result = query_solr_direct(country, params)\n", "    \n", "    if result:\n", "        response = result.get('response', {})\n", "        num_found = response.get('numFound', 0)\n", "        docs = response.get('docs', [])\n", "        \n", "        print(f\"Query successful! Found {num_found} documents that need translation.\\n\")\n", "        \n", "        if docs:\n", "            print(f\"First {len(docs)} documents:\")\n", "            for i, doc in enumerate(docs):\n", "                print(f\"\\nDocument {i+1}:\")\n", "                print(f\"ID: {doc.get('id', 'unknown')}\")\n", "                print(f\"Entity ID: {doc.get('entity_id', 'unknown')}\")\n", "                \n", "                # Verify no translated fields exist\n", "                has_translations = any(target_field in doc for target_field in SOURCE_FIELDS.values())\n", "                print(f\"Has translations: {has_translations} (should be False)\")\n", "    else:\n", "        print(\"Query failed. Check the logs for details.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the direct Solr query test\n", "test_direct_solr_query()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the filter query\n", "test_filter_query()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set up OpenAI API key\n", "import os\n", "from getpass import getpass\n", "\n", "# Ask for API key if not already set\n", "if not os.environ.get(\"OPENAI_API_KEY\"):\n", "    api_key = getpass(\"Enter your OpenAI API key: \")\n", "    os.environ[\"OPENAI_API_KEY\"] = api_key\n", "    print(\"API key set successfully!\")\n", "else:\n", "    print(\"OpenAI API key already set in environment variables.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration Management Functions\n", "def display_config():\n", "    \"\"\"\n", "    Display the current configuration.\n", "    \"\"\"\n", "    print(\"\\n📋 Current Configuration:\\n\")\n", "    print(f\"Version: {CONFIG['general']['config_version']}\")\n", "    print(f\"Last Updated: {CONFIG['general']['last_updated']}\\n\")\n", "    \n", "    print(\"General Settings:\")\n", "    print(f\"  Default Bat<PERSON> Size: {CONFIG['general']['default_batch_size']}\\n\")\n", "    \n", "    print(\"Country-Specific Batch Sizes:\")\n", "    for country, size in CONFIG['country_specific']['batch_sizes'].items():\n", "        print(f\"  {country}: {size}\")\n", "    print()\n", "    \n", "    print(\"OpenAI Settings:\")\n", "    print(f\"  Model: {CONFIG['openai']['models']['default']}\")\n", "    print(f\"  Temperature: {CONFIG['openai']['translation']['temperature']}\")\n", "    print(f\"  Batch API Max Wait Time: {CONFIG['openai']['batch_api']['max_wait_time']} seconds\")\n", "    print(f\"  Retry Count: {CONFIG['openai']['batch_api']['retry_count']}\\n\")\n", "    \n", "    print(\"<PERSON><PERSON> Settings:\")\n", "    print(f\"  Connection Timeout: {CONFIG['solr']['connection']['timeout']} seconds\")\n", "    print(f\"  Rows Per Query: {CONFIG['solr']['query']['rows_per_query']}\")\n", "    print(f\"  Sort Field: {CONFIG['solr']['query']['sort_field']} {CONFIG['solr']['query']['sort_order']}\")\n", "    print(f\"  Update Batch Size: {CONFIG['solr']['update']['batch_size']}\")\n", "    print(f\"  Commit Within: {CONFIG['solr']['update']['commit_within']} ms\")\n", "    print(f\"  Final Commit: {CONFIG['solr']['update']['final_commit']}\\n\")\n", "    \n", "    print(\"Error Handling:\")\n", "    print(f\"  Max Consecutive Errors: {CONFIG['error_handling']['max_consecutive_errors']}\")\n", "    print(f\"  Error Threshold Percentage: {CONFIG['error_handling']['error_threshold_percentage']}%\")\n", "    print(f\"  Continue on Error: {CONFIG['error_handling']['continue_on_error']}\\n\")\n", "    \n", "    print(\"Logging:\")\n", "    print(f\"  Level: {CONFIG['logging']['level']}\")\n", "    print(f\"  File Logging: {CONFIG['logging']['file']['enabled']}\")\n", "    if CONFIG['logging']['file']['enabled']:\n", "        print(f\"  Log File: {CONFIG['logging']['file']['path']}\")\n", "\n", "def edit_config():\n", "    \"\"\"\n", "    Edit the configuration.\n", "    \"\"\"\n", "    global CONFIG\n", "    \n", "    print(\"\\n⚙️ Configuration Editor\\n\")\n", "    print(\"Select a section to edit:\")\n", "    print(\"1. <PERSON> Settings\")\n", "    print(\"2. Country-Specific Batch Sizes\")\n", "    print(\"3. OpenAI Settings\")\n", "    print(\"4. <PERSON><PERSON>\")\n", "    print(\"5. <PERSON><PERSON><PERSON>\")\n", "    print(\"6. Logging\")\n", "    print(\"7. Save and Exit\")\n", "    print(\"8. Exit Without Saving\")\n", "    \n", "    choice = input(\"\\nEnter your choice (1-8): \")\n", "    \n", "    if choice == \"1\":\n", "        # Edit General Settings\n", "        print(\"\\nEditing General Settings:\\n\")\n", "        CONFIG['general']['default_batch_size'] = int(input(f\"Default <PERSON><PERSON> Size [{CONFIG['general']['default_batch_size']}]: \") or CONFIG['general']['default_batch_size'])\n", "        edit_config()  # Return to the menu\n", "    \n", "    elif choice == \"2\":\n", "        # Edit Country-Specific Batch Sizes\n", "        print(\"\\nEditing Country-Specific Batch Sizes:\\n\")\n", "        print(\"Current country-specific batch sizes:\")\n", "        for i, (country, size) in enumerate(CONFIG['country_specific']['batch_sizes'].items()):\n", "            print(f\"{i+1}. {country}: {size}\")\n", "        print(f\"{len(CONFIG['country_specific']['batch_sizes'])+1}. Add a new country\")\n", "        print(f\"{len(CONFIG['country_specific']['batch_sizes'])+2}. Return to main menu\")\n", "        \n", "        sub_choice = input(\"\\nEnter your choice: \")\n", "        try:\n", "            sub_choice = int(sub_choice)\n", "            if 1 <= sub_choice <= len(CONFIG['country_specific']['batch_sizes']):\n", "                # Edit existing country\n", "                country = list(CONFIG['country_specific']['batch_sizes'].keys())[sub_choice-1]\n", "                new_size = int(input(f\"New batch size for {country} [{CONFIG['country_specific']['batch_sizes'][country]}]: \") or CONFIG['country_specific']['batch_sizes'][country])\n", "                CONFIG['country_specific']['batch_sizes'][country] = new_size\n", "            elif sub_choice == len(CONFIG['country_specific']['batch_sizes'])+1:\n", "                # Add a new country\n", "                print(\"\\nAvailable countries:\")\n", "                available_countries = [c for c in COUNTRIES if c not in CONFIG['country_specific']['batch_sizes']]\n", "                if available_countries:\n", "                    for i, country in enumerate(available_countries):\n", "                        print(f\"{i+1}. {country}\")\n", "                    country_idx = int(input(\"\\nSelect a country: \")) - 1\n", "                    if 0 <= country_idx < len(available_countries):\n", "                        country = available_countries[country_idx]\n", "                        size = int(input(f\"Batch size for {country} [{CONFIG['general']['default_batch_size']}]: \") or CONFIG['general']['default_batch_size'])\n", "                        CONFIG['country_specific']['batch_sizes'][country] = size\n", "                    else:\n", "                        print(\"Invalid selection\")\n", "                else:\n", "                    print(\"All countries already have specific batch sizes.\")\n", "        except ValueError:\n", "            print(\"Invalid input. Please enter a number.\")\n", "        \n", "        edit_config()  # Return to the menu\n", "    \n", "    elif choice == \"3\":\n", "        # Edit OpenAI Settings\n", "        print(\"\\nEditing OpenAI Settings:\\n\")\n", "        print(\"1. Change Model\")\n", "        print(\"2. Change Temperature\")\n", "        print(\"3. Edit System Prompt\")\n", "        print(\"4. Edit User Prompt Template\")\n", "        print(\"5. Edit Batch API Settings\")\n", "        print(\"6. Return to main menu\")\n", "        \n", "        sub_choice = input(\"\\nEnter your choice (1-6): \")\n", "        \n", "        if sub_choice == \"1\":\n", "            # Change Model\n", "            print(\"\\nAvailable models:\")\n", "            print(f\"1. {CONFIG['openai']['models']['default']} (current)\")\n", "            for i, model in enumerate(CONFIG['openai']['models']['alternatives']):\n", "                print(f\"{i+2}. {model}\")\n", "            model_idx = int(input(\"\\nSelect a model: \")) - 1\n", "            if model_idx == 0:\n", "                pass  # Keep current model\n", "            elif 1 <= model_idx <= len(CONFIG['openai']['models']['alternatives']):\n", "                # Swap current model with selected alternative\n", "                current = CONFIG['openai']['models']['default']\n", "                new_model = CONFIG['openai']['models']['alternatives'][model_idx-1]\n", "                CONFIG['openai']['models']['default'] = new_model\n", "                CONFIG['openai']['models']['alternatives'][model_idx-1] = current\n", "                print(f\"Model changed to {new_model}\")\n", "            else:\n", "                print(\"Invalid selection\")\n", "        \n", "        elif sub_choice == \"2\":\n", "            # Change Temperature\n", "            temp = float(input(f\"Temperature (0.0-1.0) [{CONFIG['openai']['translation']['temperature']}]: \") or CONFIG['openai']['translation']['temperature'])\n", "            if 0.0 <= temp <= 1.0:\n", "                CONFIG['openai']['translation']['temperature'] = temp\n", "                print(f\"Temperature set to {temp}\")\n", "            else:\n", "                print(\"Invalid temperature. Must be between 0.0 and 1.0.\")\n", "        \n", "        elif sub_choice == \"3\":\n", "            # Edit System Prompt\n", "            print(f\"\\nCurrent system prompt: {CONFIG['openai']['translation']['system_prompt']}\")\n", "            new_prompt = input(\"New system prompt (press Enter to keep current): \")\n", "            if new_prompt:\n", "                CONFIG['openai']['translation']['system_prompt'] = new_prompt\n", "                print(\"System prompt updated.\")\n", "        \n", "        elif sub_choice == \"4\":\n", "            # Edit User Prompt Template\n", "            print(f\"\\nCurrent user prompt template: {CONFIG['openai']['translation']['user_prompt_template']}\")\n", "            print(\"Note: The template must include {text} placeholder.\")\n", "            new_template = input(\"New user prompt template (press Enter to keep current): \")\n", "            if new_template:\n", "                if \"{text}\" in new_template:\n", "                    CONFIG['openai']['translation']['user_prompt_template'] = new_template\n", "                    print(\"User prompt template updated.\")\n", "                else:\n", "                    print(\"Error: Template must include {text} placeholder.\")\n", "        \n", "        elif sub_choice == \"5\":\n", "            # Edit Batch API Settings\n", "            print(\"\\nEditing Batch API Settings:\\n\")\n", "            CONFIG['openai']['batch_api']['max_wait_time'] = int(input(f\"Max Wait Time (seconds) [{CONFIG['openai']['batch_api']['max_wait_time']}]: \") or CONFIG['openai']['batch_api']['max_wait_time'])\n", "            CONFIG['openai']['batch_api']['wait_interval'] = int(input(f\"Wait Interval (seconds) [{CONFIG['openai']['batch_api']['wait_interval']}]: \") or CONFIG['openai']['batch_api']['wait_interval'])\n", "            CONFIG['openai']['batch_api']['retry_count'] = int(input(f\"Retry Count [{CONFIG['openai']['batch_api']['retry_count']}]: \") or CONFIG['openai']['batch_api']['retry_count'])\n", "            CONFIG['openai']['batch_api']['retry_delay'] = int(input(f\"Retry Delay (seconds) [{CONFIG['openai']['batch_api']['retry_delay']}]: \") or CONFIG['openai']['batch_api']['retry_delay'])\n", "        \n", "        edit_config()  # Return to the menu\n", "    \n", "    elif choice == \"4\":\n", "        # Edit Solr Set<PERSON>s\n", "        print(\"\\nEditing Solr Settings:\\n\")\n", "        print(\"1. <PERSON> Settings\")\n", "        print(\"2. Query <PERSON>\")\n", "        print(\"3. Update Settings\")\n", "        print(\"4. Return to main menu\")\n", "        \n", "        sub_choice = input(\"\\nEnter your choice (1-4): \")\n", "        \n", "        if sub_choice == \"1\":\n", "            # Edit Connection Settings\n", "            print(\"\\nEditing Connection Settings:\\n\")\n", "            CONFIG['solr']['connection']['timeout'] = int(input(f\"Connection Timeout (seconds) [{CONFIG['solr']['connection']['timeout']}]: \") or CONFIG['solr']['connection']['timeout'])\n", "            new_url = input(f\"Base URL [{CONFIG['solr']['connection']['base_url']}]: \")\n", "            if new_url:\n", "                CONFIG['solr']['connection']['base_url'] = new_url\n", "        \n", "        elif sub_choice == \"2\":\n", "            # Edit Query Settings\n", "            print(\"\\nEditing Query Settings:\\n\")\n", "            CONFIG['solr']['query']['rows_per_query'] = int(input(f\"Rows Per Query [{CONFIG['solr']['query']['rows_per_query']}]: \") or CONFIG['solr']['query']['rows_per_query'])\n", "            CONFIG['solr']['query']['sort_field'] = input(f\"Sort Field [{CONFIG['solr']['query']['sort_field']}]: \") or CONFIG['solr']['query']['sort_field']\n", "            sort_order = input(f\"Sort Order (asc/desc) [{CONFIG['solr']['query']['sort_order']}]: \") or CONFIG['solr']['query']['sort_order']\n", "            if sort_order in ['asc', 'desc']:\n", "                CONFIG['solr']['query']['sort_order'] = sort_order\n", "            else:\n", "                print(\"Invalid sort order. Must be 'asc' or 'desc'.\")\n", "        \n", "        elif sub_choice == \"3\":\n", "            # Edit Update Settings\n", "            print(\"\\nEditing Update Settings:\\n\")\n", "            CONFIG['solr']['update']['batch_size'] = int(input(f\"Update Batch Size [{CONFIG['solr']['update']['batch_size']}]: \") or CONFIG['solr']['update']['batch_size'])\n", "            CONFIG['solr']['update']['commit_within'] = int(input(f\"Commit Within (ms) [{CONFIG['solr']['update']['commit_within']}]: \") or CONFIG['solr']['update']['commit_within'])\n", "            final_commit = input(f\"Final Commit (true/false) [{CONFIG['solr']['update']['final_commit']}]: \").lower() or str(CONFIG['solr']['update']['final_commit']).lower()\n", "            if final_commit in ['true', 'false']:\n", "                CONFIG['solr']['update']['final_commit'] = final_commit == 'true'\n", "            else:\n", "                print(\"Invalid input. Must be 'true' or 'false'.\")\n", "        \n", "        edit_config()  # Return to the menu\n", "    \n", "    elif choice == \"5\":\n", "        # Edit Error Handling\n", "        print(\"\\nEditing Error Handling Settings:\\n\")\n", "        CONFIG['error_handling']['max_consecutive_errors'] = int(input(f\"Max Consecutive Errors [{CONFIG['error_handling']['max_consecutive_errors']}]: \") or CONFIG['error_handling']['max_consecutive_errors'])\n", "        CONFIG['error_handling']['error_threshold_percentage'] = int(input(f\"Error Threshold Percentage [{CONFIG['error_handling']['error_threshold_percentage']}]: \") or CONFIG['error_handling']['error_threshold_percentage'])\n", "        continue_on_error = input(f\"Continue on Error (true/false) [{CONFIG['error_handling']['continue_on_error']}]: \").lower() or str(CONFIG['error_handling']['continue_on_error']).lower()\n", "        if continue_on_error in ['true', 'false']:\n", "            CONFIG['error_handling']['continue_on_error'] = continue_on_error == 'true'\n", "        else:\n", "            print(\"Invalid input. Must be 'true' or 'false'.\")\n", "        \n", "        edit_config()  # Return to the menu\n", "    \n", "    elif choice == \"6\":\n", "        # Edit Logging\n", "        print(\"\\nEditing Logging Settings:\\n\")\n", "        print(\"Available log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL\")\n", "        level = input(f\"Log Level [{CONFIG['logging']['level']}]: \").upper() or CONFIG['logging']['level']\n", "        if level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:\n", "            CONFIG['logging']['level'] = level\n", "        else:\n", "            print(\"Invalid log level.\")\n", "        \n", "        file_logging = input(f\"Enable File Logging (true/false) [{CONFIG['logging']['file']['enabled']}]: \").lower() or str(CONFIG['logging']['file']['enabled']).lower()\n", "        if file_logging in ['true', 'false']:\n", "            CONFIG['logging']['file']['enabled'] = file_logging == 'true'\n", "            if file_logging == 'true':\n", "                CONFIG['logging']['file']['path'] = input(f\"Log File Path [{CONFIG['logging']['file']['path']}]: \") or CONFIG['logging']['file']['path']\n", "                CONFIG['logging']['file']['max_size'] = int(input(f\"Max Log Size (bytes) [{CONFIG['logging']['file']['max_size']}]: \") or CONFIG['logging']['file']['max_size'])\n", "                CONFIG['logging']['file']['backup_count'] = int(input(f\"Backup Count [{CONFIG['logging']['file']['backup_count']}]: \") or CONFIG['logging']['file']['backup_count'])\n", "        else:\n", "            print(\"Invalid input. Must be 'true' or 'false'.\")\n", "        \n", "        edit_config()  # Return to the menu\n", "    \n", "    elif choice == \"7\":\n", "        # Save and Exit\n", "        save_config(CONFIG)\n", "        print(\"Configuration saved.\")\n", "        return\n", "    \n", "    elif choice == \"8\":\n", "        # Exit Without Saving\n", "        print(\"Exiting without saving changes.\")\n", "        # Reload the configuration to discard changes\n", "        CONFIG = load_config()\n", "        return\n", "    \n", "    else:\n", "        print(\"Invalid choice.\")\n", "        edit_config()  # Return to the menu\n", "\n", "# Run the translation pipeline\n", "def run_pipeline():\n", "    # Ask for country\n", "    print(\"\\n🌍 Available countries:\")\n", "    for i, country in enumerate(COUNTRIES):\n", "        batch_size = get_batch_size(country)\n", "        print(f\"{i+1}. {country} (batch size: {batch_size})\")\n", "    \n", "    try:\n", "        country_idx = int(input(f\"\\nSelect a country (1-{len(COUNTRIES)}): \")) - 1\n", "        if country_idx < 0 or country_idx >= len(COUNTRIES):\n", "            print(\"Invalid selection\")\n", "            return\n", "        \n", "        country = COUNTRIES[country_idx]\n", "        default_batch_size = get_batch_size(country)\n", "        \n", "        # Ask for batch size\n", "        batch_size_input = input(f\"Enter batch size (default: {default_batch_size}): \")\n", "        batch_size = int(batch_size_input) if batch_size_input else None  # None will use the country-specific batch size\n", "        \n", "        # Ask whether to update Sol<PERSON>\n", "        update_solr_input = input(\"Update Solr with translations? (y/n): \").lower()\n", "        update_solr = update_solr_input == 'y'\n", "        \n", "        # Ask whether to wait for completion\n", "        wait_input = input(\"Wait for batch job to complete? (y/n): \").lower()\n", "        wait_for_completion = wait_input == 'y'\n", "        \n", "        # Run the pipeline\n", "        print(f\"\\n🚀 Running translation pipeline for {country}...\")\n", "        if batch_size:\n", "            print(f\"Using custom batch size: {batch_size}\")\n", "        else:\n", "            print(f\"Using country-specific batch size: {default_batch_size}\")\n", "        print(f\"OpenAI Model: {get_openai_model()}\")\n", "        print(f\"Update Solr: {update_solr}\")\n", "        print(f\"Wait for completion: {wait_for_completion}\")\n", "        \n", "        success, results_path = run_batch_translation(country, batch_size=batch_size, update_solr=update_solr, wait_for_completion=wait_for_completion)\n", "        \n", "        if success:\n", "            print(f\"\\n✅ Translation pipeline completed successfully!\")\n", "            if results_path:\n", "                print(f\"Results saved to: {results_path}\")\n", "        else:\n", "            print(f\"\\n❌ Translation pipeline failed. Check the logs for details.\")\n", "    except ValueError:\n", "        print(\"Invalid input. Please enter a number.\")\n", "\n", "# Main menu function\n", "def main_menu():\n", "    try:\n", "        print(\"\\n🔄 Job Offer Translation Pipeline\\n\")\n", "        print(\"1. Run Translation Pipeline\")\n", "        print(\"2. Display Configuration\")\n", "        print(\"3. Edit Configuration\")\n", "        print(\"4. Test Direct Solr Query\")\n", "        print(\"5. Test Filter Query\")\n", "        print(\"6. H5 Storage Management\")\n", "        print(\"7. <PERSON> Pending Batch Jobs\")\n", "        print(\"8. Exit\")\n", "        \n", "        choice = input(\"\\nEnter your choice (1-8): \")\n", "        \n", "        if choice == \"1\":\n", "            run_pipeline()\n", "            main_menu()\n", "        elif choice == \"2\":\n", "            display_config()\n", "            main_menu()\n", "        elif choice == \"3\":\n", "            edit_config()\n", "            main_menu()\n", "        elif choice == \"4\":\n", "            test_direct_solr_query()\n", "            main_menu()\n", "        elif choice == \"5\":\n", "            test_filter_query()\n", "            main_menu()\n", "        elif choice == \"6\":\n", "            h5_storage_menu()\n", "            main_menu()\n", "        elif choice == \"7\":\n", "            check_pending_batch_jobs()\n", "            main_menu()\n", "        elif choice == \"8\":\n", "            print(\"Exiting...\")\n", "            return\n", "        else:\n", "            print(\"Invalid choice.\")\n", "            main_menu()\n", "    finally:\n", "        # Clean up H5 files when exiting\n", "        if 'h5_storage' in CONFIG and CONFIG['h5_storage']['enabled']:\n", "            count = close_all_h5_files()\n", "            if count > 0:\n", "                print(f\"\\nClosed {count} H5 files.\")\n", "\n", "# H5 storage management menu\n", "def h5_storage_menu():\n", "    \"\"\"\n", "    Display the H5 storage management menu and handle user input.\n", "    \"\"\"\n", "    while True:\n", "        print(\"\\n\" + \"=\" * 50)\n", "        print(\"💾 H5 Storage Management\")\n", "        print(\"=\" * 50)\n", "        print(f\"H5 storage is currently {'enabled' if CONFIG['h5_storage']['enabled'] else 'disabled'}\")\n", "        print(\"1. List H5 Files\")\n", "        print(\"2. View H5 File Contents\")\n", "        print(\"3. Close All H5 Files\")\n", "        print(\"4. Toggle H5 Storage\")\n", "        print(\"0. Back to Main Menu\")\n", "        \n", "        choice = input(\"\\nEnter your choice (0-4): \")\n", "        \n", "        if choice == \"1\":\n", "            list_h5_files()\n", "        elif choice == \"2\":\n", "            view_h5_file_contents()\n", "        elif choice == \"3\":\n", "            count = close_all_h5_files()\n", "            print(f\"\\nClosed {count} H5 files.\")\n", "        elif choice == \"4\":\n", "            CONFIG['h5_storage']['enabled'] = not CONFIG['h5_storage']['enabled']\n", "            save_config(CONFIG)\n", "            print(f\"\\nH5 storage is now {'enabled' if CONFIG['h5_storage']['enabled'] else 'disabled'}.\")\n", "        elif choice == \"0\":\n", "            break\n", "        else:\n", "            print(\"\\n❌ Invalid choice. Please try again.\")\n", "\n", "def list_h5_files():\n", "    \"\"\"\n", "    List all H5 files in the H5 storage directory.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\" * 50)\n", "    print(\"📁 H5 Files\")\n", "    print(\"=\" * 50)\n", "    \n", "    h5_files_list = [f for f in os.listdir(H5_DIR) if f.endswith('.h5')]\n", "    \n", "    if not h5_files_list:\n", "        print(\"No H5 files found.\")\n", "        return\n", "    \n", "    print(f\"Found {len(h5_files_list)} H5 files:\")\n", "    for i, file_name in enumerate(h5_files_list):\n", "        file_path = os.path.join(H5_DIR, file_name)\n", "        file_size = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB\n", "        print(f\"{i+1}. {file_name} ({file_size:.2f} MB)\")\n", "\n", "def view_h5_file_contents():\n", "    \"\"\"\n", "    View the contents of an H5 file.\n", "    \"\"\"\n", "    h5_files_list = [f for f in os.listdir(H5_DIR) if f.endswith('.h5')]\n", "    \n", "    if not h5_files_list:\n", "        print(\"\\nNo H5 files found.\")\n", "        return\n", "    \n", "    print(\"\\n\" + \"=\" * 50)\n", "    print(\"📁 H5 Files\")\n", "    print(\"=\" * 50)\n", "    \n", "    for i, file_name in enumerate(h5_files_list):\n", "        print(f\"{i+1}. {file_name}\")\n", "    \n", "    try:\n", "        choice = int(input(\"\\nEnter the number of the file to view (0 to cancel): \"))\n", "        \n", "        if choice == 0:\n", "            return\n", "        \n", "        if choice < 1 or choice > len(h5_files_list):\n", "            print(\"\\n❌ Invalid choice.\")\n", "            return\n", "        \n", "        file_name = h5_files_list[choice - 1]\n", "        file_path = os.path.join(H5_DIR, file_name)\n", "        \n", "        # Extract country from file name\n", "        country = file_name.split('.')[0]\n", "        \n", "        # Open the H5 file\n", "        with h5py.File(file_path, 'r') as h5_file:\n", "            # Check if the job_offers group exists\n", "            if 'job_offers' not in h5_file:\n", "                print(f\"\\nNo job offers found in {file_name}.\")\n", "                return\n", "            \n", "            # Get the job_offers group\n", "            job_offers_group = h5_file['job_offers']\n", "            \n", "            # Get all offer IDs\n", "            offer_ids = list(job_offers_group.keys())\n", "            \n", "            print(f\"\\nFound {len(offer_ids)} job offers in {file_name}.\")\n", "            \n", "            # Count offers with translations\n", "            translated_count = 0\n", "            for offer_id in offer_ids:\n", "                offer_group = job_offers_group[offer_id]\n", "                has_translations = any(target_field in offer_group for target_field in SOURCE_FIELDS.values())\n", "                if has_translations:\n", "                    translated_count += 1\n", "            \n", "            print(f\"Translated: {translated_count} ({translated_count/len(offer_ids)*100:.2f}%)\")\n", "            print(f\"Untranslated: {len(offer_ids) - translated_count} ({(len(offer_ids) - translated_count)/len(offer_ids)*100:.2f}%)\")\n", "            \n", "            # Ask if user wants to view a specific offer\n", "            view_offer = input(\"\\nView a specific offer? (y/n): \")\n", "            if view_offer.lower() == 'y':\n", "                offer_index = int(input(f\"Enter offer index (1-{len(offer_ids)}): \")) - 1\n", "                if offer_index < 0 or offer_index >= len(offer_ids):\n", "                    print(\"\\n❌ Invalid index.\")\n", "                    return\n", "                \n", "                offer_id = offer_ids[offer_index]\n", "                offer_group = job_offers_group[offer_id]\n", "                \n", "                print(f\"\\nOffer ID: {offer_id}\")\n", "                print(\"=\" * 50)\n", "                \n", "                for field_name in offer_group.keys():\n", "                    value = offer_group[field_name][()]\n", "                    if isinstance(value, bytes):\n", "                        value = value.decode('utf-8')\n", "                    \n", "                    # Truncate long values\n", "                    if len(str(value)) > 100:\n", "                        print(f\"{field_name}: {str(value)[:100]}...\")\n", "                    else:\n", "                        print(f\"{field_name}: {value}\")\n", "    except Exception as e:\n", "        print(f\"\\n❌ Error viewing H5 file: {e}\")\n", "\n", "# Import menu functions\n", "try:\n", "    from menu_functions import run_interactive_menu\n", "    print(\"✅ Menu functions successfully imported\")\n", "except ImportError:\n", "    print(\"❌ Menu functions not found. Make sure menu_functions.py is in the same directory.\")\n", "    \n", "# Import Solr test functions\n", "try:\n", "    from solr_test_functions import test_solr_connection\n", "    print(\"✅ Solr test functions successfully imported\")\n", "except ImportError:\n", "    print(\"❌ Solr test functions not found. Make sure solr_test_functions.py is in the same directory.\")\n", "    # Create a simple fallback function\n", "    def test_solr_connection():\n", "        print(\"\\nSolr test function not available. Please create solr_test_functions.py first.\")\n", "        \n", "# Import Solr update functions\n", "try:\n", "    from solr_update_functions import update_solr_with_translations, test_solr_update, verify_solr_update\n", "    print(\"✅ Solr update functions successfully imported\")\n", "    # Replace the existing update_solr_with_translations function with the imported one\n", "    print(\"✅ Replaced existing Solr update function with improved version\")\n", "except ImportError:\n", "    print(\"❌ Solr update functions not found. Make sure solr_update_functions.py is in the same directory.\")\n", "\n", "# Run the interactive menu\n", "run_interactive_menu(COUNTRIES, CONFIG, run_pipeline, check_pending_batch_jobs, \n", "                    test_solr_connection, display_config, edit_config,\n", "                    generate_pipeline_report, analyze_pipeline_performance)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}