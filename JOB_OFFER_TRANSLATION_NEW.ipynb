# Import necessary libraries
try:
    import json
    import logging
    import os
    import time
    import re
    import html
    import shutil
    import glob
    import statistics
    from datetime import datetime
    import uuid
    print("✅ Basic Python libraries successfully imported")
except ImportError as e:
    print(f"❌ Error importing basic libraries: {e}")
    
# Create pipeline_monitor.py if it doesn't exist
try:
    from pipeline_monitor import take_snapshot, generate_pipeline_report, analyze_pipeline_performance
    print("✅ Pipeline monitoring system successfully imported")
except ImportError:
    print("⚠️ Pipeline monitoring system not found. Creating it now...")
    # We'll create the file later in a separate cell

# Try importing external libraries
missing_libraries = []

try:
    import requests
    print("✅ requests library successfully imported")
except ImportError:
    missing_libraries.append("requests")
    print("❌ requests library not found")

try:
    import h5py
    import numpy as np
    print("✅ h5py library successfully imported")
except ImportError:
    missing_libraries.append("h5py numpy")
    print("❌ h5py library not found")

try:
    import pysolr
    print("✅ pysolr library successfully imported")
except ImportError:
    missing_libraries.append("pysolr")
    print("❌ pysolr library not found")

try:
    from openai import OpenAI
    print("✅ openai library successfully imported")
except ImportError:
    missing_libraries.append("openai")
    print("❌ openai library not found")

# If any libraries are missing, print installation instructions
if missing_libraries:
    print("\n⚠️ Some required libraries are missing. Please install them using pip:")
    for lib in missing_libraries:
        print(f"pip install {lib}")
    print("\nAfter installing, restart the kernel and run this cell again.")
else:
    print("\n✅ All required libraries are installed!")

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('job_offer_translation')

# Enhanced Configuration System
import json
import logging
import os
from datetime import datetime

# Base configuration constants
SOLR_BASE_URL = "http://************:8983/solr/"
CONFIG_DIR = "config"
CONFIG_FILE = os.path.join(CONFIG_DIR, "translation_config.json")
BATCH_DIR = "batch_files"
RESULTS_DIR = "translation_results"
H5_DIR = "h5_storage"

# Create necessary directories
for directory in [CONFIG_DIR, BATCH_DIR, RESULTS_DIR, H5_DIR]:
    os.makedirs(directory, exist_ok=True)

# Fields we're interested in for job offers
# UPDATED: Using tm_* dynamic field pattern (proven working approach)
SOURCE_FIELDS = {
    "sm_field_offre_description_poste": "tm_offre_description_poste",
    "sm_field_offre_profil": "tm_offre_profil"
}

# Countries to work with - all French-speaking countries with job offers
COUNTRIES = [
    'algerie', 'benin', 'burkina', 'burundi', 'cameroun', 'centrafrique', 
    'congo', 'cote_d_ivoire', 'guinee', 'gabon', 'mali', 'mauritanie', 
    'maroc', 'niger', 'rdc', 'senegal', 'tchad', 'togo', 'tunisie'
]

# Default configuration
DEFAULT_CONFIG = {
    "general": {
        "default_batch_size": 5,  # Default batch size for all countries
        "config_version": "1.0.0",  # Configuration version for tracking changes
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    },
    "country_specific": {
        # Country-specific batch sizes (override default_batch_size)
        # Countries with more job offers can have larger batch sizes
        "batch_sizes": {
            "algerie": 10,
            "maroc": 10,
            "tunisie": 10,
            "senegal": 8,
            "cote_d_ivoire": 8,
            # Other countries will use the default_batch_size
        }
    },
    "openai": {
        "models": {
            "default": "gpt-4.1-nano-2025-04-14",  # Default model
            "alternatives": [  # Alternative models that can be selected
                "gpt-4.1-mini-2025-04-14",
                "gpt-4.1-turbo-2025-04-14",
                "gpt-4o-2024-05-13"
            ]
        },
        "translation": {
            "temperature": 0.3,  # Lower for more consistent translations
            "system_prompt": "You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text.",
            "user_prompt_template": "Translate the following French job offer text to English and return as JSON:\n\n{text}"
        },
        "batch_api": {
            "max_wait_time": 300,  # Maximum time to wait for batch completion (seconds)
            "wait_interval": 5,  # Time between status checks (seconds)
            "retry_count": 3,  # Number of retries for failed requests
            "retry_delay": 10  # Delay between retries (seconds)
        }
    },
    "solr": {
        "connection": {
            "timeout": 30,  # Connection timeout in seconds
            "base_url": SOLR_BASE_URL  # Base URL for Solr
        },
        "query": {
            "rows_per_query": 100,  # Maximum number of rows to return per query
            "sort_field": "entity_id",  # Field to sort by
            "sort_order": "asc"  # Sort order (asc or desc)
        },
        "update": {
            "commit_within": 5000,  # Time in ms to commit within
            "batch_size": 50,  # Number of documents to update in one batch
            "final_commit": True  # Whether to perform a final commit after all updates
        }
    },
    "logging": {
        "level": "INFO",  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        "format": "%(asctime)s - %(levelname)s - %(message)s",  # Log format
        "file": {  # File logging configuration
            "enabled": False,  # Whether to log to a file
            "path": "logs/translation.log",  # Log file path
            "max_size": 10485760,  # Maximum log file size (10 MB)
            "backup_count": 5  # Number of backup log files to keep
        }
    },
    "error_handling": {
        "max_consecutive_errors": 5,  # Maximum number of consecutive errors before aborting
        "error_threshold_percentage": 20,  # Maximum percentage of errors before aborting
        "continue_on_error": True,  # Whether to continue processing after errors
        "error_log_file": "logs/error.log"  # Error log file path
    }
}

# Function to load configuration
def load_config():
    """
    Load configuration from file or create default if not exists.
    
    Returns:
        dict: Configuration dictionary
    """
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"✅ Configuration loaded from {CONFIG_FILE}")
                return config
        else:
            # Create default configuration file
            save_config(DEFAULT_CONFIG)
            print(f"✅ Default configuration created at {CONFIG_FILE}")
            return DEFAULT_CONFIG
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        print("Using default configuration instead.")
        return DEFAULT_CONFIG

# Function to save configuration
def save_config(config):
    """
    Save configuration to file.
    
    Args:
        config (dict): Configuration dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Update last_updated timestamp
        config['general']['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"✅ Configuration saved to {CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"❌ Error saving configuration: {e}")
        return False

# Load the configuration
CONFIG = load_config()

# Add H5 storage configuration if it doesn't exist
if 'h5_storage' not in CONFIG:
    CONFIG['h5_storage'] = {
        "enabled": True,  # Whether to use H5 storage
        "compression": "gzip",  # Compression type (gzip, lzf, or None)
        "compression_level": 4,  # Compression level (1-9, higher = more compression but slower)
        "chunk_size": 100,  # Chunk size for datasets
        "buffer_size": 1000,  # Maximum number of documents to keep in memory before flushing to disk
        "file_pattern": "{country}.h5"  # File name pattern for H5 files
    }
    save_config(CONFIG)
    print("✅ Added H5 storage configuration")
    
# Add OpenAI configuration if it doesn't exist
if 'openai' not in CONFIG:
    CONFIG['openai'] = {
        "models": {
            "default": "gpt-4.1-nano-2025-04-14",  # Default model
            "alternatives": [  # Alternative models that can be selected
                "gpt-4.1-mini-2025-04-14",
                "gpt-4.1-turbo-2025-04-14",
                "gpt-4o-2024-05-13"
            ]
        },
        "translation": {
            "temperature": 0.3,  # Lower for more consistent translations
            "system_prompt": "You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text.",
            "user_prompt_template": "Translate the following French job offer text to English and return as JSON:\n\n{text}"
        },
        "batch_api": {
            "max_wait_time": 300,  # Maximum time to wait for batch completion (seconds)
            "wait_interval": 5,  # Time between status checks (seconds)
            "retry_count": 3,  # Number of retries for failed requests
            "retry_delay": 10  # Delay between retries (seconds)
        }
    }
    save_config(CONFIG)
    print("✅ Added OpenAI configuration")
    
# Add other required configuration sections if they don't exist
# Check for general section
if 'general' not in CONFIG:
    CONFIG['general'] = {
        "default_batch_size": 5,  # Default batch size for all countries
        "config_version": "1.0.0",  # Configuration version for tracking changes
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    save_config(CONFIG)
    print("✅ Added general configuration")
    
# Check for country_specific section
if 'country_specific' not in CONFIG:
    CONFIG['country_specific'] = {
        "batch_sizes": {
            "algerie": 10,
            "maroc": 10,
            "tunisie": 10,
            "senegal": 8,
            "cote_d_ivoire": 8,
        }
    }
    save_config(CONFIG)
    print("✅ Added country_specific configuration")
    
# Check for solr section
if 'solr' not in CONFIG:
    CONFIG['solr'] = {
        "connection": {
            "timeout": 30,  # Connection timeout in seconds
            "base_url": SOLR_BASE_URL  # Base URL for Solr
        },
        "query": {
            "rows_per_query": 100,  # Maximum number of rows to return per query
            "sort_field": "entity_id",  # Field to sort by
            "sort_order": "asc"  # Sort order (asc or desc)
        },
        "update": {
            "commit_within": 5000,  # Time in ms to commit within
            "batch_size": 50,  # Number of documents to update in one batch
            "final_commit": True  # Whether to perform a final commit after all updates
        }
    }
    save_config(CONFIG)
    print("✅ Added solr configuration")
    
# Check for error_handling section
if 'error_handling' not in CONFIG:
    CONFIG['error_handling'] = {
        "max_consecutive_errors": 5,  # Maximum number of consecutive errors before aborting
        "error_threshold_percentage": 20,  # Maximum percentage of errors before aborting
        "continue_on_error": True,  # Whether to continue processing after errors
        "error_log_file": "logs/error.log"  # Error log file path
    }
    save_config(CONFIG)
    print("✅ Added error_handling configuration")
    
# Check for logging section
if 'logging' not in CONFIG:
    CONFIG['logging'] = {
        "level": "INFO",  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        "format": "%(asctime)s - %(levelname)s - %(message)s",  # Log format
        "file": {  # File logging configuration
            "enabled": False,  # Whether to log to a file
            "path": "logs/translation.log",  # Log file path
            "max_size": 10485760,  # Maximum log file size (10 MB)
            "backup_count": 5  # Number of backup log files to keep
        }
    }
    save_config(CONFIG)
    print("✅ Added logging configuration")

# Configure logging based on configuration
log_level = getattr(logging, CONFIG['logging']['level'])
logging.basicConfig(level=log_level, format=CONFIG['logging']['format'])
logger = logging.getLogger('job_offer_translation')

# Add file handler if enabled and file configuration exists
if 'file' in CONFIG['logging'] and CONFIG['logging']['file']['enabled']:
    log_dir = os.path.dirname(CONFIG['logging']['file']['path'])
    os.makedirs(log_dir, exist_ok=True)
    file_handler = logging.FileHandler(CONFIG['logging']['file']['path'])
    file_handler.setFormatter(logging.Formatter(CONFIG['logging']['format']))
    logger.addHandler(file_handler)
elif 'file' not in CONFIG['logging']:
    # Add file configuration if it doesn't exist
    CONFIG['logging']['file'] = {
        "enabled": False,  # Whether to log to a file
        "path": "logs/translation.log",  # Log file path
        "max_size": 10485760,  # Maximum log file size (10 MB)
        "backup_count": 5  # Number of backup log files to keep
    }
    save_config(CONFIG)
    print("✅ Added logging file configuration")

# Helper function to get batch size for a country
def get_batch_size(country):
    """
    Get the batch size for a specific country.
    
    Args:
        country (str): Country code
        
    Returns:
        int: Batch size for the country
    """
    return CONFIG['country_specific']['batch_sizes'].get(
        country, CONFIG['general']['default_batch_size'])

def validate_job_offer(offer):
    """
    Validate a job offer document.
    
    Args:
        offer (dict): Job offer document
        
    Returns:
        tuple: (is_valid, reason)
    """
    # Check if the offer has an ID
    if 'id' not in offer:
        return False, "Missing ID"
    
    # Check if the offer has an entity_id
    if 'entity_id' not in offer:
        return False, "Missing entity_id"
    
    # Check if the offer has at least one of the source fields
    has_source_field = False
    for source_field in SOURCE_FIELDS.keys():
        if source_field in offer:
            has_source_field = True
            break
    
    if not has_source_field:
        return False, "Missing source fields"
    
    return True, ""

# Helper function to get current OpenAI model
def get_openai_model():
    """
    Get the current OpenAI model.
    
    Returns:
        str: OpenAI model name
    """
    return CONFIG['openai']['models']['default']

# =============================================================================
# CUSTOM CONFIGURATION ADJUSTMENT
# Modify any parameters below to customize the pipeline behavior
# =============================================================================

# ------ GENERAL SETTINGS ------
CONFIG['general']['default_batch_size'] = 5  # Default batch size for all countries

# ------ COUNTRY-SPECIFIC BATCH SIZES ------
# Adjust batch sizes for specific countries
CONFIG['country_specific']['batch_sizes'] = {
    'algerie': 10,
    'maroc': 10,
    'tunisie': 10,
    'senegal': 8,
    'cote_d_ivoire': 8,
    'benin': 5,
    'burkina': 5,
    'burundi': 5,
    'cameroun': 5,
    'centrafrique': 5,
    'congo': 5,
    'guinee': 5,
    'gabon': 5,
    'mali': 5,
    'mauritanie': 5,
    'niger': 5,
    'rdc': 5,
    'tchad': 5,
    'togo': 5
}

# ------ OPENAI API SETTINGS ------
# Ensure all required OpenAI sections exist
if 'models' not in CONFIG['openai']:
    CONFIG['openai']['models'] = {}
if 'translation' not in CONFIG['openai']:
    CONFIG['openai']['translation'] = {}
if 'batch_api' not in CONFIG['openai']:
    CONFIG['openai']['batch_api'] = {}
    
# Model selection
CONFIG['openai']['models']['default'] = "gpt-4.1-nano-2025-04-14"  # Options: "gpt-4.1-nano-2025-04-14", "gpt-4.1-mini-2025-04-14", "gpt-4.1-turbo-2025-04-14", "gpt-4o-2024-05-13"
if 'alternatives' not in CONFIG['openai']['models']:
    CONFIG['openai']['models']['alternatives'] = ["gpt-4.1-mini-2025-04-14", "gpt-4.1-turbo-2025-04-14", "gpt-4o-2024-05-13"]

# Translation settings
CONFIG['openai']['translation']['temperature'] = 0.3  # Lower for more consistent translations, higher for more variety (0.0-1.0)
CONFIG['openai']['translation']['system_prompt'] = "You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text."
CONFIG['openai']['translation']['user_prompt_template'] = "Translate the following French job offer text to English and return as JSON:\n\n{text}"

# Batch API settings
CONFIG['openai']['batch_api']['max_wait_time'] = 1800  # Maximum time to wait for batch completion (seconds) - 30 minutes
CONFIG['openai']['batch_api']['wait_interval'] = 10  # Time between status checks (seconds)
CONFIG['openai']['batch_api']['retry_count'] = 3  # Number of retries for failed requests
CONFIG['openai']['batch_api']['retry_delay'] = 10  # Delay between retries (seconds)

# ------ SOLR CONNECTION SETTINGS ------
# Ensure all required Solr sections exist
if 'connection' not in CONFIG['solr']:
    CONFIG['solr']['connection'] = {}
if 'query' not in CONFIG['solr']:
    CONFIG['solr']['query'] = {}
if 'update' not in CONFIG['solr']:
    CONFIG['solr']['update'] = {}
    
CONFIG['solr']['connection']['timeout'] = 30  # Connection timeout in seconds
CONFIG['solr']['connection']['base_url'] = SOLR_BASE_URL  # Base URL for Solr

# Solr query settings
CONFIG['solr']['query']['rows_per_query'] = 100  # Maximum number of rows to return per query
CONFIG['solr']['query']['sort_field'] = "entity_id"  # Field to sort by
CONFIG['solr']['query']['sort_order'] = "asc"  # Sort order (asc or desc)

# Solr update settings
CONFIG['solr']['update']['commit_within'] = 5000  # Time in ms to commit within
CONFIG['solr']['update']['batch_size'] = 50  # Number of documents to update in one batch
CONFIG['solr']['update']['final_commit'] = True  # Whether to perform a final commit after all updates

# ------ H5 STORAGE SETTINGS ------
# Ensure all H5 storage settings exist
if 'enabled' not in CONFIG['h5_storage']:
    CONFIG['h5_storage']['enabled'] = True
if 'compression' not in CONFIG['h5_storage']:
    CONFIG['h5_storage']['compression'] = "gzip"
if 'compression_level' not in CONFIG['h5_storage']:
    CONFIG['h5_storage']['compression_level'] = 4
if 'chunk_size' not in CONFIG['h5_storage']:
    CONFIG['h5_storage']['chunk_size'] = 100
if 'buffer_size' not in CONFIG['h5_storage']:
    CONFIG['h5_storage']['buffer_size'] = 1000
if 'file_pattern' not in CONFIG['h5_storage']:
    CONFIG['h5_storage']['file_pattern'] = "{country}.h5"
    
CONFIG['h5_storage']['enabled'] = True  # Whether to use H5 storage
CONFIG['h5_storage']['compression'] = "gzip"  # Compression type (gzip, lzf, or None)
CONFIG['h5_storage']['compression_level'] = 4  # Compression level (1-9, higher = more compression but slower)
CONFIG['h5_storage']['chunk_size'] = 100  # Chunk size for datasets
CONFIG['h5_storage']['buffer_size'] = 1000  # Maximum number of documents to keep in memory before flushing to disk
CONFIG['h5_storage']['file_pattern'] = "{country}.h5"  # File name pattern for H5 files

# ------ LOGGING SETTINGS ------
# Ensure all logging settings exist
if 'level' not in CONFIG['logging']:
    CONFIG['logging']['level'] = "INFO"
if 'format' not in CONFIG['logging']:
    CONFIG['logging']['format'] = "%(asctime)s - %(levelname)s - %(message)s"
if 'file' not in CONFIG['logging']:
    CONFIG['logging']['file'] = {}
if 'enabled' not in CONFIG['logging']['file']:
    CONFIG['logging']['file']['enabled'] = False
if 'path' not in CONFIG['logging']['file']:
    CONFIG['logging']['file']['path'] = "logs/translation.log"
if 'max_size' not in CONFIG['logging']['file']:
    CONFIG['logging']['file']['max_size'] = 10485760
if 'backup_count' not in CONFIG['logging']['file']:
    CONFIG['logging']['file']['backup_count'] = 5
    
CONFIG['logging']['level'] = "INFO"  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
CONFIG['logging']['format'] = "%(asctime)s - %(levelname)s - %(message)s"  # Log format

# File logging settings
CONFIG['logging']['file']['enabled'] = False  # Whether to log to a file
CONFIG['logging']['file']['path'] = "logs/translation.log"  # Log file path
CONFIG['logging']['file']['max_size'] = 10485760  # Maximum log file size (10 MB)
CONFIG['logging']['file']['backup_count'] = 5  # Number of backup log files to keep

# ------ ERROR HANDLING SETTINGS ------
# Ensure all error handling settings exist
if 'max_consecutive_errors' not in CONFIG['error_handling']:
    CONFIG['error_handling']['max_consecutive_errors'] = 5
if 'error_threshold_percentage' not in CONFIG['error_handling']:
    CONFIG['error_handling']['error_threshold_percentage'] = 20
if 'continue_on_error' not in CONFIG['error_handling']:
    CONFIG['error_handling']['continue_on_error'] = True
if 'error_log_file' not in CONFIG['error_handling']:
    CONFIG['error_handling']['error_log_file'] = "logs/error.log"
    
CONFIG['error_handling']['max_consecutive_errors'] = 5  # Maximum number of consecutive errors before aborting
CONFIG['error_handling']['error_threshold_percentage'] = 20  # Maximum percentage of errors before aborting
CONFIG['error_handling']['continue_on_error'] = True  # Whether to continue processing after errors
CONFIG['error_handling']['error_log_file'] = "logs/error.log"  # Error log file path

# Save the updated configuration
save_config(CONFIG)

# Print confirmation of key settings
print("\n===== CONFIGURATION UPDATED =====")
print(f"Default batch size: {CONFIG['general']['default_batch_size']}")
print(f"OpenAI model: {CONFIG['openai']['models']['default']}")
print(f"H5 storage: {'Enabled' if CONFIG['h5_storage']['enabled'] else 'Disabled'}")
print(f"Logging level: {CONFIG['logging']['level']}")
print("=================================\n")

# Print configuration summary
print(f"📋 Configuration loaded (version {CONFIG['general']['config_version']})")
print(f"📅 Last updated: {CONFIG['general']['last_updated']}")
print(f"🤖 Using OpenAI model: {get_openai_model()}")
print(f"📊 Default batch size: {CONFIG['general']['default_batch_size']}")
print(f"🔍 Log level: {CONFIG['logging']['level']}")
print(f"💾 H5 storage: {'Enabled' if CONFIG['h5_storage']['enabled'] else 'Disabled'}")

# Dictionary to cache open H5 file handles
h5_files = {}

def get_h5_file_path(country):
    """
    Get the path to the H5 file for a specific country.
    
    Args:
        country (str): Country code
        
    Returns:
        str: Path to the H5 file
    """
    file_pattern = CONFIG['h5_storage']['file_pattern']
    file_name = file_pattern.format(country=country)
    return os.path.join(H5_DIR, file_name)

def open_h5_file(country, mode='a'):
    """
    Open an H5 file for a specific country.
    Uses connection pooling to reuse existing file handles.
    
    Args:
        country (str): Country code
        mode (str): File mode ('r' for read-only, 'a' for read/write)
        
    Returns:
        h5py.File: H5 file handle
    """
    try:
        # Check if we already have an open file handle for this country
        if country in h5_files and h5_files[country] is not None:
            # Check if the file is still open
            if h5_files[country].id.valid:
                logger.debug(f"Using existing H5 file handle for {country}")
                return h5_files[country]
            else:
                # File was closed, remove it from the cache
                logger.debug(f"H5 file handle for {country} was closed, removing from cache")
                h5_files.pop(country, None)
        
        # Get the file path
        file_path = get_h5_file_path(country)
        logger.info(f"Opening H5 file: {file_path} (mode={mode})")
        
        # Create the file if it doesn't exist
        if mode == 'a' and not os.path.exists(file_path):
            logger.info(f"Creating new H5 file: {file_path}")
            # Create parent directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Open the file
        h5_file = h5py.File(file_path, mode)
        
        # Store in connection pool
        h5_files[country] = h5_file
        
        return h5_file
    except Exception as e:
        logger.error(f"Error opening H5 file for {country}: {e}")
        return None

def close_h5_file(country):
    """
    Close an H5 file for a specific country.
    
    Args:
        country (str): Country code
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if country in h5_files and h5_files[country] is not None:
            if h5_files[country].id.valid:
                logger.info(f"Closing H5 file for {country}")
                h5_files[country].close()
            h5_files.pop(country, None)
            return True
        return False
    except Exception as e:
        logger.error(f"Error closing H5 file for {country}: {e}")
        return False

def close_all_h5_files():
    """
    Close all open H5 files.
    
    Returns:
        int: Number of files closed
    """
    count = 0
    for country in list(h5_files.keys()):
        if close_h5_file(country):
            count += 1
    return count

def store_job_offers_in_h5(job_offers, country):
    """
    Store job offers in an H5 file.
    
    Args:
        job_offers (list): List of job offer documents
        country (str): Country code
        
    Returns:
        bool: True if successful, False otherwise
    """
    h5_file = None
    try:
        if not job_offers:
            logger.warning("No job offers to store")
            return False
        
        if not CONFIG['h5_storage']['enabled']:
            logger.info("H5 storage is disabled, skipping")
            return False
        
        # Close any existing file handle first to avoid write intent issues
        close_h5_file(country)
        
        # Make sure the directory exists
        file_path = get_h5_file_path(country)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Open the H5 file with write intent
        h5_file = open_h5_file(country, mode='a')
        if h5_file is None:
            logger.error(f"Failed to open H5 file for {country}")
            return False
        
        # Get compression settings
        compression = CONFIG['h5_storage']['compression']
        compression_level = CONFIG['h5_storage']['compression_level']
        
        # Create the main group if it doesn't exist
        if 'job_offers' not in h5_file:
            logger.info(f"Creating 'job_offers' group in H5 file for {country}")
            h5_file.create_group('job_offers')
        
        # Get the job_offers group
        job_offers_group = h5_file['job_offers']
        
        # Store each job offer
        stored_count = 0
        for offer in job_offers:
            try:
                # Validate the job offer
                is_valid, reason = validate_job_offer(offer)
                if not is_valid:
                    logger.warning(f"Skipping invalid offer: {reason}")
                    continue
                
                # Use the full ID as the key
                offer_id = offer['id']
                
                # Create a group for this offer if it doesn't exist
                if offer_id not in job_offers_group:
                    offer_group = job_offers_group.create_group(offer_id)
                else:
                    offer_group = job_offers_group[offer_id]
                
                # Store each field as a dataset
                for field, value in offer.items():
                    try:
                        # Skip fields that are already stored and haven't changed
                        if field in offer_group:
                            try:
                                # For list fields, compare the first item
                                if isinstance(value, list) and len(value) > 0:
                                    stored_value = offer_group[field][()]
                                    if isinstance(stored_value, bytes):
                                        stored_value = stored_value.decode('utf-8')
                                    if stored_value == value[0]:
                                        continue
                                # For string fields, compare directly
                                elif isinstance(value, str):
                                    stored_value = offer_group[field][()]
                                    if isinstance(stored_value, bytes):
                                        stored_value = stored_value.decode('utf-8')
                                    if stored_value == value:
                                        continue
                                # For other fields, delete and recreate
                                del offer_group[field]
                            except Exception as compare_error:
                                logger.warning(f"Error comparing field {field} for offer {offer_id}: {compare_error}")
                                # If there's an error comparing, delete and recreate
                                try:
                                    del offer_group[field]
                                except Exception:
                                    pass  # Ignore if deletion fails
                        
                        # Convert value to string for storage
                        if isinstance(value, list) and len(value) > 0:
                            # Store only the first item for list fields
                            string_value = str(value[0])
                        elif isinstance(value, (int, float, bool)):
                            # Store numeric values as strings
                            string_value = str(value)
                        elif isinstance(value, str):
                            string_value = value
                        else:
                            # Skip complex types
                            continue
                        
                        # Create the dataset - handle scalar dataset issues
                        try:
                            # Always store as a fixed-length string to avoid scalar dataset issues
                            # Create a string dtype with a fixed length that can accommodate the data
                            # Use utf-8 encoding to handle all Unicode characters
                            string_dtype = h5py.string_dtype('utf-8', len(string_value.encode('utf-8')) + 50)  # Add extra padding for encoding
                            
                            # For longer strings, use compression
                            if len(string_value) > 100:
                                offer_group.create_dataset(
                                    field,
                                    data=string_value,
                                    dtype=string_dtype,
                                    compression=compression,
                                    compression_opts=compression_level if compression == 'gzip' else None
                                )
                            else:
                                # For short strings, don't use compression
                                offer_group.create_dataset(field, data=string_value, dtype=string_dtype)
                        except Exception as dataset_error:
                            logger.warning(f"Error creating dataset for field {field} in offer {offer_id}: {dataset_error}")
                            # Try a fallback method with explicit UTF-8 encoding
                            try:
                                # First try with explicit UTF-8 encoding and variable length
                                offer_group.create_dataset(field, data=string_value, dtype=h5py.string_dtype('utf-8'))
                            except Exception as fallback_error1:
                                logger.warning(f"First fallback failed for field {field} in offer {offer_id}: {fallback_error1}")
                                try:
                                    # Second fallback: store as bytes with explicit encoding
                                    encoded_value = string_value.encode('utf-8', errors='replace')
                                    offer_group.create_dataset(field, data=encoded_value)
                                except Exception as fallback_error2:
                                    logger.warning(f"All fallbacks failed for field {field} in offer {offer_id}: {fallback_error2}")
                                    continue
                    except Exception as field_error:
                        logger.warning(f"Error processing field {field} for offer {offer_id}: {field_error}")
                        continue
                
                stored_count += 1
                # Flush every 10 offers to avoid memory issues
                if stored_count % 10 == 0:
                    h5_file.flush()
                    
            except Exception as offer_error:
                logger.warning(f"Error storing offer {offer.get('id', 'unknown')}: {offer_error}")
                continue
        
        # Flush the file to disk
        h5_file.flush()
        
        logger.info(f"Stored {stored_count} job offers in H5 file for {country}")
        return True
    except Exception as e:
        logger.error(f"Error storing job offers in H5 file for {country}: {e}")
        return False
    finally:
        # Make sure to close the file handle
        if h5_file is not None and hasattr(h5_file, 'id') and h5_file.id.valid:
            try:
                h5_file.close()
                logger.debug(f"Closed H5 file for {country} after storing")
            except Exception as close_error:
                logger.warning(f"Error closing H5 file for {country}: {close_error}")

def get_job_offers_from_h5(country, num_offers=None, start=0, skip_translated=True):
    """
    Get job offers from an H5 file.
    
    Args:
        country (str): Country code
        num_offers (int, optional): Number of job offers to retrieve. If None, uses the country-specific batch size.
        start (int): Starting offset for pagination
        skip_translated (bool): Whether to skip documents that already have translated fields
        
    Returns:
        list: List of job offer documents
    """
    try:
        if not CONFIG['h5_storage']['enabled']:
            logger.info("H5 storage is disabled, skipping")
            return []
        
        # Use country-specific batch size if not specified
        if num_offers is None:
            num_offers = get_batch_size(country)
            logger.info(f"Using country-specific batch size for {country}: {num_offers}")
        
        # Check if the H5 file exists
        file_path = get_h5_file_path(country)
        if not os.path.exists(file_path):
            logger.info(f"H5 file for {country} does not exist: {file_path}")
            return []
        
        # Open the H5 file
        h5_file = open_h5_file(country, mode='r')
        if h5_file is None:
            logger.error(f"Failed to open H5 file for {country}")
            return []
        
        # Check if the job_offers group exists
        if 'job_offers' not in h5_file:
            logger.info(f"No job offers found in H5 file for {country}")
            return []
        
        # Get the job_offers group
        job_offers_group = h5_file['job_offers']
        
        # Get all offer IDs
        offer_ids = list(job_offers_group.keys())
        
        # Apply pagination
        end = start + num_offers if num_offers is not None else len(offer_ids)
        paginated_ids = offer_ids[start:end]
        
        # Load job offers
        job_offers = []
        for offer_id in paginated_ids:
            try:
                # Convert offer_id to string if it's not already
                if not isinstance(offer_id, (str, bytes)):
                    offer_id = str(offer_id)
                    
                offer_group = job_offers_group[offer_id]
                
                # Create a dictionary for this offer
                offer = {'id': offer_id if isinstance(offer_id, str) else offer_id.decode('utf-8')}
                
                # Load all fields
                for field_name in offer_group.keys():
                    try:
                        # Load the dataset
                        dataset = offer_group[field_name]
                        value = dataset[()]
                        
                        # Convert bytes to string
                        if isinstance(value, bytes):
                            value = value.decode('utf-8')
                        
                        # Convert numeric strings back to numbers
                        if field_name == 'entity_id' and isinstance(value, str) and value.isdigit():
                            value = int(value)
                        
                        # Add to the offer dictionary
                        offer[field_name] = value
                    except Exception as field_error:
                        logger.warning(f"Error loading field {field_name} for offer {offer_id}: {field_error}")
                        continue
                
                # Check if this offer has translations
                has_translations = any(target_field in offer for target_field in SOURCE_FIELDS.values())
                
                # Skip if it has translations and we're skipping translated documents
                if skip_translated and has_translations:
                    continue
                
                # Validate the offer
                is_valid, reason = validate_job_offer(offer)
                if not is_valid:
                    logger.warning(f"Skipping invalid offer from H5: {reason}")
                    continue
                
                job_offers.append(offer)
            except Exception as offer_error:
                logger.warning(f"Error processing offer {offer_id} from H5: {offer_error}")
                continue
        
        logger.info(f"Retrieved {len(job_offers)} job offers from H5 file for {country}")
        return job_offers
    except Exception as e:
        logger.error(f"Error retrieving job offers from H5 file for {country}: {e}")
        return []

def update_job_offers_in_h5(translated_offers, country):
    """
    Update job offers in an H5 file with translations.
    
    Args:
        translated_offers (list): List of job offers with translations
        country (str): Country code
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if not translated_offers:
            logger.warning("No translated offers to update")
            return False
        
        if not CONFIG['h5_storage']['enabled']:
            logger.info("H5 storage is disabled, skipping")
            return False
        
        # Store the translated offers in the H5 file
        return store_job_offers_in_h5(translated_offers, country)
    except Exception as e:
        logger.error(f"Error updating job offers in H5 file for {country}: {e}")
        return False

# Create a connection pool for reusing Solr connections
solr_connections = {}

def get_solr_info():
    """
    Get information about the Solr server.
    
    Returns:
        dict: Solr server information
    """
    try:
        response = requests.get(f"{SOLR_BASE_URL}admin/info/system?wt=json", timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to get Solr info: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error getting Solr info: {e}")
        return None

def get_jobsearch_connection(country, timeout=None):
    """
    Get a connection to the JobSearch Solr core for the specified country.
    Uses connection pooling to reuse existing connections.
    
    Args:
        country (str): Country code
        timeout (int, optional): Connection timeout in seconds. If None, uses the value from config.
        
    Returns:
        pysolr.Solr: Solr connection
    """
    try:
        # Use timeout from config if not specified
        if timeout is None:
            timeout = CONFIG['solr']['connection']['timeout']
            
        # Check if we already have a connection for this country
        if country in solr_connections:
            logger.info(f"Using existing connection for {country}")
            return solr_connections[country]
        
        # Use the correct core pattern: core_jobsearch_[country]
        # Ensure the URL format is exactly as expected
        solr_url = f"{CONFIG['solr']['connection']['base_url']}core_jobsearch_{country}"
        logger.info(f"Creating new connection to JobSearch Solr core at {solr_url}")
        
        # Log the exact URL format that will be used for queries
        sort_param = f"{CONFIG['solr']['query']['sort_field']}+{CONFIG['solr']['query']['sort_order']}"
        rows = CONFIG['solr']['query']['rows_per_query']
        example_query_url = f"{solr_url}/select?q=*:*&fq=*:*&fl=id,entity_id&rows={rows}&start=0&sort={sort_param}&wt=json"
        logger.debug(f"Example query URL: {example_query_url}")
        
        # Create PySOLR connection with timeout
        solr = pysolr.Solr(solr_url, timeout=timeout)
        
        # Store in connection pool
        solr_connections[country] = solr
        return solr
    except Exception as e:
        logger.error(f"Error connecting to JobSearch Solr for {country}: {e}")
        return None

def query_solr_direct(country, params=None):
    """
    Query Solr directly using HTTP requests instead of PySOLR.
    This is useful for testing and debugging Solr queries.
    
    Args:
        country (str): Country code
        params (dict): Query parameters
        
    Returns:
        dict: Solr response as JSON
    """
    try:
        # Set default parameters if none provided
        if params is None:
            sort_param = f"{CONFIG['solr']['query']['sort_field']} {CONFIG['solr']['query']['sort_order']}"
            params = {
                'q': '*:*',
                'fq': '*:*',
                'fl': 'id,entity_id',
                'rows': CONFIG['solr']['query']['rows_per_query'],
                'start': 0,
                'sort': sort_param,
                'wt': 'json'
            }
        
        # Construct the URL
        solr_url = f"{CONFIG['solr']['connection']['base_url']}core_jobsearch_{country}/select"
        
        # Make the request
        logger.info(f"Making direct HTTP request to Solr: {solr_url}")
        response = requests.get(solr_url, params=params, timeout=CONFIG['solr']['connection']['timeout'])
        
        # Check if the request was successful
        if response.status_code == 200:
            # Parse the JSON response
            result = response.json()
            logger.info(f"Direct Solr query successful. Found {result.get('response', {}).get('numFound', 0)} documents.")
            return result
        else:
            logger.error(f"Direct Solr query failed with status code {response.status_code}: {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error in direct Solr query: {e}")
        return None

def validate_job_offer(offer):
    """
    Validate a job offer document to ensure it has all required fields and proper ID format.
    
    Args:
        offer (dict): Job offer document
        
    Returns:
        tuple: (is_valid, reason)
    """
    # Validate ID format
    if 'id' not in offer or not isinstance(offer['id'], str) or '/node/' not in offer['id']:
        return False, f"Invalid ID format: {offer.get('id', 'unknown')}"
    
    # Validate entity_id field
    if 'entity_id' not in offer:
        return False, f"Missing entity_id field for offer {offer['id']}"
    
    # Cross-validate id and entity_id fields
    try:
        id_parts = offer['id'].split('/node/')
        if len(id_parts) == 2 and id_parts[1].isdigit():
            numeric_id = int(id_parts[1])
            if numeric_id != offer['entity_id']:
                return False, f"Mismatched IDs: id={offer['id']}, entity_id={offer['entity_id']}"
        else:
            return False, f"Unparseable ID format: {offer['id']}"
    except Exception as e:
        return False, f"Error validating offer IDs: {e}"
    
    # Check if all source fields exist
    has_source_fields = all(source_field in offer for source_field in SOURCE_FIELDS.keys())
    if not has_source_fields:
        return False, f"Missing source fields for offer {offer['id']}"
    
    return True, "Valid"

def get_job_offers(country, num_offers=None, start=0, skip_translated=True):
    """
    Get job offers from the JobSearch Solr core with direct query filtering for documents needing translation.
    
    Args:
        country (str): Country code
        num_offers (int, optional): Number of job offers to retrieve. If None, uses the country-specific batch size.
        start (int): Starting offset for pagination
        skip_translated (bool): Whether to skip documents that already have translated fields
        
    Returns:
        list: List of job offer documents that need translation
    """
    try:
        # Use country-specific batch size if not specified
        if num_offers is None:
            num_offers = get_batch_size(country)
            logger.info(f"Using country-specific batch size for {country}: {num_offers}")
            
        solr = get_jobsearch_connection(country)
        if not solr:
            logger.error(f"Failed to get JobSearch Solr connection for {country}")
            return []
        
        # Fields to retrieve - we no longer need to include translated fields
        # since we're filtering at the query level
        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())
        
        # Prepare query parameters
        sort_param = f"{CONFIG['solr']['query']['sort_field']} {CONFIG['solr']['query']['sort_order']}"
        params = {
            'q': '*:*',     # Main query
            'fl': ','.join(fields),  # Fields to return
            'rows': num_offers,      # Number of rows to return
            'start': start,          # Starting offset
            'sort': sort_param,      # Sort by configured field and order
            'wt': 'json'             # Response format
        }
        
        # Add filter query to exclude documents with translations if requested
        if skip_translated:
            # Create a filter query that excludes documents with any translated fields
            translation_filters = []
            for target_field in SOURCE_FIELDS.values():
                translation_filters.append(f"-{target_field}:[* TO *]")
            
            # Combine filters with AND
            params['fq'] = " AND ".join(translation_filters)
            logger.info(f"Using filter query to exclude documents with translations: {params['fq']}")
        else:
            # If not skipping translated documents, use a simple filter query
            params['fq'] = '*:*'
        
        # Construct the exact URL for logging/debugging
        solr_url = f"{CONFIG['solr']['connection']['base_url']}core_jobsearch_{country}/select"
        query_params = '&'.join([f"{k}={v}" for k, v in params.items()])
        full_url = f"{solr_url}?{query_params}"
        logger.debug(f"Full Solr query URL: {full_url}")
        
        # Execute the query
        logger.info(f"Retrieving job offers from {country} (start={start}, rows={num_offers})")
        # Note: PySOLR's search method takes q as a positional argument and the rest as kwargs
        q = params.pop('q')  # Remove q from params to pass it separately
        results = solr.search(q, **params)
        
        # Convert to list of dictionaries
        all_offers = [dict(doc) for doc in results]
        logger.info(f"Retrieved {len(all_offers)} job offers from {country} that need translation")
        
        # We still need to validate the documents, but we don't need to check for translations
        valid_offers = []
        for offer in all_offers:
            # Validate the job offer using our validation function
            is_valid, reason = validate_job_offer(offer)
            if not is_valid:
                logger.warning(f"Skipping offer: {reason}")
                continue
            
            valid_offers.append(offer)
        
        logger.info(f"{len(valid_offers)} out of {len(all_offers)} job offers are valid and need translation")
        return valid_offers
    except Exception as e:
        logger.error(f"Error retrieving job offers from {country}: {e}")
        return []

def prepare_batch_file(job_offers, country):
    """
    Prepare a batch file for OpenAI Batch API with the correct format.
    
    Args:
        job_offers (list): List of job offer documents
        country (str): Country code
        
    Returns:
        str: Path to the batch file
    """
    try:
        if not job_offers:
            logger.warning("No job offers to process")
            return None
        
        # Create a unique batch file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        batch_file_path = os.path.join(BATCH_DIR, f"{country}_batch_{timestamp}.jsonl")
        
        # Prepare batch requests
        batch_requests = []
        
        # Get OpenAI configuration
        model = get_openai_model()
        temperature = CONFIG['openai']['translation']['temperature']
        system_prompt = CONFIG['openai']['translation']['system_prompt']
        user_prompt_template = CONFIG['openai']['translation']['user_prompt_template']
        
        for offer in job_offers:
            offer_id = offer.get('id', str(uuid.uuid4()))
            
            # Process each source field
            for source_field, target_field in SOURCE_FIELDS.items():
                if source_field in offer:
                    value = offer[source_field]
                    
                    # Handle list values
                    if isinstance(value, list):
                        if not value:
                            continue
                        text = value[0]  # Use the first item
                    elif isinstance(value, str):
                        text = value
                    else:
                        continue
                    
                    # Skip empty text
                    if not text or not text.strip():
                        continue
                    
                    # Decode HTML entities
                    decoded_text = html.unescape(text)
                    
                    # Create a unique custom ID for this request that avoids parsing issues
                    # Use a separator that won't appear in the field name
                    # For source fields with underscores, use a special format
                    if '_' in source_field:
                        # Use a special format for fields with underscores
                        field_part = source_field.replace('_', '-')
                        custom_id = f"{offer_id}__FIELD__{field_part}"
                    else:
                        custom_id = f"{offer_id}_{source_field}"
                    
                    # Format the user prompt with the text
                    user_prompt = user_prompt_template.format(text=decoded_text)
                    
                    # Create the batch request with body as an object (not a string)
                    # Include the word "json" in the messages to satisfy the requirement
                    batch_request = {
                        "custom_id": custom_id,
                        "method": "POST",
                        "url": "/v1/chat/completions",
                        "body": {
                            "model": model,
                            "messages": [
                                {"role": "system", "content": system_prompt},
                                {"role": "user", "content": user_prompt}
                            ],
                            "response_format": {"type": "json_object"},
                            "temperature": temperature
                        }
                    }
                    
                    batch_requests.append(batch_request)
        
        # Write batch requests to file
        if batch_requests:
            with open(batch_file_path, 'w', encoding='utf-8', newline='\n') as f:
                for request in batch_requests:
                    f.write(json.dumps(request, ensure_ascii=False) + '\n')
            
            logger.info(f"Created batch file with {len(batch_requests)} requests: {batch_file_path}")
            return batch_file_path
        else:
            logger.warning("No valid requests to process")
            return None
    except Exception as e:
        logger.error(f"Error preparing batch file: {e}")
        return None

def save_batch_job_info(batch_job_id, batch_file_path, country):
    """
    Save batch job information to a JSON file for later processing.
    
    Args:
        batch_job_id (str): The batch job ID
        batch_file_path (str): Path to the original batch file
        country (str): The country code
        
    Returns:
        str: Path to the info file
    """
    try:
        # Create the pending jobs directory if it doesn't exist
        pending_dir = os.path.join(os.path.dirname(batch_file_path), "pending_jobs")
        os.makedirs(pending_dir, exist_ok=True)
        
        # Create a unique filename based on the batch job ID
        info_file_path = os.path.join(pending_dir, f"{batch_job_id}.json")
        
        # Create the info dictionary
        info = {
            "batch_job_id": batch_job_id,
            "batch_file_path": batch_file_path,
            "country": country,
            "created_at": datetime.now().isoformat(),
            "status": "pending"
        }
        
        # Save the info to a file
        with open(info_file_path, 'w', encoding='utf-8') as f:
            json.dump(info, f, indent=2)
        
        logger.info(f"Saved batch job info to {info_file_path}")
        return info_file_path
    except Exception as e:
        logger.error(f"Error saving batch job info: {e}")
        return None

def process_batch(batch_file_path, client, country=None, wait_for_completion=False):
    """
    Process a batch file using OpenAI's Batch API.
    
    Args:
        batch_file_path (str): Path to the batch file
        client (OpenAI): OpenAI client
        country (str, optional): Country code for the batch
        wait_for_completion (bool): Whether to wait for the batch to complete
        
    Returns:
        str: Path to the output file if wait_for_completion=True and batch completes,
             or path to the batch job info file if wait_for_completion=False
    """
    try:
        if not batch_file_path or not os.path.exists(batch_file_path):
            logger.error("Invalid batch file path")
            return None
        
        logger.info(f"Processing batch file: {batch_file_path}")
        
        # Get batch API configuration
        max_wait_time = CONFIG['openai']['batch_api']['max_wait_time']
        wait_interval = CONFIG['openai']['batch_api']['wait_interval']
        retry_count = CONFIG['openai']['batch_api']['retry_count']
        retry_delay = CONFIG['openai']['batch_api']['retry_delay']
        
        # Step 1: Upload the batch file to OpenAI
        uploaded_file = None
        for attempt in range(retry_count + 1):
            try:
                logger.info(f"Uploading batch file to OpenAI: {batch_file_path}")
                with open(batch_file_path, 'rb') as f:
                    uploaded_file = client.files.create(
                        file=f,
                        purpose="batch"
                    )
                logger.info(f"Successfully uploaded file with ID: {uploaded_file.id}")
                break
            except Exception as e:
                if attempt < retry_count:
                    logger.warning(f"File upload failed (attempt {attempt+1}/{retry_count+1}): {e}")
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"File upload failed after {retry_count+1} attempts: {e}")
                    return None
        
        if not uploaded_file:
            logger.error("Failed to upload batch file")
            return None
            
        # Step 2: Create a batch job with the uploaded file
        batch_job = None
        for attempt in range(retry_count + 1):
            try:
                logger.info(f"Creating batch job with file ID: {uploaded_file.id}")
                batch_job = client.batches.create(
                    input_file_id=uploaded_file.id,
                    endpoint="/v1/chat/completions",
                    completion_window="24h"
                )
                logger.info(f"Successfully created batch job with ID: {batch_job.id}")
                break
            except Exception as e:
                if attempt < retry_count:
                    logger.warning(f"Batch job creation failed (attempt {attempt+1}/{retry_count+1}): {e}")
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"Batch job creation failed after {retry_count+1} attempts: {e}")
                    return None
        
        if not batch_job:
            logger.error("Failed to create batch job")
            return None
        
        job_id = batch_job.id
        logger.info(f"Created batch job with ID: {job_id}")
        
        # If we're not waiting for completion, save the job info and return
        if not wait_for_completion:
            info_file_path = save_batch_job_info(job_id, batch_file_path, country)
            logger.info(f"Batch job {job_id} submitted and will be processed later. Info saved to {info_file_path}")
            return info_file_path
        
        # Step 3: Wait for the job to complete (only if wait_for_completion=True)
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            # Get the job status
            job_status = client.batches.retrieve(job_id)
            status = job_status.status
            
            # Log the current status and request counts
            request_counts = job_status.request_counts
            logger.info(f"Batch job status: {status} (Total: {request_counts.total}, Completed: {request_counts.completed}, Failed: {request_counts.failed})")
            
            if status == "completed":
                # Step 4: Get the output file ID
                output_file_id = job_status.output_file_id
                if not output_file_id:
                    logger.error("No output file ID found in completed batch")
                    return None
                
                logger.info(f"Batch completed with output file ID: {output_file_id}")
                
                # Check if there were any errors
                if job_status.error_file_id:
                    logger.warning(f"Batch completed with errors. Error file ID: {job_status.error_file_id}")
                    # Optionally download and process error file here
                
                # Step 5: Download the output file content
                for attempt in range(retry_count + 1):
                    try:
                        logger.info(f"Downloading output file content for file ID: {output_file_id}")
                        file_response = client.files.content(output_file_id)
                        
                        # Save the output file
                        output_file_path = batch_file_path.replace(".jsonl", "_output.jsonl")
                        with open(output_file_path, 'wb') as f:
                            # Write the content to the file
                            for chunk in file_response.iter_bytes():
                                f.write(chunk)
                        
                        logger.info(f"Downloaded output file: {output_file_path}")
                        return output_file_path
                    except Exception as e:
                        if attempt < retry_count:
                            logger.warning(f"Error downloading output file (attempt {attempt+1}/{retry_count+1}): {e}")
                            logger.info(f"Retrying in {retry_delay} seconds...")
                            time.sleep(retry_delay)
                        else:
                            logger.error(f"Error downloading output file after {retry_count+1} attempts: {e}")
                            return None
            elif status == "failed":
                error_message = getattr(job_status, 'error', 'No error details available')
                logger.error(f"Batch job failed: {error_message}")
                return None
            elif status == "expired":
                logger.error("Batch job expired (not completed within 24-hour window)")
                return None
            elif status == "cancelled":
                logger.error("Batch job was cancelled")
                return None
            elif status in ["validating", "in_progress", "finalizing", "cancelling"]:
                # Wait and check again
                time.sleep(wait_interval)
                elapsed_time += wait_interval
            else:
                logger.error(f"Unknown batch job status: {status}")
                return None
        
        # If we reach here, the job timed out but we'll save it for later processing
        logger.warning(f"Batch job monitoring timed out after {max_wait_time} seconds, saving for later processing")
        info_file_path = save_batch_job_info(job_id, batch_file_path, country)
        return None
    except Exception as e:
        logger.error(f"Error processing batch: {e}")
        return None

def process_batch_results(output_file_path, job_offers):
    """
    Process the results of a batch job and map them back to the job offers.
    
    Args:
        output_file_path (str): Path to the output file
        job_offers (list): List of job offer documents
        
    Returns:
        list: List of job offers with translations
    """
    try:
        if not output_file_path or not os.path.exists(output_file_path):
            logger.error("Invalid output file path")
            return []
        
        # Create a map of job offers by ID for quick lookup
        job_offers_map = {offer['id']: offer for offer in job_offers}
        
        # Read the output file
        with open(output_file_path, 'r', encoding='utf-8') as f:
            results = [json.loads(line) for line in f if line.strip()]
        
        logger.info(f"Processing {len(results)} batch results")
        
        # Process each result
        translated_offers = []
        for result in results:
            # Check if the request has an error
            if result.get('error'):
                logger.warning(f"Request failed: {result.get('error')}")
                continue
                
            # Check if the response exists and has a valid status code
            response = result.get('response')
            if not response or response.get('status_code') != 200:
                status_code = response.get('status_code') if response else 'No response'
                logger.warning(f"Request failed with status code: {status_code}")
                continue
            
            # Get the custom ID to map back to the job offer
            custom_id = result.get('custom_id')
            if not custom_id or '_' not in custom_id:
                logger.warning(f"Invalid custom ID: {custom_id}")
                continue
            
            # Parse the custom ID to get the job offer ID and field
            try:
                # Handle different custom ID formats
                if '__FIELD__' in custom_id:
                    # This is our special format for fields with underscores
                    offer_id, field_part = custom_id.split('__FIELD__')
                    # Convert hyphens back to underscores
                    source_field = field_part.replace('-', '_')
                elif '_sm_field_offre_' in custom_id:
                    # Handle the case where the field name contains underscores (old format)
                    parts = custom_id.split('_sm_field_offre_')
                    offer_id = parts[0]
                    source_field = 'sm_field_offre_' + parts[1]
                else:
                    # Default case - split on the last underscore
                    offer_id, source_field = custom_id.rsplit('_', 1)
                
                # Get the job offer
                offer = job_offers_map.get(offer_id)
                
                # If not found, try alternative formats
                if not offer:
                    # Try with just the entity_id part
                    if '/' in offer_id:
                        entity_id = offer_id.split('/')[-1]
                        # Look for any offer with this entity_id
                        for potential_offer in job_offers:
                            if str(potential_offer.get('entity_id')) == entity_id or potential_offer.get('id').endswith('/' + entity_id):
                                offer = potential_offer
                                break
                
                if not offer:
                    logger.warning(f"Job offer not found for ID: {offer_id} (from custom_id: {custom_id})")
                    continue
            except Exception as e:
                logger.warning(f"Error parsing custom ID {custom_id}: {e}")
                continue
            
            # Get the target field
            target_field = SOURCE_FIELDS.get(source_field)
            if not target_field:
                logger.warning(f"Target field not found for source field: {source_field}")
                continue
            
            # Get the response body
            response_body = response.get('body')
            if not response_body:
                logger.warning(f"No response body for request: {custom_id}")
                continue
            
            # Parse the response body
            try:
                # Extract the assistant's message from the choices
                choices = response_body.get('choices', [])
                if not choices or len(choices) == 0:
                    logger.warning(f"No choices found in response for request: {custom_id}")
                    continue
                    
                # Get the message content from the first choice
                message = choices[0].get('message', {})
                content = message.get('content', '')
                
                if not content:
                    logger.warning(f"No content found in response message for request: {custom_id}")
                    continue
                    
                # Parse the JSON content to extract the translation
                try:
                    content_json = json.loads(content)
                    translation = content_json.get('translation')
                    
                    if not translation:
                        logger.warning(f"No translation field found in JSON response for request: {custom_id}")
                        continue
                        
                    # Add the translation to the job offer as an array to match the original format
                    offer[target_field] = [translation]
                    
                    # Add the job offer to the list of translated offers if not already there
                    if offer not in translated_offers:
                        translated_offers.append(offer)
                        
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON content from response for request {custom_id}: {e}")
                    continue
            except Exception as e:
                logger.warning(f"Failed to process response for request {custom_id}: {e}")
                continue
        
        logger.info(f"Processed {len(translated_offers)} job offers with translations")
        return translated_offers
    except Exception as e:
        logger.error(f"Error processing batch results: {e}")
        return []

def save_translations_to_json(translated_offers, country):
    """
    Save translated job offers to a JSON file.
    
    Args:
        translated_offers (list): List of job offers with translations
        country (str): Country code
        
    Returns:
        str: Path to the saved file
    """
    try:
        if not translated_offers:
            logger.warning("No translated offers to save")
            return None
        
        # Create a unique file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join(RESULTS_DIR, f"{country}_translated_offers_{timestamp}.json")
        
        # Save the translated offers
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(translated_offers, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved {len(translated_offers)} translated offers to {file_path}")
        return file_path
    except Exception as e:
        logger.error(f"Error saving translations to JSON: {e}")
        return None

def index_translations(en_translation, country):
    """
    UPDATED: Index translations using the proven tm_* dynamic field approach with PySOLR.
    This function replaces the old approach and uses the working solution from our testing.
    
    Args:
        en_translation (dict): Dictionary containing document ID and translated fields
        country (str): Country code
        
    Returns:
        str: Status message indicating success or failure
    """
    try:
        import pysolr
        
        # Create PySOLR connection
        solr = pysolr.Solr(f'http://************:8983/solr/core_jobsearch_{country}')
        node_id = en_translation['id']
        
        logger.debug(f"Processing document: {node_id}")
        
        update_count = 0
        for key, value in en_translation.items():
            if key.startswith('tm_'):  # Dynamic fields using tm_* pattern
                logger.debug(f"Updating dynamic field: {key}")
                try:
                    # Use individual field atomic updates (proven working approach)
                    solr.add([{'id': node_id, key: {'set': value}}])
                    logger.debug(f"Field {key} updated successfully")
                    update_count += 1
                except Exception as e:
                    logger.error(f"Error updating {key}: {e}")
                    return f"{node_id} ERROR: {str(e)}"
        
        # Commit the changes
        try:
            solr.commit()
            logger.debug(f"Committed {update_count} field updates for {node_id}")
            return f"{node_id} DONE"
        except Exception as e:
            logger.error(f"Commit error for {node_id}: {e}")
            return f"{node_id} COMMIT_FAILED: {str(e)}"
            
    except Exception as e:
        logger.error(f"Error in index_translations for {node_id}: {e}")
        return f"{node_id} ERROR: {str(e)}"

def prepare_atomic_update_document(doc_id, translations):
    """
    Prepare an update document for Solr using atomic updates.
    
    Args:
        doc_id (str): Document ID
        translations (dict): Dictionary of translated fields
        
    Returns:
        dict: Update document with atomic update syntax
    """
    # Create update document with ID field
    update_doc = {
        "id": doc_id
    }
    
    # Add translated fields using atomic update syntax
    for field, value in translations.items():
        # Ensure the value is an array to match the original format
        if isinstance(value, list):
            update_doc[field] = {"set": value}
        else:
            update_doc[field] = {"set": [value]}
    
    return update_doc

def update_solr_with_translations(translated_offers, country, batch_size=None):
    """
    Update Solr with the translated fields using atomic updates via direct HTTP requests.
    This implementation uses the working logic from SOLR_UPDATE_TESTING.ipynb.
    
    Args:
        translated_offers (list): List of job offers with translations
        country (str): Country code
        batch_size (int, optional): Number of documents to update in one batch. If None, uses the configured value.
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if not translated_offers:
            logger.warning("No translated offers to update")
            return False
        
        # Use configured batch size if not specified
        if batch_size is None:
            batch_size = CONFIG['solr']['update']['batch_size']
            logger.info(f"Using configured batch size for Solr updates: {batch_size}")
        
        logger.info(f"Updating {len(translated_offers)} job offers in Solr for {country} using atomic updates")
        
        # Get Solr update configuration
        commit_within = CONFIG['solr']['update']['commit_within']
        final_commit = CONFIG['solr']['update']['final_commit']
        timeout = CONFIG['solr']['connection']['timeout']
        
        # Construct the update URL
        core_name = f"core_jobsearch_{country}"
        update_url = f"{CONFIG['solr']['connection']['base_url']}{core_name}/update"
        
        # Process in batches
        total_offers = len(translated_offers)
        success_count = 0
        failure_count = 0
        consecutive_errors = 0
        max_consecutive_errors = CONFIG['error_handling']['max_consecutive_errors']
        continue_on_error = CONFIG['error_handling']['continue_on_error']
        error_threshold_percentage = CONFIG['error_handling']['error_threshold_percentage']
        
        for i in range(0, total_offers, batch_size):
            batch = translated_offers[i:i+batch_size]
            update_batch = []
            
            for offer in batch:
                # Validate the offer
                is_valid, reason = validate_job_offer(offer)
                if not is_valid:
                    logger.warning(f"Skipping invalid offer: {reason}")
                    failure_count += 1
                    continue
                
                # Prepare translations dictionary
                translations = {}
                for source_field, target_field in SOURCE_FIELDS.items():
                    if target_field in offer:
                        # Ensure the value is an array to match the original format
                        if isinstance(offer[target_field], list):
                            translations[target_field] = offer[target_field]
                        else:
                            translations[target_field] = [offer[target_field]]
                
                # Skip if no translated fields
                if not translations:
                    logger.warning(f"Skipping offer with no translated fields: {offer['id']}")
                    failure_count += 1
                    continue
                
                # Prepare atomic update document
                update_doc = prepare_atomic_update_document(offer["id"], translations)
                update_batch.append(update_doc)
            
            # Update documents in batch using direct HTTP requests with atomic updates
            if update_batch:
                try:
                    # Prepare parameters
                    params = {
                        'commit': 'true' if final_commit else 'false',
                        'wt': 'json'
                    }
                    
                    # Add commitWithin if specified
                    if commit_within:
                        params['commitWithin'] = str(commit_within)
                    
                    # Prepare headers
                    headers = {
                        'Content-Type': 'application/json'
                    }
                    
                    # Log the update URL for debugging
                    param_str = "&".join([f"{k}={v}" for k, v in params.items()])
                    full_url = f"{update_url}?{param_str}"
                    logger.debug(f"Solr update URL: {full_url}")
                    logger.debug(f"Updating batch of {len(update_batch)} documents")
                    
                    # Make the HTTP request with atomic update syntax
                    response = requests.post(
                        update_url,
                        params=params,
                        headers=headers,
                        data=json.dumps(update_batch),
                        timeout=timeout
                    )
                    
                    # Check if the request was successful
                    if response.status_code == 200:
                        response_json = response.json()
                        if response_json.get('responseHeader', {}).get('status') == 0:
                            success_count += len(update_batch)
                            consecutive_errors = 0  # Reset consecutive errors counter on success
                            logger.info(f"Successfully updated batch of {len(update_batch)} documents")
                        else:
                            logger.error(f"Solr returned error status: {response_json}")
                            failure_count += len(update_batch)
                            consecutive_errors += 1
                    else:
                        logger.error(f"HTTP request failed with status {response.status_code}: {response.text}")
                        failure_count += len(update_batch)
                        consecutive_errors += 1
                        
                except Exception as e:
                    logger.error(f"Error updating batch: {e}")
                    failure_count += len(update_batch)
                    consecutive_errors += 1
                
                # Check if we should abort due to too many consecutive errors
                if consecutive_errors >= max_consecutive_errors:
                    logger.error(f"Aborting after {consecutive_errors} consecutive errors")
                    break
                
                # Check if we should abort due to error threshold percentage
                if success_count + failure_count > 0:
                    error_rate = (failure_count / (success_count + failure_count)) * 100
                    if error_rate > error_threshold_percentage:
                        logger.error(f"Aborting due to error rate exceeding threshold: {error_rate:.1f}% > {error_threshold_percentage}%")
                        break
                
                # Check if we should continue after errors
                if consecutive_errors > 0 and not continue_on_error:
                    logger.error("Aborting due to error (continue_on_error=False)")
                    break
        
        # Final commit if configured and not already committed
        if final_commit and success_count > 0 and not CONFIG['solr']['update'].get('commit_within'):
            try:
                commit_params = {'commit': 'true', 'wt': 'json'}
                commit_response = requests.post(update_url, params=commit_params, timeout=timeout)
                if commit_response.status_code == 200:
                    logger.info("Final commit successful")
                else:
                    logger.error(f"Final commit failed: {commit_response.status_code} - {commit_response.text}")
            except Exception as e:
                logger.error(f"Error during final commit: {e}")
        
        logger.info(f"Solr update complete: {success_count} successful, {failure_count} failed")
        return success_count > 0
    except Exception as e:
        logger.error(f"Error updating Solr with translations: {e}")
        return False

def check_pending_batch_jobs():
    """
    Check for pending batch jobs and process any that are completed.
    """
    try:
        print("\n🔍 Checking for pending batch jobs...")
        
        # Create the OpenAI client
        client = OpenAI()
        
        # Check for pending jobs
        counts = check_pending_batch_jobs(client)
        
        if counts.get("total", 0) == 0:
            print("No pending batch jobs found.")
            return
        
        print(f"\nFound {counts.get('total', 0)} pending batch jobs:")
        print(f"- Completed: {counts.get('completed', 0)}")
        print(f"- In progress: {counts.get('in_progress', 0)}")
        print(f"- Failed: {counts.get('failed', 0)}")
        print(f"- Other: {counts.get('other', 0)}")
        
        # Process completed jobs
        if counts.get('completed', 0) > 0:
            print(f"\n✅ {counts.get('completed', 0)} jobs completed and processed.")
            print("Check the 'batch_files/completed_jobs' directory for details.")
        
        # Show in-progress jobs
        if counts.get('in_progress', 0) > 0:
            print(f"\n⏳ {counts.get('in_progress', 0)} jobs still in progress.")
            print("Run this function again later to check their status.")
        
        # Show failed jobs
        if counts.get('failed', 0) > 0:
            print(f"\n❌ {counts.get('failed', 0)} jobs failed.")
            print("Check the 'batch_files/failed_jobs' directory for details.")
    except Exception as e:
        print(f"\n❌ Error checking pending batch jobs: {e}")

def run_batch_translation(country, batch_size=None, update_solr=False, wait_for_completion=False):
    """
    Run the batch translation pipeline for a country.
    
    Args:
        country (str): Country code
        batch_size (int, optional): Number of job offers to process in one batch. If None, uses the country-specific batch size.
        update_solr (bool): Whether to update Solr with the translations
        wait_for_completion (bool): Whether to wait for the batch job to complete
        
    Returns:
        tuple: (success, results_path)
    """
    try:
        # Create a unique run ID for this pipeline execution
        run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Use country-specific batch size if not specified
        if batch_size is None:
            batch_size = get_batch_size(country)
            
        logger.info(f"Starting batch translation for {country} with batch size {batch_size}")
        
        # Take snapshot of pipeline start
        take_snapshot("pipeline_start", [], {
            "country": country,
            "batch_size": batch_size,
            "update_solr": update_solr,
            "wait_for_completion": wait_for_completion,
            "model": get_openai_model(),
            "temperature": CONFIG['openai']['translation']['temperature']
        }, country=country, run_id=run_id)
        
        # Step 1: Get job offers that need translation
        # First try to get from H5 storage if enabled
        job_offers = []
        if CONFIG['h5_storage']['enabled']:
            logger.info(f"Trying to get job offers from H5 storage for {country}")
            job_offers = get_job_offers_from_h5(country, num_offers=batch_size, start=0, skip_translated=True)
            
        # If not enough offers found in H5 or H5 is disabled, get more from Solr
        if len(job_offers) < batch_size:
            remaining = batch_size - len(job_offers)
            if job_offers:
                logger.info(f"Found {len(job_offers)} offers in H5, getting {remaining} more from Solr for {country}")
            else:
                logger.info(f"No offers found in H5, getting {remaining} from Solr for {country}")
                
            solr_offers = get_job_offers(country, num_offers=remaining, start=0, skip_translated=True)
            
            # Store new offers in H5 if enabled
            if solr_offers and CONFIG['h5_storage']['enabled']:
                logger.info(f"Storing {len(solr_offers)} new job offers in H5 for {country}")
                store_job_offers_in_h5(solr_offers, country)
            
            # Combine offers from H5 and Solr
            job_offers.extend(solr_offers)
        
        if not job_offers:
            logger.info(f"No job offers found for {country} that need translation")
            return False, None
        
        # Step 2: Prepare batch file
        batch_file_path = prepare_batch_file(job_offers, country)
        if not batch_file_path:
            logger.error("Failed to prepare batch file")
            return False, None
        
        # Take snapshot of batch preparation
        # Count the number of requests in the batch file
        request_count = 0
        try:
            with open(batch_file_path, 'r', encoding='utf-8') as f:
                request_count = sum(1 for _ in f)
        except Exception as e:
            logger.warning(f"Error counting requests in batch file: {e}")
            
        take_snapshot("translation_prep", job_offers, {
            "batch_file": os.path.basename(batch_file_path),
            "job_offers_count": len(job_offers),
            "request_count": request_count,
            "avg_description_length": sum(len(o.get("sm_field_offre_description_poste", [""])[0]) 
                                       for o in job_offers if o.get("sm_field_offre_description_poste")) / 
                                       max(sum(1 for o in job_offers if o.get("sm_field_offre_description_poste")), 1),
            "avg_profile_length": sum(len(o.get("sm_field_offre_profil", [""])[0]) 
                                   for o in job_offers if o.get("sm_field_offre_profil")) / 
                                   max(sum(1 for o in job_offers if o.get("sm_field_offre_profil")), 1)
        }, country=country, run_id=run_id)
        
        # Step 3: Process batch
        client = OpenAI()
        result = process_batch(batch_file_path, client, country=country, wait_for_completion=wait_for_completion)
        
        if not result:
            logger.error("Failed to process batch")
            return False, None
        
        # If we're not waiting for completion, return success with the info file path
        if not wait_for_completion:
            logger.info(f"Batch job submitted successfully for {country}. It will be processed in the background.")
            logger.info(f"Run the 'check_pending_batch_jobs()' function later to check its status.")
            return True, result
        
        # If we are waiting for completion, continue with processing the results
        output_file_path = result
        
        # Step 4: Process batch results
        translated_offers = process_batch_results(output_file_path, job_offers)
        if not translated_offers:
            logger.error("Failed to process batch results")
            return False, None
        
        # Take snapshot of translation results
        take_snapshot("translation", translated_offers, {
            "translated_count": len(translated_offers),
            "original_count": len(job_offers),
            "success_rate": len(translated_offers) / max(len(job_offers), 1) * 100,
            "avg_description_translation_length": sum(len(o.get("tm_offre_description_poste", [""])[0]) 
                                                for o in translated_offers if o.get("tm_offre_description_poste")) / 
                                                max(sum(1 for o in translated_offers if o.get("tm_offre_description_poste")), 1),
            "avg_profile_translation_length": sum(len(o.get("tm_offre_profil", [""])[0]) 
                                            for o in translated_offers if o.get("tm_offre_profil")) / 
                                            max(sum(1 for o in translated_offers if o.get("tm_offre_profil")), 1)
        }, country=country, run_id=run_id)
        
        # Step 5: Save translations to JSON
        results_path = save_translations_to_json(translated_offers, country)
        if not results_path:
            logger.error("Failed to save translations to JSON")
            return False, None
        
        # Step 6: Update H5 storage with translations if enabled
        if CONFIG['h5_storage']['enabled']:
            logger.info(f"Updating H5 storage with {len(translated_offers)} translated offers for {country}")
            update_result = update_job_offers_in_h5(translated_offers, country)
            if not update_result:
                logger.warning(f"Failed to update H5 storage for {country}")
        
        # Step 7: Update Solr (if requested)
        if update_solr:
            # Use the configured batch size for Solr updates
            solr_batch_size = CONFIG['solr']['update']['batch_size']
            result = update_solr_with_translations(translated_offers, country, batch_size=solr_batch_size)
            
            # Take snapshot of Solr update
            take_snapshot("solr_update", translated_offers, {
                "update_count": len(translated_offers),
                "success": result is True,
                "batch_size": solr_batch_size,
                "commit_within": CONFIG['solr']['update']['commit_within'],
                "final_commit": CONFIG['solr']['update']['final_commit']
            }, country=country, run_id=run_id)
            
            if not result:
                logger.error("Failed to update Solr with translations")
                return False, results_path
        
        # Take snapshot of pipeline completion
        take_snapshot("pipeline_complete", [], {
            "success": True,
            "total_time": (datetime.now() - datetime.strptime(run_id, "%Y%m%d_%H%M%S")).total_seconds(),
            "results_path": os.path.basename(results_path) if results_path else None,
            "job_offers_processed": len(job_offers),
            "translations_created": len(translated_offers),
            "solr_updated": update_solr
        }, country=country, run_id=run_id)
        
        logger.info(f"Batch translation completed successfully for {country}")
        return True, results_path
    except Exception as e:
        logger.error(f"Error running batch translation: {e}")
        return False, None
    finally:
        # Close H5 file for this country if open
        if CONFIG['h5_storage']['enabled']:
            close_h5_file(country)

def test_direct_solr_query():
    """
    Test the direct Solr query function.
    """
    print("Testing direct Solr query...\n")
    
    # Test with a specific country
    country = 'algerie'  # Change to the country you want to test
    
    # Set up query parameters exactly matching the working URL format
    params = {
        'q': '*:*',
        'fq': '*:*',
        'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',
        'rows': 5,
        'start': 0,
        'sort': 'entity_id asc',
        'wt': 'json'
    }
    
    # Construct the URL manually for logging
    solr_url = f"{SOLR_BASE_URL}core_jobsearch_{country}/select"
    query_params = '&'.join([f"{k}={v}" for k, v in params.items()])
    full_url = f"{solr_url}?{query_params}"
    print(f"Full query URL: {full_url}\n")
    
    # Execute the query
    result = query_solr_direct(country, params)
    
    if result:
        response = result.get('response', {})
        num_found = response.get('numFound', 0)
        docs = response.get('docs', [])
        
        print(f"Query successful! Found {num_found} documents.\n")
        
        if docs:
            print(f"First {len(docs)} documents:")
            for i, doc in enumerate(docs):
                print(f"\nDocument {i+1}:")
                print(f"ID: {doc.get('id', 'unknown')}")
                print(f"Entity ID: {doc.get('entity_id', 'unknown')}")
                
                # Check if translated fields already exist
                has_translations = any(target_field in doc for target_field in SOURCE_FIELDS.values())
                print(f"Has translations: {has_translations}")
                
                # Show a sample of the source fields
                for source_field in SOURCE_FIELDS.keys():
                    if source_field in doc:
                        value = doc[source_field]
                        if isinstance(value, list) and value:
                            sample = value[0][:100] + "..." if len(value[0]) > 100 else value[0]
                            print(f"\n{source_field}: {sample}")
    else:
        print("Query failed. Check the logs for details.")

def test_filter_query():
    """
    Test the filter query for excluding documents with translations.
    """
    print("Testing filter query for excluding documents with translations...\n")
    
    # Test with a specific country
    country = 'algerie'  # Change to the country you want to test
    
    # Create filter query
    translation_filters = []
    for target_field in SOURCE_FIELDS.values():
        translation_filters.append(f"-{target_field}:[* TO *]")
    
    filter_query = " AND ".join(translation_filters)
    print(f"Filter query: {filter_query}\n")
    
    # Set up query parameters
    params = {
        'q': '*:*',
        'fq': filter_query,
        'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',
        'rows': 5,
        'start': 0,
        'sort': 'entity_id asc',
        'wt': 'json'
    }
    
    # Construct the URL manually for logging
    solr_url = f"{SOLR_BASE_URL}core_jobsearch_{country}/select"
    query_params = '&'.join([f"{k}={v}" for k, v in params.items()])
    full_url = f"{solr_url}?{query_params}"
    print(f"Full query URL: {full_url}\n")
    
    # Execute the query
    result = query_solr_direct(country, params)
    
    if result:
        response = result.get('response', {})
        num_found = response.get('numFound', 0)
        docs = response.get('docs', [])
        
        print(f"Query successful! Found {num_found} documents that need translation.\n")
        
        if docs:
            print(f"First {len(docs)} documents:")
            for i, doc in enumerate(docs):
                print(f"\nDocument {i+1}:")
                print(f"ID: {doc.get('id', 'unknown')}")
                print(f"Entity ID: {doc.get('entity_id', 'unknown')}")
                
                # Verify no translated fields exist
                has_translations = any(target_field in doc for target_field in SOURCE_FIELDS.values())
                print(f"Has translations: {has_translations} (should be False)")
    else:
        print("Query failed. Check the logs for details.")

# Run the direct Solr query test
test_direct_solr_query()

# Test the filter query
test_filter_query()

# Set up OpenAI API key
import os
from getpass import getpass

# Ask for API key if not already set
if not os.environ.get("OPENAI_API_KEY"):
    api_key = getpass("Enter your OpenAI API key: ")
    os.environ["OPENAI_API_KEY"] = api_key
    print("API key set successfully!")
else:
    print("OpenAI API key already set in environment variables.")

# Configuration Management Functions
def display_config():
    """
    Display the current configuration.
    """
    print("\n📋 Current Configuration:\n")
    print(f"Version: {CONFIG['general']['config_version']}")
    print(f"Last Updated: {CONFIG['general']['last_updated']}\n")
    
    print("General Settings:")
    print(f"  Default Batch Size: {CONFIG['general']['default_batch_size']}\n")
    
    print("Country-Specific Batch Sizes:")
    for country, size in CONFIG['country_specific']['batch_sizes'].items():
        print(f"  {country}: {size}")
    print()
    
    print("OpenAI Settings:")
    print(f"  Model: {CONFIG['openai']['models']['default']}")
    print(f"  Temperature: {CONFIG['openai']['translation']['temperature']}")
    print(f"  Batch API Max Wait Time: {CONFIG['openai']['batch_api']['max_wait_time']} seconds")
    print(f"  Retry Count: {CONFIG['openai']['batch_api']['retry_count']}\n")
    
    print("Solr Settings:")
    print(f"  Connection Timeout: {CONFIG['solr']['connection']['timeout']} seconds")
    print(f"  Rows Per Query: {CONFIG['solr']['query']['rows_per_query']}")
    print(f"  Sort Field: {CONFIG['solr']['query']['sort_field']} {CONFIG['solr']['query']['sort_order']}")
    print(f"  Update Batch Size: {CONFIG['solr']['update']['batch_size']}")
    print(f"  Commit Within: {CONFIG['solr']['update']['commit_within']} ms")
    print(f"  Final Commit: {CONFIG['solr']['update']['final_commit']}\n")
    
    print("Error Handling:")
    print(f"  Max Consecutive Errors: {CONFIG['error_handling']['max_consecutive_errors']}")
    print(f"  Error Threshold Percentage: {CONFIG['error_handling']['error_threshold_percentage']}%")
    print(f"  Continue on Error: {CONFIG['error_handling']['continue_on_error']}\n")
    
    print("Logging:")
    print(f"  Level: {CONFIG['logging']['level']}")
    print(f"  File Logging: {CONFIG['logging']['file']['enabled']}")
    if CONFIG['logging']['file']['enabled']:
        print(f"  Log File: {CONFIG['logging']['file']['path']}")

def edit_config():
    """
    Edit the configuration.
    """
    global CONFIG
    
    print("\n⚙️ Configuration Editor\n")
    print("Select a section to edit:")
    print("1. General Settings")
    print("2. Country-Specific Batch Sizes")
    print("3. OpenAI Settings")
    print("4. Solr Settings")
    print("5. Error Handling")
    print("6. Logging")
    print("7. Save and Exit")
    print("8. Exit Without Saving")
    
    choice = input("\nEnter your choice (1-8): ")
    
    if choice == "1":
        # Edit General Settings
        print("\nEditing General Settings:\n")
        CONFIG['general']['default_batch_size'] = int(input(f"Default Batch Size [{CONFIG['general']['default_batch_size']}]: ") or CONFIG['general']['default_batch_size'])
        edit_config()  # Return to the menu
    
    elif choice == "2":
        # Edit Country-Specific Batch Sizes
        print("\nEditing Country-Specific Batch Sizes:\n")
        print("Current country-specific batch sizes:")
        for i, (country, size) in enumerate(CONFIG['country_specific']['batch_sizes'].items()):
            print(f"{i+1}. {country}: {size}")
        print(f"{len(CONFIG['country_specific']['batch_sizes'])+1}. Add a new country")
        print(f"{len(CONFIG['country_specific']['batch_sizes'])+2}. Return to main menu")
        
        sub_choice = input("\nEnter your choice: ")
        try:
            sub_choice = int(sub_choice)
            if 1 <= sub_choice <= len(CONFIG['country_specific']['batch_sizes']):
                # Edit existing country
                country = list(CONFIG['country_specific']['batch_sizes'].keys())[sub_choice-1]
                new_size = int(input(f"New batch size for {country} [{CONFIG['country_specific']['batch_sizes'][country]}]: ") or CONFIG['country_specific']['batch_sizes'][country])
                CONFIG['country_specific']['batch_sizes'][country] = new_size
            elif sub_choice == len(CONFIG['country_specific']['batch_sizes'])+1:
                # Add a new country
                print("\nAvailable countries:")
                available_countries = [c for c in COUNTRIES if c not in CONFIG['country_specific']['batch_sizes']]
                if available_countries:
                    for i, country in enumerate(available_countries):
                        print(f"{i+1}. {country}")
                    country_idx = int(input("\nSelect a country: ")) - 1
                    if 0 <= country_idx < len(available_countries):
                        country = available_countries[country_idx]
                        size = int(input(f"Batch size for {country} [{CONFIG['general']['default_batch_size']}]: ") or CONFIG['general']['default_batch_size'])
                        CONFIG['country_specific']['batch_sizes'][country] = size
                    else:
                        print("Invalid selection")
                else:
                    print("All countries already have specific batch sizes.")
        except ValueError:
            print("Invalid input. Please enter a number.")
        
        edit_config()  # Return to the menu
    
    elif choice == "3":
        # Edit OpenAI Settings
        print("\nEditing OpenAI Settings:\n")
        print("1. Change Model")
        print("2. Change Temperature")
        print("3. Edit System Prompt")
        print("4. Edit User Prompt Template")
        print("5. Edit Batch API Settings")
        print("6. Return to main menu")
        
        sub_choice = input("\nEnter your choice (1-6): ")
        
        if sub_choice == "1":
            # Change Model
            print("\nAvailable models:")
            print(f"1. {CONFIG['openai']['models']['default']} (current)")
            for i, model in enumerate(CONFIG['openai']['models']['alternatives']):
                print(f"{i+2}. {model}")
            model_idx = int(input("\nSelect a model: ")) - 1
            if model_idx == 0:
                pass  # Keep current model
            elif 1 <= model_idx <= len(CONFIG['openai']['models']['alternatives']):
                # Swap current model with selected alternative
                current = CONFIG['openai']['models']['default']
                new_model = CONFIG['openai']['models']['alternatives'][model_idx-1]
                CONFIG['openai']['models']['default'] = new_model
                CONFIG['openai']['models']['alternatives'][model_idx-1] = current
                print(f"Model changed to {new_model}")
            else:
                print("Invalid selection")
        
        elif sub_choice == "2":
            # Change Temperature
            temp = float(input(f"Temperature (0.0-1.0) [{CONFIG['openai']['translation']['temperature']}]: ") or CONFIG['openai']['translation']['temperature'])
            if 0.0 <= temp <= 1.0:
                CONFIG['openai']['translation']['temperature'] = temp
                print(f"Temperature set to {temp}")
            else:
                print("Invalid temperature. Must be between 0.0 and 1.0.")
        
        elif sub_choice == "3":
            # Edit System Prompt
            print(f"\nCurrent system prompt: {CONFIG['openai']['translation']['system_prompt']}")
            new_prompt = input("New system prompt (press Enter to keep current): ")
            if new_prompt:
                CONFIG['openai']['translation']['system_prompt'] = new_prompt
                print("System prompt updated.")
        
        elif sub_choice == "4":
            # Edit User Prompt Template
            print(f"\nCurrent user prompt template: {CONFIG['openai']['translation']['user_prompt_template']}")
            print("Note: The template must include {text} placeholder.")
            new_template = input("New user prompt template (press Enter to keep current): ")
            if new_template:
                if "{text}" in new_template:
                    CONFIG['openai']['translation']['user_prompt_template'] = new_template
                    print("User prompt template updated.")
                else:
                    print("Error: Template must include {text} placeholder.")
        
        elif sub_choice == "5":
            # Edit Batch API Settings
            print("\nEditing Batch API Settings:\n")
            CONFIG['openai']['batch_api']['max_wait_time'] = int(input(f"Max Wait Time (seconds) [{CONFIG['openai']['batch_api']['max_wait_time']}]: ") or CONFIG['openai']['batch_api']['max_wait_time'])
            CONFIG['openai']['batch_api']['wait_interval'] = int(input(f"Wait Interval (seconds) [{CONFIG['openai']['batch_api']['wait_interval']}]: ") or CONFIG['openai']['batch_api']['wait_interval'])
            CONFIG['openai']['batch_api']['retry_count'] = int(input(f"Retry Count [{CONFIG['openai']['batch_api']['retry_count']}]: ") or CONFIG['openai']['batch_api']['retry_count'])
            CONFIG['openai']['batch_api']['retry_delay'] = int(input(f"Retry Delay (seconds) [{CONFIG['openai']['batch_api']['retry_delay']}]: ") or CONFIG['openai']['batch_api']['retry_delay'])
        
        edit_config()  # Return to the menu
    
    elif choice == "4":
        # Edit Solr Settings
        print("\nEditing Solr Settings:\n")
        print("1. Connection Settings")
        print("2. Query Settings")
        print("3. Update Settings")
        print("4. Return to main menu")
        
        sub_choice = input("\nEnter your choice (1-4): ")
        
        if sub_choice == "1":
            # Edit Connection Settings
            print("\nEditing Connection Settings:\n")
            CONFIG['solr']['connection']['timeout'] = int(input(f"Connection Timeout (seconds) [{CONFIG['solr']['connection']['timeout']}]: ") or CONFIG['solr']['connection']['timeout'])
            new_url = input(f"Base URL [{CONFIG['solr']['connection']['base_url']}]: ")
            if new_url:
                CONFIG['solr']['connection']['base_url'] = new_url
        
        elif sub_choice == "2":
            # Edit Query Settings
            print("\nEditing Query Settings:\n")
            CONFIG['solr']['query']['rows_per_query'] = int(input(f"Rows Per Query [{CONFIG['solr']['query']['rows_per_query']}]: ") or CONFIG['solr']['query']['rows_per_query'])
            CONFIG['solr']['query']['sort_field'] = input(f"Sort Field [{CONFIG['solr']['query']['sort_field']}]: ") or CONFIG['solr']['query']['sort_field']
            sort_order = input(f"Sort Order (asc/desc) [{CONFIG['solr']['query']['sort_order']}]: ") or CONFIG['solr']['query']['sort_order']
            if sort_order in ['asc', 'desc']:
                CONFIG['solr']['query']['sort_order'] = sort_order
            else:
                print("Invalid sort order. Must be 'asc' or 'desc'.")
        
        elif sub_choice == "3":
            # Edit Update Settings
            print("\nEditing Update Settings:\n")
            CONFIG['solr']['update']['batch_size'] = int(input(f"Update Batch Size [{CONFIG['solr']['update']['batch_size']}]: ") or CONFIG['solr']['update']['batch_size'])
            CONFIG['solr']['update']['commit_within'] = int(input(f"Commit Within (ms) [{CONFIG['solr']['update']['commit_within']}]: ") or CONFIG['solr']['update']['commit_within'])
            final_commit = input(f"Final Commit (true/false) [{CONFIG['solr']['update']['final_commit']}]: ").lower() or str(CONFIG['solr']['update']['final_commit']).lower()
            if final_commit in ['true', 'false']:
                CONFIG['solr']['update']['final_commit'] = final_commit == 'true'
            else:
                print("Invalid input. Must be 'true' or 'false'.")
        
        edit_config()  # Return to the menu
    
    elif choice == "5":
        # Edit Error Handling
        print("\nEditing Error Handling Settings:\n")
        CONFIG['error_handling']['max_consecutive_errors'] = int(input(f"Max Consecutive Errors [{CONFIG['error_handling']['max_consecutive_errors']}]: ") or CONFIG['error_handling']['max_consecutive_errors'])
        CONFIG['error_handling']['error_threshold_percentage'] = int(input(f"Error Threshold Percentage [{CONFIG['error_handling']['error_threshold_percentage']}]: ") or CONFIG['error_handling']['error_threshold_percentage'])
        continue_on_error = input(f"Continue on Error (true/false) [{CONFIG['error_handling']['continue_on_error']}]: ").lower() or str(CONFIG['error_handling']['continue_on_error']).lower()
        if continue_on_error in ['true', 'false']:
            CONFIG['error_handling']['continue_on_error'] = continue_on_error == 'true'
        else:
            print("Invalid input. Must be 'true' or 'false'.")
        
        edit_config()  # Return to the menu
    
    elif choice == "6":
        # Edit Logging
        print("\nEditing Logging Settings:\n")
        print("Available log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL")
        level = input(f"Log Level [{CONFIG['logging']['level']}]: ").upper() or CONFIG['logging']['level']
        if level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            CONFIG['logging']['level'] = level
        else:
            print("Invalid log level.")
        
        file_logging = input(f"Enable File Logging (true/false) [{CONFIG['logging']['file']['enabled']}]: ").lower() or str(CONFIG['logging']['file']['enabled']).lower()
        if file_logging in ['true', 'false']:
            CONFIG['logging']['file']['enabled'] = file_logging == 'true'
            if file_logging == 'true':
                CONFIG['logging']['file']['path'] = input(f"Log File Path [{CONFIG['logging']['file']['path']}]: ") or CONFIG['logging']['file']['path']
                CONFIG['logging']['file']['max_size'] = int(input(f"Max Log Size (bytes) [{CONFIG['logging']['file']['max_size']}]: ") or CONFIG['logging']['file']['max_size'])
                CONFIG['logging']['file']['backup_count'] = int(input(f"Backup Count [{CONFIG['logging']['file']['backup_count']}]: ") or CONFIG['logging']['file']['backup_count'])
        else:
            print("Invalid input. Must be 'true' or 'false'.")
        
        edit_config()  # Return to the menu
    
    elif choice == "7":
        # Save and Exit
        save_config(CONFIG)
        print("Configuration saved.")
        return
    
    elif choice == "8":
        # Exit Without Saving
        print("Exiting without saving changes.")
        # Reload the configuration to discard changes
        CONFIG = load_config()
        return
    
    else:
        print("Invalid choice.")
        edit_config()  # Return to the menu

# Run the translation pipeline
def run_pipeline():
    # Ask for country
    print("\n🌍 Available countries:")
    for i, country in enumerate(COUNTRIES):
        batch_size = get_batch_size(country)
        print(f"{i+1}. {country} (batch size: {batch_size})")
    
    try:
        country_idx = int(input(f"\nSelect a country (1-{len(COUNTRIES)}): ")) - 1
        if country_idx < 0 or country_idx >= len(COUNTRIES):
            print("Invalid selection")
            return
        
        country = COUNTRIES[country_idx]
        default_batch_size = get_batch_size(country)
        
        # Ask for batch size
        batch_size_input = input(f"Enter batch size (default: {default_batch_size}): ")
        batch_size = int(batch_size_input) if batch_size_input else None  # None will use the country-specific batch size
        
        # Ask whether to update Solr
        update_solr_input = input("Update Solr with translations? (y/n): ").lower()
        update_solr = update_solr_input == 'y'
        
        # Ask whether to wait for completion
        wait_input = input("Wait for batch job to complete? (y/n): ").lower()
        wait_for_completion = wait_input == 'y'
        
        # Run the pipeline
        print(f"\n🚀 Running translation pipeline for {country}...")
        if batch_size:
            print(f"Using custom batch size: {batch_size}")
        else:
            print(f"Using country-specific batch size: {default_batch_size}")
        print(f"OpenAI Model: {get_openai_model()}")
        print(f"Update Solr: {update_solr}")
        print(f"Wait for completion: {wait_for_completion}")
        
        success, results_path = run_batch_translation(country, batch_size=batch_size, update_solr=update_solr, wait_for_completion=wait_for_completion)
        
        if success:
            print(f"\n✅ Translation pipeline completed successfully!")
            if results_path:
                print(f"Results saved to: {results_path}")
        else:
            print(f"\n❌ Translation pipeline failed. Check the logs for details.")
    except ValueError:
        print("Invalid input. Please enter a number.")

# Main menu function
def main_menu():
    try:
        print("\n🔄 Job Offer Translation Pipeline\n")
        print("1. Run Translation Pipeline")
        print("2. Display Configuration")
        print("3. Edit Configuration")
        print("4. Test Direct Solr Query")
        print("5. Test Filter Query")
        print("6. H5 Storage Management")
        print("7. Check Pending Batch Jobs")
        print("8. Exit")
        
        choice = input("\nEnter your choice (1-8): ")
        
        if choice == "1":
            run_pipeline()
            main_menu()
        elif choice == "2":
            display_config()
            main_menu()
        elif choice == "3":
            edit_config()
            main_menu()
        elif choice == "4":
            test_direct_solr_query()
            main_menu()
        elif choice == "5":
            test_filter_query()
            main_menu()
        elif choice == "6":
            h5_storage_menu()
            main_menu()
        elif choice == "7":
            check_pending_batch_jobs()
            main_menu()
        elif choice == "8":
            print("Exiting...")
            return
        else:
            print("Invalid choice.")
            main_menu()
    finally:
        # Clean up H5 files when exiting
        if 'h5_storage' in CONFIG and CONFIG['h5_storage']['enabled']:
            count = close_all_h5_files()
            if count > 0:
                print(f"\nClosed {count} H5 files.")

# H5 storage management menu
def h5_storage_menu():
    """
    Display the H5 storage management menu and handle user input.
    """
    while True:
        print("\n" + "=" * 50)
        print("💾 H5 Storage Management")
        print("=" * 50)
        print(f"H5 storage is currently {'enabled' if CONFIG['h5_storage']['enabled'] else 'disabled'}")
        print("1. List H5 Files")
        print("2. View H5 File Contents")
        print("3. Close All H5 Files")
        print("4. Toggle H5 Storage")
        print("0. Back to Main Menu")
        
        choice = input("\nEnter your choice (0-4): ")
        
        if choice == "1":
            list_h5_files()
        elif choice == "2":
            view_h5_file_contents()
        elif choice == "3":
            count = close_all_h5_files()
            print(f"\nClosed {count} H5 files.")
        elif choice == "4":
            CONFIG['h5_storage']['enabled'] = not CONFIG['h5_storage']['enabled']
            save_config(CONFIG)
            print(f"\nH5 storage is now {'enabled' if CONFIG['h5_storage']['enabled'] else 'disabled'}.")
        elif choice == "0":
            break
        else:
            print("\n❌ Invalid choice. Please try again.")

def list_h5_files():
    """
    List all H5 files in the H5 storage directory.
    """
    print("\n" + "=" * 50)
    print("📁 H5 Files")
    print("=" * 50)
    
    h5_files_list = [f for f in os.listdir(H5_DIR) if f.endswith('.h5')]
    
    if not h5_files_list:
        print("No H5 files found.")
        return
    
    print(f"Found {len(h5_files_list)} H5 files:")
    for i, file_name in enumerate(h5_files_list):
        file_path = os.path.join(H5_DIR, file_name)
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB
        print(f"{i+1}. {file_name} ({file_size:.2f} MB)")

def view_h5_file_contents():
    """
    View the contents of an H5 file.
    """
    h5_files_list = [f for f in os.listdir(H5_DIR) if f.endswith('.h5')]
    
    if not h5_files_list:
        print("\nNo H5 files found.")
        return
    
    print("\n" + "=" * 50)
    print("📁 H5 Files")
    print("=" * 50)
    
    for i, file_name in enumerate(h5_files_list):
        print(f"{i+1}. {file_name}")
    
    try:
        choice = int(input("\nEnter the number of the file to view (0 to cancel): "))
        
        if choice == 0:
            return
        
        if choice < 1 or choice > len(h5_files_list):
            print("\n❌ Invalid choice.")
            return
        
        file_name = h5_files_list[choice - 1]
        file_path = os.path.join(H5_DIR, file_name)
        
        # Extract country from file name
        country = file_name.split('.')[0]
        
        # Open the H5 file
        with h5py.File(file_path, 'r') as h5_file:
            # Check if the job_offers group exists
            if 'job_offers' not in h5_file:
                print(f"\nNo job offers found in {file_name}.")
                return
            
            # Get the job_offers group
            job_offers_group = h5_file['job_offers']
            
            # Get all offer IDs
            offer_ids = list(job_offers_group.keys())
            
            print(f"\nFound {len(offer_ids)} job offers in {file_name}.")
            
            # Count offers with translations
            translated_count = 0
            for offer_id in offer_ids:
                offer_group = job_offers_group[offer_id]
                has_translations = any(target_field in offer_group for target_field in SOURCE_FIELDS.values())
                if has_translations:
                    translated_count += 1
            
            print(f"Translated: {translated_count} ({translated_count/len(offer_ids)*100:.2f}%)")
            print(f"Untranslated: {len(offer_ids) - translated_count} ({(len(offer_ids) - translated_count)/len(offer_ids)*100:.2f}%)")
            
            # Ask if user wants to view a specific offer
            view_offer = input("\nView a specific offer? (y/n): ")
            if view_offer.lower() == 'y':
                offer_index = int(input(f"Enter offer index (1-{len(offer_ids)}): ")) - 1
                if offer_index < 0 or offer_index >= len(offer_ids):
                    print("\n❌ Invalid index.")
                    return
                
                offer_id = offer_ids[offer_index]
                offer_group = job_offers_group[offer_id]
                
                print(f"\nOffer ID: {offer_id}")
                print("=" * 50)
                
                for field_name in offer_group.keys():
                    value = offer_group[field_name][()]
                    if isinstance(value, bytes):
                        value = value.decode('utf-8')
                    
                    # Truncate long values
                    if len(str(value)) > 100:
                        print(f"{field_name}: {str(value)[:100]}...")
                    else:
                        print(f"{field_name}: {value}")
    except Exception as e:
        print(f"\n❌ Error viewing H5 file: {e}")

# Import menu functions
try:
    from menu_functions import run_interactive_menu
    print("✅ Menu functions successfully imported")
except ImportError:
    print("❌ Menu functions not found. Make sure menu_functions.py is in the same directory.")
    
# Import Solr test functions
try:
    from solr_test_functions import test_solr_connection
    print("✅ Solr test functions successfully imported")
except ImportError:
    print("❌ Solr test functions not found. Make sure solr_test_functions.py is in the same directory.")
    # Create a simple fallback function
    def test_solr_connection():
        print("\nSolr test function not available. Please create solr_test_functions.py first.")
        
# Import Solr update functions
try:
    from solr_update_functions import update_solr_with_translations, test_solr_update, verify_solr_update
    print("✅ Solr update functions successfully imported")
    # Replace the existing update_solr_with_translations function with the imported one
    print("✅ Replaced existing Solr update function with improved version")
except ImportError:
    print("❌ Solr update functions not found. Make sure solr_update_functions.py is in the same directory.")

# Run the interactive menu
run_interactive_menu(COUNTRIES, CONFIG, run_pipeline, check_pending_batch_jobs, 
                    test_solr_connection, display_config, edit_config,
                    generate_pipeline_report, analyze_pipeline_performance)