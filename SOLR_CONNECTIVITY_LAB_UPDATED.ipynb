{"cells": [{"cell_type": "markdown", "id": "title-section", "metadata": {}, "source": ["# Solr Connectivity and Query Construction Lab\n", "\n", "This notebook is dedicated to testing and validating Solr connectivity and query construction for the Job Offer Translation Pipeline. We'll explore various aspects of Solr interaction to ensure robustness in the main pipeline."]}, {"cell_type": "code", "execution_count": 1, "id": "imports-section", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import logging\n", "import os\n", "import pysolr\n", "import requests\n", "import time\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from urllib.parse import quote_plus, urlparse, parse_qs\n", "from tqdm.notebook import tqdm\n", "from datetime import datetime\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('solr_lab')"]}, {"cell_type": "code", "execution_count": 2, "id": "config-section", "metadata": {}, "outputs": [], "source": ["# Solr Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "LOCAL_SOLR_URL = \"http://localhost:8983/solr/\"\n", "\n", "# Default timeout for requests (in seconds)\n", "DEFAULT_TIMEOUT = 30\n", "\n", "# Fields we're interested in for job offers\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "# Countries to test with\n", "TEST_COUNTRIES = ['algerie']\n", "\n", "# Test parameters\n", "MAX_TEST_DOCS = 1000  # Maximum number of documents to retrieve for testing"]}, {"cell_type": "markdown", "id": "basic-connectivity-section", "metadata": {}, "source": ["## 1. Basic Connectivity Tests\n", "\n", "First, let's test basic connectivity to the Solr instance and verify we can access the cores we need."]}, {"cell_type": "code", "execution_count": 3, "id": "solr-info-function", "metadata": {}, "outputs": [], "source": ["def get_solr_info(base_url=SOLR_BASE_URL, timeout=DEFAULT_TIMEOUT):\n", "    \"\"\"\n", "    Get basic information about the Solr instance.\n", "    \n", "    Args:\n", "        base_url (str): Base URL of the Solr instance\n", "        timeout (int): Timeout in seconds for the request\n", "        \n", "    Returns:\n", "        dict: Information about the Solr instance\n", "    \"\"\"\n", "    try:\n", "        # Get Solr admin info\n", "        admin_url = f\"{base_url}admin/info/system\"\n", "        logger.info(f\"Connecting to Solr admin at {admin_url} with timeout {timeout}s\")\n", "        response = requests.get(admin_url, params={'wt': 'json'}, timeout=timeout)\n", "        \n", "        if response.status_code != 200:\n", "            logger.error(f\"Failed to get Solr info. Status code: {response.status_code}\")\n", "            return None\n", "        \n", "        return response.json()\n", "    except requests.exceptions.Timeout:\n", "        logger.error(f\"Connection to {base_url} timed out after {timeout} seconds\")\n", "        return None\n", "    except requests.exceptions.ConnectionError as e:\n", "        logger.error(f\"Connection error to {base_url}: {e}\")\n", "        return None\n", "    except Exception as e:\n", "        logger.error(f\"Error getting Solr info: {e}\")\n", "        return None\n", "\n", "def list_solr_cores(base_url=SOLR_BASE_URL, timeout=DEFAULT_TIMEOUT):\n", "    \"\"\"\n", "    List all cores in the Solr instance.\n", "    \n", "    Args:\n", "        base_url (str): Base URL of the Solr instance\n", "        timeout (int): Timeout in seconds for the request\n", "        \n", "    Returns:\n", "        list: List of core names\n", "    \"\"\"\n", "    try:\n", "        # Get list of cores\n", "        cores_url = f\"{base_url}admin/cores\"\n", "        logger.info(f\"Listing Solr cores from {cores_url} with timeout {timeout}s\")\n", "        response = requests.get(cores_url, params={'action': 'STATUS', 'wt': 'json'}, timeout=timeout)\n", "        \n", "        if response.status_code != 200:\n", "            logger.error(f\"Failed to list Solr cores. Status code: {response.status_code}\")\n", "            return []\n", "        \n", "        data = response.json()\n", "        return list(data.get('status', {}).keys())\n", "    except requests.exceptions.Timeout:\n", "        logger.error(f\"Connection to {base_url} timed out after {timeout} seconds\")\n", "        return []\n", "    except requests.exceptions.ConnectionError as e:\n", "        logger.error(f\"Connection error to {base_url}: {e}\")\n", "        return []\n", "    except Exception as e:\n", "        logger.error(f\"Error listing Solr cores: {e}\")\n", "        return []\n", "\n", "def test_core_connectivity(core_name, base_url=SOLR_BASE_URL, timeout=DEFAULT_TIMEOUT):\n", "    \"\"\"\n", "    Test connectivity to a specific Solr core.\n", "    \n", "    Args:\n", "        core_name (str): Name of the Solr core\n", "        base_url (str): Base URL of the Solr instance\n", "        timeout (int): Timeout in seconds for the request\n", "        \n", "    Returns:\n", "        dict: Information about the core\n", "    \"\"\"\n", "    try:\n", "        # Test a simple query\n", "        core_url = f\"{base_url}{core_name}/select\"\n", "        logger.info(f\"Testing connectivity to core {core_name} at {core_url} with timeout {timeout}s\")\n", "        response = requests.get(core_url, params={'q': '*:*', 'rows': '1', 'wt': 'json'}, timeout=timeout)\n", "        \n", "        if response.status_code != 200:\n", "            logger.error(f\"Failed to connect to core {core_name}. Status code: {response.status_code}\")\n", "            return None\n", "        \n", "        data = response.json()\n", "        return {\n", "            'core_name': core_name,\n", "            'num_found': data['response']['numFound'],\n", "            'status': 'OK' if response.status_code == 200 else 'Error',\n", "            'query_time_ms': data['responseHeader']['QTime']\n", "        }\n", "    except requests.exceptions.Timeout:\n", "        logger.error(f\"Connection to core {core_name} timed out after {timeout} seconds\")\n", "        return {\n", "            'core_name': core_name,\n", "            'status': 'Error',\n", "            'error': f\"Timeout after {timeout} seconds\"\n", "        }\n", "    except requests.exceptions.ConnectionError as e:\n", "        logger.error(f\"Connection error to core {core_name}: {e}\")\n", "        return {\n", "            'core_name': core_name,\n", "            'status': 'Error',\n", "            'error': f\"Connection error: {str(e)}\"\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error testing core {core_name}: {e}\")\n", "        return {\n", "            'core_name': core_name,\n", "            'status': 'Error',\n", "            'error': str(e)\n", "        }"]}, {"cell_type": "code", "execution_count": 4, "id": "run-basic-connectivity", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:42:51,766 - INFO - Connecting to Solr admin at http://************:8983/solr/admin/info/system with timeout 5s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Testing Solr connectivity...\n", "\n", "\n", "Trying with timeout = 5 seconds...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:42:56,772 - ERROR - Connection to http://************:8983/solr/ timed out after 5 seconds\n", "2025-05-13 11:42:56,776 - INFO - Connecting to Solr admin at http://************:8983/solr/admin/info/system with timeout 15s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Could not retrieve Solr information with timeout = 5 seconds.\n", "\n", "Trying with timeout = 15 seconds...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:43:11,784 - ERROR - Connection to http://************:8983/solr/ timed out after 15 seconds\n", "2025-05-13 11:43:11,785 - INFO - Connecting to Solr admin at http://************:8983/solr/admin/info/system with timeout 30s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Could not retrieve Solr information with timeout = 15 seconds.\n", "\n", "Trying with timeout = 30 seconds...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:43:32,821 - ERROR - Connection to http://************:8983/solr/ timed out after 30 seconds\n", "2025-05-13 11:43:32,834 - INFO - Connecting to Solr admin at http://localhost:8983/solr/admin/info/system with timeout 30s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Could not retrieve Solr information with timeout = 30 seconds.\n", "\n", "Trying with local Solr...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:43:36,939 - ERROR - Connection error to http://localhost:8983/solr/: HTTPConnectionPool(host='localhost', port=8983): Max retries exceeded with url: /solr/admin/info/system?wt=json (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002579E841810>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\n", "2025-05-13 11:43:36,944 - INFO - Listing Solr cores from http://************:8983/solr/admin/cores with timeout 30s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Could not connect to local Solr either. Please check your Solr installation.\n", "\n", "Listing Solr cores...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:43:57,986 - ERROR - Connection to http://************:8983/solr/ timed out after 30 seconds\n", "2025-05-13 11:43:57,988 - INFO - Listing Solr cores from http://localhost:8983/solr/admin/cores with timeout 30s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["No cores found or could not retrieve core list.\n", "Trying with local Solr...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:44:02,071 - ERROR - Connection error to http://localhost:8983/solr/: HTTPConnectionPool(host='localhost', port=8983): Max retries exceeded with url: /solr/admin/cores?action=STATUS&wt=json (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002579E8B0710>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\n", "2025-05-13 11:44:02,074 - INFO - Testing connectivity to core core_cvsearch_algerie at http://************:8983/solr/core_cvsearch_algerie/select with timeout 30s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["No cores found in local Solr either.\n", "\n", "Testing connectivity to relevant cores...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:44:23,105 - ERROR - Connection to core core_cvsearch_algerie timed out after 30 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  - core_cvsearch_algerie: Error (Documents: Unknown, Query Time: Unknownms)\n"]}], "source": ["# Run basic connectivity tests\n", "print(\"Testing Solr connectivity...\\n\")\n", "\n", "# Try different timeout values\n", "for timeout in [5, 15, 30]:\n", "    print(f\"\\nTrying with timeout = {timeout} seconds...\")\n", "    \n", "    # Get Solr information\n", "    solr_info = get_solr_info(timeout=timeout)\n", "    if solr_info:\n", "        print(f\"Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}\")\n", "        print(f\"Solr Implementation: {solr_info.get('lucene', {}).get('solr-impl-version', 'Unknown')}\")\n", "        print(f\"JVM: {solr_info.get('jvm', {}).get('name', 'Unknown')} {solr_info.get('jvm', {}).get('version', 'Unknown')}\")\n", "        print(f\"System Memory: {solr_info.get('system', {}).get('totalPhysicalMemorySize', 'Unknown')} bytes\")\n", "        break  # If successful, no need to try longer timeouts\n", "    else:\n", "        print(f\"Could not retrieve Solr information with timeout = {timeout} seconds.\")\n", "\n", "# If all timeouts failed, try with local Solr\n", "if not solr_info:\n", "    print(\"\\nTrying with local Solr...\")\n", "    solr_info = get_solr_info(base_url=LOCAL_SOLR_URL)\n", "    if solr_info:\n", "        print(f\"Local Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}\")\n", "    else:\n", "        print(\"Could not connect to local Solr either. Please check your Solr installation.\")\n", "\n", "# List cores\n", "print(\"\\nListing Solr cores...\")\n", "cores = list_solr_cores()\n", "if cores:\n", "    print(f\"Found {len(cores)} cores:\")\n", "    for core in cores:\n", "        print(f\"  - {core}\")\n", "else:\n", "    print(\"No cores found or could not retrieve core list.\")\n", "    \n", "    # Try with local Solr\n", "    print(\"Trying with local Solr...\")\n", "    cores = list_solr_cores(base_url=LOCAL_SOLR_URL)\n", "    if cores:\n", "        print(f\"Found {len(cores)} cores in local Solr:\")\n", "        for core in cores:\n", "            print(f\"  - {core}\")\n", "    else:\n", "        print(\"No cores found in local Solr either.\")\n", "\n", "# Test connectivity to relevant cores\n", "print(\"\\nTesting connectivity to relevant cores...\")\n", "core_results = []\n", "for country in TEST_COUNTRIES:\n", "    core_name = f\"core_cvsearch_{country}\"\n", "    result = test_core_connectivity(core_name)\n", "    core_results.append(result)\n", "    if result:\n", "        print(f\"  - {core_name}: {result['status']} (Documents: {result.get('num_found', 'Unknown')}, Query Time: {result.get('query_time_ms', 'Unknown')}ms)\")\n", "    else:\n", "        print(f\"  - {core_name}: Connection failed\")\n", "        \n", "        # Try with local Solr\n", "        print(f\"    Trying {core_name} with local Solr...\")\n", "        result = test_core_connectivity(core_name, base_url=LOCAL_SOLR_URL)\n", "        if result:\n", "            print(f\"    - Local {core_name}: {result['status']} (Documents: {result.get('num_found', 'Unknown')}, Query Time: {result.get('query_time_ms', 'Unknown')}ms)\")\n", "        else:\n", "            print(f\"    - Local {core_name}: Connection failed\")\n", "            \n", "            # Try with a generic core name\n", "            generic_core = \"core_outman_cvsearch\"\n", "            print(f\"    Trying generic core {generic_core}...\")\n", "            result = test_core_connectivity(generic_core, base_url=LOCAL_SOLR_URL)\n", "            if result:\n", "                print(f\"    - {generic_core}: {result['status']} (Documents: {result.get('num_found', 'Unknown')}, Query Time: {result.get('query_time_ms', 'Unknown')}ms)\")\n", "            else:\n", "                print(f\"    - {generic_core}: Connection failed\")"]}, {"cell_type": "markdown", "id": "connectivity-summary", "metadata": {}, "source": ["## Connectivity Summary\n", "\n", "Based on the connectivity tests above, we can determine:\n", "\n", "1. Whether the Solr server is accessible\n", "2. Which cores are available\n", "3. Whether the cores we need for job offer translation are accessible\n", "4. The appropriate timeout values to use\n", "\n", "This information will help us design a robust query construction approach for the job offer translation pipeline."]}, {"cell_type": "markdown", "id": "query-construction-section", "metadata": {}, "source": ["## 2. Query Construction and URL Length Testing\n", "\n", "Now let's test query construction, particularly focusing on URL length limitations when querying with multiple entity IDs."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}