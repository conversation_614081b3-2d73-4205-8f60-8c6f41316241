"""
Menu functions for the job offer translation pipeline.
"""

import os
from datetime import datetime

def show_menu(COUNTRIES, CONFIG, run_pipeline, check_pending_batch_jobs,
              test_solr_connection, display_config, edit_config,
              generate_pipeline_report, analyze_pipeline_performance):
    """
    Display the main menu and handle user input.
    """
    print("\n" + "=" * 50)
    print("🌐 JOB OFFER TRANSLATION PIPELINE")
    print("=" * 50)
    print("\nPlease select an option:")
    print("1. Run Translation Pipeline")
    print("2. Check Pending Batch Jobs")
    print("3. Test Solr Connection")
    print("4. Test Solr Update")
    print("5. View Configuration")
    print("6. Edit Configuration")
    print("7. Generate Pipeline Reports")
    print("8. Analyze Pipeline Performance")
    print("9. Exit")

    choice = input("\nEnter your choice (1-9): ")

    if choice == "1":
        run_pipeline()
    elif choice == "2":
        check_pending_batch_jobs()
    elif choice == "3":
        test_solr_connection()
    elif choice == "4":
        # Test Solr Update
        try:
            from solr_update_functions import test_solr_update, verify_solr_update

            print("\n🔍 SOLR UPDATE TEST")
            print("\nSelect a country to test:")
            for i, country in enumerate(COUNTRIES):
                print(f"{i+1}. {country}")

            country_idx = int(input("\nSelect a country (number): ")) - 1
            if 0 <= country_idx < len(COUNTRIES):
                country = COUNTRIES[country_idx]

                # Ask for number of test documents
                num_docs = int(input("\nNumber of test documents to create (1-10): ") or "1")
                num_docs = max(1, min(10, num_docs))

                # Ask if this is a dry run
                dry_run_input = input("\nDry run (no actual updates)? (Y/n): ").lower()
                dry_run = dry_run_input != "n"

                # Run the test
                test_solr_update(country, num_docs, dry_run)

                # If not a dry run, offer to verify the update
                if not dry_run:
                    verify = input("\nVerify the update? (y/N): ").lower()
                    if verify == "y":
                        offer_id = input("\nEnter the offer ID to verify: ")
                        doc = verify_solr_update(country, offer_id)

                        if doc:
                            print("\n✅ Document found in Solr:")
                            for field in ["id", "entity_id", "tr_field_offre_description_poste", "tr_field_offre_profil"]:
                                if field in doc:
                                    print(f"  - {field}: {doc[field]}")
                        else:
                            print("\n❌ Document not found in Solr")
            else:
                print("Invalid selection")
        except ImportError:
            print("\n❌ Solr update functions not found. Make sure solr_update_functions.py is in the same directory.")
        except Exception as e:
            print(f"\n❌ Error testing Solr update: {e}")
    elif choice == "5":
        display_config()
    elif choice == "6":
        edit_config()
    elif choice == "7":
        # Generate pipeline reports
        print("\n📊 Pipeline Report Generator")
        print("\nSelect report type:")
        print("1. Report for specific country")
        print("2. Report for specific run ID")
        print("3. Report for all countries")
        print("4. Return to main menu")

        report_choice = input("\nEnter your choice (1-4): ")

        if report_choice == "1":
            # Get country
            print("\nAvailable countries:")
            for i, country in enumerate(COUNTRIES):
                print(f"{i+1}. {country}")
            country_idx = int(input("\nSelect a country (number): ")) - 1
            if 0 <= country_idx < len(COUNTRIES):
                country = COUNTRIES[country_idx]
                generate_pipeline_report(country=country)
            else:
                print("Invalid selection")
        elif report_choice == "2":
            # Get run ID
            run_id = input("\nEnter run ID (YYYYMMDD_HHMMSS): ")
            generate_pipeline_report(run_id=run_id)
        elif report_choice == "3":
            # Report for all countries
            generate_pipeline_report()
    elif choice == "8":
        # Analyze pipeline performance
        print("\n📈 Pipeline Performance Analysis")
        print("\nSelect analysis type:")
        print("1. Analysis for specific country")
        print("2. Analysis for all countries")
        print("3. Return to main menu")

        analysis_choice = input("\nEnter your choice (1-3): ")

        if analysis_choice == "1":
            # Get country
            print("\nAvailable countries:")
            for i, country in enumerate(COUNTRIES):
                print(f"{i+1}. {country}")
            country_idx = int(input("\nSelect a country (number): ")) - 1
            if 0 <= country_idx < len(COUNTRIES):
                country = COUNTRIES[country_idx]
                # Get number of runs to analyze
                last_n_runs = int(input("\nNumber of most recent runs to analyze (default: 5): ") or 5)
                analyze_pipeline_performance(country=country, last_n_runs=last_n_runs)
            else:
                print("Invalid selection")
        elif analysis_choice == "2":
            # Analysis for all countries
            last_n_runs = int(input("\nNumber of most recent runs to analyze (default: 5): ") or 5)
            analyze_pipeline_performance(last_n_runs=last_n_runs)
    elif choice == "9":
        print("\nExiting...")
        return False
    else:
        print("\nInvalid choice. Please try again.")

    return True

def run_interactive_menu(COUNTRIES, CONFIG, run_pipeline, check_pending_batch_jobs,
                         test_solr_connection, display_config, edit_config,
                         generate_pipeline_report, analyze_pipeline_performance):
    """
    Run the interactive menu in a loop until the user chooses to exit.
    """
    while show_menu(COUNTRIES, CONFIG, run_pipeline, check_pending_batch_jobs,
                   test_solr_connection, display_config, edit_config,
                   generate_pipeline_report, analyze_pipeline_performance):
        pass
