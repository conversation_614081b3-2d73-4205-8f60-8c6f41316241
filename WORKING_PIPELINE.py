"""
Job Offer Translation Pipeline - Working Version

This script implements a working version of the job offer translation pipeline
using the development pattern discovered in our lab experimentation.

Key features:
1. Uses the "dev" mode to work with a local Solr instance
2. Implements OpenAI API for translation
3. Focuses on the two fields that need translation
4. Includes robust error handling
"""

import json
import logging
import os
import pysolr
import requests
import time
import pandas as pd
from datetime import datetime
from openai import OpenAI
from tqdm import tqdm

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('translation_pipeline')

# Solr Configuration
SOLR_BASE_URL = "http://************:8983/solr/"
LOCAL_SOLR_URL = "http://localhost:8983/solr/"

# OpenAI API Configuration
# Replace with your actual API key
OPENAI_API_KEY = "your-api-key-here"
client = OpenAI(api_key=OPENAI_API_KEY)

# Fields to translate
SOURCE_FIELDS = {
    "sm_field_offre_description_poste": "tr_field_offre_description_poste",
    "sm_field_offre_profil": "tr_field_offre_profil"
}

# Batch size for OpenAI API calls
BATCH_SIZE = 20

# Default timeout for requests (in seconds)
DEFAULT_TIMEOUT = 30

# Directory for storing results
RESULTS_DIR = "translation_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

def get_solr_connection(country, timeout=DEFAULT_TIMEOUT):
    """
    Get a connection to the Solr core for the specified country.
    Uses the pattern from the boss's code.
    
    Args:
        country (str): Country code or 'dev' for local Solr
        timeout (int): Timeout in seconds for the request
        
    Returns:
        pysolr.Solr: Solr connection
    """
    try:
        # Use the pattern from the boss's code
        if country != "dev":
            solr_url = f"{SOLR_BASE_URL}core_cvsearch_{country}"
            core_name = f"core_cvsearch_{country}"
        else:
            solr_url = f"{LOCAL_SOLR_URL}core_outman_cvsearch"
            core_name = "core_outman_cvsearch"
        
        logger.info(f"Connecting to Solr core {core_name} at {solr_url}")
        
        # Create PySOLR connection with timeout
        solr = pysolr.Solr(solr_url, timeout=timeout)
        
        return solr
    except Exception as e:
        logger.error(f"Error connecting to Solr for {country}: {e}")
        return None

def get_job_offers(country, num_offers=100, timeout=DEFAULT_TIMEOUT):
    """
    Get job offers from Solr.
    
    Args:
        country (str): Country code or 'dev' for local Solr
        num_offers (int): Number of job offers to retrieve
        timeout (int): Timeout in seconds for the request
        
    Returns:
        list: List of job offer documents
    """
    try:
        solr = get_solr_connection(country, timeout)
        if not solr:
            logger.error(f"Failed to get Solr connection for {country}")
            return []
        
        # Fields to retrieve
        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())
        
        # Execute the query
        logger.info(f"Retrieving {num_offers} job offers from {country}")
        results = solr.search('*:*', **{'fl': ','.join(fields), 'rows': num_offers})
        
        # Convert to list of dictionaries
        job_offers = [dict(doc) for doc in results]
        
        logger.info(f"Retrieved {len(job_offers)} job offers from {country}")
        return job_offers
    except Exception as e:
        logger.error(f"Error retrieving job offers from {country}: {e}")
        return []

def translate_batch_with_openai(texts, model="gpt-3.5-turbo"):
    """
    Translate a batch of texts from French to English using OpenAI API.
    
    Args:
        texts (list): List of texts to translate
        model (str): OpenAI model to use
        
    Returns:
        list: List of translated texts
    """
    try:
        # Skip empty batch
        if not texts or all(not text for text in texts):
            return []
        
        # Prepare messages for batch processing
        messages = [
            {"role": "system", "content": "You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Only return the translated text without any additional comments or explanations."}
        ]
        
        # Add all texts to translate in one request
        batch_text = "\n---ITEM---\n".join(texts)
        messages.append({"role": "user", "content": f"Translate the following French job offer texts to English. Each text is separated by '---ITEM---'. Return the translations in the same order, also separated by '---ITEM---':\n\n{batch_text}"})
        
        # Make the API call
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.3,  # Lower temperature for more consistent translations
        )
        
        # Extract and split the response
        translated_text = response.choices[0].message.content
        translated_items = translated_text.split("---ITEM---")
        
        # Clean up the results
        cleaned_translations = []
        for item in translated_items:
            item = item.strip()
            if item.startswith("\n"):
                item = item[1:]
            if item:
                cleaned_translations.append(item)
        
        # Ensure we have the same number of translations as input texts
        if len(cleaned_translations) != len(texts):
            logger.warning(f"Number of translations ({len(cleaned_translations)}) doesn't match number of input texts ({len(texts)})")
            # If there's a mismatch, we'll try to align them as best we can
            if len(cleaned_translations) < len(texts):
                # Pad with empty strings if we have fewer translations than inputs
                cleaned_translations.extend(["" for _ in range(len(texts) - len(cleaned_translations))])
            else:
                # Truncate if we have more translations than inputs
                cleaned_translations = cleaned_translations[:len(texts)]
        
        return cleaned_translations
    except Exception as e:
        logger.error(f"Error in translate_batch_with_openai: {e}")
        # Return empty strings for each input text in case of error
        return ["" for _ in range(len(texts))]

def translate_job_offers(job_offers):
    """
    Translate the specified fields in job offers.
    
    Args:
        job_offers (list): List of job offer dictionaries
        
    Returns:
        list: List of job offers with translated fields
    """
    try:
        if not job_offers:
            logger.warning("No job offers to translate")
            return []
        
        translated_offers = []
        
        # Process each source field separately
        for source_field, target_field in SOURCE_FIELDS.items():
            logger.info(f"Translating field {source_field} to {target_field}")
            
            # Collect texts to translate
            texts_to_translate = []
            offer_indices = []
            
            for i, offer in enumerate(job_offers):
                if source_field in offer and offer[source_field]:
                    # Some fields might be lists, take the first item if so
                    text = offer[source_field]
                    if isinstance(text, list):
                        text = text[0]
                    texts_to_translate.append(text)
                    offer_indices.append(i)
            
            logger.info(f"Found {len(texts_to_translate)} texts to translate for field {source_field}")
            
            # Process in batches to avoid API limits
            all_translations = []
            for i in range(0, len(texts_to_translate), BATCH_SIZE):
                batch = texts_to_translate[i:i+BATCH_SIZE]
                logger.info(f"Translating batch {i//BATCH_SIZE + 1}/{(len(texts_to_translate) + BATCH_SIZE - 1)//BATCH_SIZE}")
                translations = translate_batch_with_openai(batch)
                all_translations.extend(translations)
                # Add a small delay to avoid rate limits
                time.sleep(1)
            
            # Add translations to the offers
            for idx, translation in zip(offer_indices, all_translations):
                offer = job_offers[idx]
                
                # Initialize the offer in our results if it's not there yet
                offer_in_results = next((o for o in translated_offers if o['id'] == offer['id']), None)
                if not offer_in_results:
                    # Create a copy of the original offer
                    offer_in_results = offer.copy()
                    translated_offers.append(offer_in_results)
                
                # Add the translation
                offer_in_results[target_field] = translation
        
        logger.info(f"Translated {len(translated_offers)} job offers")
        return translated_offers
    except Exception as e:
        logger.error(f"Error in translate_job_offers: {e}")
        return []

def save_translations_to_json(translated_offers, country):
    """
    Save translated job offers to a JSON file.
    
    Args:
        translated_offers (list): List of job offers with translated fields
        country (str): Country code or 'dev' for local Solr
        
    Returns:
        str: Path to the saved file
    """
    try:
        if not translated_offers:
            logger.warning("No translated offers to save")
            return None
        
        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"{country}_translated_offers_{timestamp}.json"
        json_path = os.path.join(RESULTS_DIR, json_filename)
        
        # Save to JSON
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(translated_offers, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved {len(translated_offers)} translated job offers to {json_path}")
        return json_path
    except Exception as e:
        logger.error(f"Error saving translations to JSON for {country}: {e}")
        return None

def update_solr_with_translations(translated_offers, country):
    """
    Update Solr with translated job offer fields.
    
    Args:
        translated_offers (list): List of job offers with translated fields
        country (str): Country code or 'dev' for local Solr
        
    Returns:
        bool or list: True if successful, list of failed offer IDs otherwise
    """
    try:
        solr = get_solr_connection(country)
        if not solr:
            logger.error(f"Failed to get Solr connection for {country}")
            return False
        
        logger.info(f"Updating Solr with {len(translated_offers)} translated job offers")
        
        offers_with_error = []
        for offer in translated_offers:
            offer_id = offer['id']
            try:
                # Update each translated field
                for target_field in SOURCE_FIELDS.values():
                    if target_field in offer:
                        solr.add([{'id': offer_id, target_field: offer[target_field]}], fieldUpdates={'set': [target_field]})
                
                # Commit after each offer to ensure changes are saved
                solr.commit()
                logger.info(f"Updated Solr with translations for offer ID {offer_id}")
            except Exception as inner_e:
                offers_with_error.append(offer_id)
                logger.error(f"Error updating Solr for offer ID {offer_id}: {inner_e}")
        
        # Return result
        if not offers_with_error:
            logger.info("Successfully updated all offers in Solr")
            return True
        else:
            logger.warning(f"Failed to update {len(offers_with_error)} offers in Solr")
            return offers_with_error
    except Exception as e:
        logger.error(f"Error in update_solr_with_translations: {e}")
        return False

def main():
    """
    Main function to run the job offer translation pipeline.
    """
    print("JOB OFFER TRANSLATION PIPELINE")
    print("==============================\n")
    
    # Use development mode for now
    country = "dev"
    print(f"Running in development mode with local Solr instance\n")
    
    # Step 1: Get job offers from Solr
    print("Step 1: Retrieving job offers from Solr...")
    job_offers = get_job_offers(country, num_offers=10)
    
    if not job_offers:
        print("No job offers found. Make sure your local Solr instance is running and has data.")
        return
    
    print(f"Retrieved {len(job_offers)} job offers\n")
    
    # Step 2: Translate job offers
    print("Step 2: Translating job offers...")
    translated_offers = translate_job_offers(job_offers)
    
    if not translated_offers:
        print("Failed to translate job offers.")
        return
    
    print(f"Translated {len(translated_offers)} job offers\n")
    
    # Step 3: Save translations to JSON
    print("Step 3: Saving translations to JSON...")
    json_path = save_translations_to_json(translated_offers, country)
    
    if json_path:
        print(f"Saved translations to {json_path}\n")
    else:
        print("Failed to save translations to JSON.\n")
    
    # Step 4: Update Solr with translations (optional for now)
    print("Step 4: Updating Solr with translations...")
    print("This step is optional and requires a running local Solr instance.")
    update_solr = input("Do you want to update Solr with the translations? (y/n): ")
    
    if update_solr.lower() == 'y':
        result = update_solr_with_translations(translated_offers, country)
        if result is True:
            print("Successfully updated Solr with translations.")
        elif isinstance(result, list):
            print(f"Partially updated Solr. Failed for {len(result)} offers.")
        else:
            print("Failed to update Solr with translations.")
    else:
        print("Skipping Solr update.")
    
    print("\nPipeline completed!")

if __name__ == "__main__":
    main()
