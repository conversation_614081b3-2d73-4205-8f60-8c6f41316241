"""
PySOLR Connectivity Test

This script tests connectivity to Solr using the PySOLR library,
based on the code snippet provided by the boss.
"""

import pysolr
import json
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('pysolr_test')

# Solr Configuration
SOLR_BASE_URL = "http://************:8983/solr/"
LOCAL_SOLR_URL = "http://localhost:8983/solr/"

# Default timeout for requests (in seconds)
DEFAULT_TIMEOUT = 30

# Fields we're interested in for job offers
SOURCE_FIELDS = {
    "sm_field_offre_description_poste": "tr_field_offre_description_poste",
    "sm_field_offre_profil": "tr_field_offre_profil"
}

# List of countries to test
TEST_COUNTRIES = ['algerie', 'centrafrique', 'dev']

def test_pysolr_connection(country, timeout=DEFAULT_TIMEOUT):
    """
    Test connectivity to Solr using PySOLR.
    
    Args:
        country (str): Country code or 'dev' for local Solr
        timeout (int): Timeout in seconds for the request
        
    Returns:
        dict: Information about the connection test
    """
    try:
        start_time = time.time()
        
        # Use the pattern from the boss's code
        if country != "dev":
            solr_url = f"http://************:8983/solr/core_cvsearch_{country}"
            core_name = f"core_cvsearch_{country}"
        else:
            solr_url = f"http://localhost:8983/solr/core_outman_cvsearch"
            core_name = "core_outman_cvsearch"
        
        logger.info(f"Testing PySOLR connection to {core_name} at {solr_url}")
        
        # Create PySOLR connection with timeout
        solr = pysolr.Solr(solr_url, timeout=timeout)
        
        # Try a simple query
        results = solr.search('*:*', rows=10)
        
        end_time = time.time()
        query_time = (end_time - start_time) * 1000  # Convert to ms
        
        # Get number of documents
        num_found = len(results)
        
        return {
            'country': country,
            'core_name': core_name,
            'status': 'OK',
            'num_found': num_found,
            'query_time_ms': query_time,
            'solr_url': solr_url
        }
    except Exception as e:
        logger.error(f"Error connecting to {country} with PySOLR: {e}")
        return {
            'country': country,
            'core_name': core_name if 'core_name' in locals() else f"Unknown for {country}",
            'status': 'Error',
            'error': str(e),
            'solr_url': solr_url if 'solr_url' in locals() else "Unknown"
        }

def test_pysolr_query(country, query='*:*', fields=None, rows=10, timeout=DEFAULT_TIMEOUT):
    """
    Test querying Solr using PySOLR.
    
    Args:
        country (str): Country code or 'dev' for local Solr
        query (str): Solr query string
        fields (list): List of fields to retrieve
        rows (int): Number of rows to retrieve
        timeout (int): Timeout in seconds for the request
        
    Returns:
        dict: Information about the query test
    """
    try:
        # Use the pattern from the boss's code
        if country != "dev":
            solr_url = f"http://************:8983/solr/core_cvsearch_{country}"
            core_name = f"core_cvsearch_{country}"
        else:
            solr_url = f"http://localhost:8983/solr/core_outman_cvsearch"
            core_name = "core_outman_cvsearch"
        
        logger.info(f"Testing PySOLR query on {core_name} at {solr_url}")
        
        # Create PySOLR connection with timeout
        solr = pysolr.Solr(solr_url, timeout=timeout)
        
        # Prepare fields parameter
        if fields:
            fl = ','.join(fields)
        else:
            fl = '*'
        
        # Execute the query
        start_time = time.time()
        results = solr.search(query, **{'fl': fl, 'rows': rows})
        end_time = time.time()
        query_time = (end_time - start_time) * 1000  # Convert to ms
        
        # Process results
        docs = [doc for doc in results]
        
        return {
            'country': country,
            'core_name': core_name,
            'status': 'OK',
            'num_found': len(docs),
            'query_time_ms': query_time,
            'docs_sample': docs[:3] if docs else [],
            'solr_url': solr_url
        }
    except Exception as e:
        logger.error(f"Error querying {country} with PySOLR: {e}")
        return {
            'country': country,
            'core_name': core_name if 'core_name' in locals() else f"Unknown for {country}",
            'status': 'Error',
            'error': str(e),
            'solr_url': solr_url if 'solr_url' in locals() else "Unknown"
        }

def main():
    """
    Main function to run the PySOLR connectivity tests.
    """
    print("PYSOLR CONNECTIVITY TEST")
    print("=======================\n")
    
    print(f"Testing PySOLR connection to {len(TEST_COUNTRIES)} countries/environments...\n")
    
    # Test connection to each country/environment
    for country in TEST_COUNTRIES:
        print(f"Testing connection to {country}...")
        result = test_pysolr_connection(country)
        
        if result['status'] == 'OK':
            print(f"  - {result['core_name']}: SUCCESS")
            print(f"    Documents found: {result['num_found']}")
            print(f"    Query time: {result['query_time_ms']:.2f} ms")
        else:
            print(f"  - {result['core_name']}: FAILED - {result.get('error', 'Unknown error')}")
        
        # If connection successful, try a more specific query
        if result['status'] == 'OK':
            print(f"\n  Testing query on {country}...")
            query_result = test_pysolr_query(
                country, 
                fields=['id', 'entity_id'] + list(SOURCE_FIELDS.keys())
            )
            
            if query_result['status'] == 'OK':
                print(f"    Query SUCCESS - Found {query_result['num_found']} documents")
                if query_result['docs_sample']:
                    print(f"    Sample document fields: {list(query_result['docs_sample'][0].keys())}")
            else:
                print(f"    Query FAILED - {query_result.get('error', 'Unknown error')}")
        
        print("\n" + "-" * 50 + "\n")

if __name__ == "__main__":
    main()
