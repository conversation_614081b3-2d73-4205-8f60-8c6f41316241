{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PySOLR Deep Investigation - Why Updates Succeed But <PERSON> Don't Appear\n", "\n", "## Current Status\n", "- ✅ PySOLR connection works\n", "- ✅ PySOLR updates succeed (no errors)\n", "- ❌ Translation fields don't appear in document\n", "- ❌ Direct HTTP updates fail completely\n", "\n", "## Investigation Strategy\n", "1. **Check if fields are created with different names**\n", "2. **Test with existing tr_* fields that we know work**\n", "3. **Investigate dynamic field patterns**\n", "4. **Test field creation vs. field updates**\n", "5. **Check <PERSON><PERSON> logs for clues**\n", "\n", "## Hypothesis\n", "PySOLR is successfully sending updates, but:\n", "- Fields might be created with modified names\n", "- Updates might be silently ignored due to schema restrictions\n", "- Dynamic field patterns might be interfering\n", "- Field types might be incompatible"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pysolr\n", "import json\n", "import requests\n", "import logging\n", "import time\n", "from datetime import datetime\n", "import os\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('pysolr_deep_investigation')\n", "\n", "# Configuration\n", "SOLR_BASE_URL = \"http://51.195.54.52:8983/solr/\"\n", "COUNTRY = \"maroc\"\n", "CORE_NAME = f\"core_jobsearch_{COUNTRY}\"\n", "SOLR_CORE_URL = f\"{SOLR_BASE_URL}{CORE_NAME}\"\n", "SCHEMA_URL = f\"{SOLR_CORE_URL}/schema\"\n", "\n", "# Test document\n", "TEST_DOC_ID = \"c8xu1x/node/157221\"\n", "\n", "# Create results directory\n", "RESULTS_DIR = \"pysolr_deep_investigation_results\"\n", "os.makedirs(RESULTS_DIR, exist_ok=True)\n", "\n", "print(\"🔍 PySOLR Deep Investigation Initialized\")\n", "print(f\"Core: {CORE_NAME}\")\n", "print(f\"Test document: {TEST_DOC_ID}\")\n", "\n", "# Create connections\n", "solr = pysolr.Solr(SOLR_CORE_URL)\n", "print(f\"✅ PySOLR connection created\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Investigation 1: Check <PERSON> in Document"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def investigate_all_document_fields():\n", "    \"\"\"\n", "    Get ALL fields from the test document to see if translation fields\n", "    were created with different names or are hidden.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 INVESTIGATION 1: ALL DOCUMENT FIELDS\")\n", "    print(\"=\"*60)\n", "    \n", "    try:\n", "        # Query with all fields (*)\n", "        print(f\"🔍 Querying ALL fields for document: {TEST_DOC_ID}\")\n", "        results = solr.search(f'id:\"{TEST_DOC_ID}\"', fl='*')\n", "        \n", "        if results.hits > 0:\n", "            doc = results.docs[0]\n", "            all_fields = list(doc.keys())\n", "            \n", "            print(f\"📋 Total fields in document: {len(all_fields)}\")\n", "            print(f\"\\n📄 All field names:\")\n", "            \n", "            # Categorize fields\n", "            tr_fields = [f for f in all_fields if f.startswith('tr_')]\n", "            sm_fields = [f for f in all_fields if f.startswith('sm_')]\n", "            other_fields = [f for f in all_fields if not f.startswith(('tr_', 'sm_'))]\n", "            \n", "            print(f\"\\n🔄 Translation fields (tr_*): {len(tr_fields)}\")\n", "            for field in sorted(tr_fields):\n", "                print(f\"  📄 {field}\")\n", "            \n", "            print(f\"\\n📝 Source fields (sm_*): {len(sm_fields)}\")\n", "            for field in sorted(sm_fields):\n", "                print(f\"  📄 {field}\")\n", "            \n", "            print(f\"\\n📋 Other fields: {len(other_fields)}\")\n", "            for field in sorted(other_fields):\n", "                print(f\"  📄 {field}\")\n", "            \n", "            # Look for our target fields specifically\n", "            target_fields = ['tr_field_offre_description_poste', 'tr_field_offre_profil']\n", "            print(f\"\\n🎯 Target field check:\")\n", "            for target in target_fields:\n", "                if target in all_fields:\n", "                    print(f\"  ✅ {target}: FOUND\")\n", "                    print(f\"     Value: {doc[target]}\")\n", "                else:\n", "                    print(f\"  ❌ {target}: NOT FOUND\")\n", "            \n", "            # Look for similar field names\n", "            print(f\"\\n🔍 Looking for similar field names:\")\n", "            for target in target_fields:\n", "                similar = [f for f in all_fields if 'offre' in f.lower() or 'description' in f.lower() or 'profil' in f.lower()]\n", "                if similar:\n", "                    print(f\"  📄 Fields containing 'offre/description/profil': {similar}\")\n", "            \n", "            return {\n", "                'all_fields': all_fields,\n", "                'tr_fields': tr_fields,\n", "                'sm_fields': sm_fields,\n", "                'target_fields_found': {target: target in all_fields for target in target_fields},\n", "                'document': doc\n", "            }\n", "        else:\n", "            print(f\"❌ Document not found: {TEST_DOC_ID}\")\n", "            return {'error': 'Document not found'}\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error investigating fields: {e}\")\n", "        return {'error': str(e)}\n", "\n", "# Run investigation\n", "field_investigation = investigate_all_document_fields()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Investigation 2: Test with Existing tr_* Fields"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_existing_tr_fields():\n", "    \"\"\"\n", "    Test PySOLR updates with existing tr_* fields to see if the approach works\n", "    when fields definitely exist in the schema.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🧪 INVESTIGATION 2: TEST WITH EXISTING TR_* FIELDS\")\n", "    print(\"=\"*60)\n", "    \n", "    # Get existing tr_* fields from previous investigation\n", "    existing_tr_fields = field_investigation.get('tr_fields', [])\n", "    \n", "    if not existing_tr_fields:\n", "        print(\"❌ No existing tr_* fields found to test with\")\n", "        return {'error': 'No existing tr_* fields'}\n", "    \n", "    print(f\"📋 Found {len(existing_tr_fields)} existing tr_* fields\")\n", "    \n", "    # Pick the first available tr_* field for testing\n", "    test_field = existing_tr_fields[0]\n", "    print(f\"🎯 Testing with existing field: {test_field}\")\n", "    \n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    test_value = [f\"[EXISTING FIELD TEST] {timestamp}\"]\n", "    \n", "    try:\n", "        # Get current value\n", "        print(f\"\\n📋 Getting current value of {test_field}...\")\n", "        results = solr.search(f'id:\"{TEST_DOC_ID}\"', fl=f'id,{test_field}')\n", "        \n", "        if results.hits > 0:\n", "            doc = results.docs[0]\n", "            current_value = doc.get(test_field, 'NOT_FOUND')\n", "            print(f\"📄 Current value: {current_value}\")\n", "        \n", "        # Try PySOLR update on existing field\n", "        print(f\"\\n🔄 Updating {test_field} with PySOLR...\")\n", "        update_doc = {'id': TEST_DOC_ID, test_field: {'set': test_value}}\n", "        print(f\"📄 Update document: {json.dumps(update_doc, indent=2)}\")\n", "        \n", "        solr.add([update_doc])\n", "        solr.commit()\n", "        print(f\"✅ PySOLR update completed for existing field\")\n", "        \n", "        # Wait and verify\n", "        print(f\"⏳ Waiting for update to be processed...\")\n", "        time.sleep(3)\n", "        \n", "        # Check if update worked\n", "        print(f\"\\n🔍 Verifying update to existing field...\")\n", "        verify_results = solr.search(f'id:\"{TEST_DOC_ID}\"', fl=f'id,{test_field}')\n", "        \n", "        if verify_results.hits > 0:\n", "            verify_doc = verify_results.docs[0]\n", "            new_value = verify_doc.get(test_field, 'NOT_FOUND')\n", "            print(f\"📄 New value: {new_value}\")\n", "            \n", "            # Check if our test content is present\n", "            test_content_found = \"EXISTING FIELD TEST\" in str(new_value)\n", "            \n", "            if test_content_found:\n", "                print(f\"🎉 SUCCESS! PySOLR update worked on existing field!\")\n", "                print(f\"✅ This confirms PySOLR can update fields when they exist in schema\")\n", "                return {\n", "                    'success': True,\n", "                    'test_field': test_field,\n", "                    'old_value': current_value,\n", "                    'new_value': new_value,\n", "                    'test_content_found': True\n", "                }\n", "            else:\n", "                print(f\"❌ Update failed - test content not found\")\n", "                return {\n", "                    'success': <PERSON><PERSON><PERSON>,\n", "                    'test_field': test_field,\n", "                    'old_value': current_value,\n", "                    'new_value': new_value,\n", "                    'test_content_found': False\n", "                }\n", "        else:\n", "            print(f\"❌ Document not found during verification\")\n", "            return {'error': 'Document not found during verification'}\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error testing existing field: {e}\")\n", "        return {'error': str(e)}\n", "\n", "# Test with existing tr_* field\n", "existing_field_test = test_existing_tr_fields()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Investigation 3: Dynamic Field Patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def investigate_dynamic_fields():\n", "    \"\"\"\n", "    Check if there are dynamic field patterns that might affect our field names.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 INVESTIGATION 3: DYNAMIC FIELD PATTERNS\")\n", "    print(\"=\"*60)\n", "    \n", "    try:\n", "        # Get dynamic fields from schema\n", "        print(\"📋 Fetching dynamic field patterns...\")\n", "        response = requests.get(f\"{SCHEMA_URL}/dynamicfields\", params={'wt': 'json'}, timeout=30)\n", "        \n", "        if response.status_code == 200:\n", "            dynamic_data = response.json()\n", "            dynamic_fields = dynamic_data.get('dynamicFields', [])\n", "            \n", "            print(f\"✅ Found {len(dynamic_fields)} dynamic field patterns\")\n", "            \n", "            if dynamic_fields:\n", "                print(f\"\\n📄 Dynamic field patterns:\")\n", "                for df in dynamic_fields:\n", "                    pattern = df.get('name', 'unknown')\n", "                    field_type = df.get('type', 'unknown')\n", "                    print(f\"  🔄 {pattern} → type: {field_type}\")\n", "                \n", "                # Check if our field names match any patterns\n", "                target_fields = ['tr_field_offre_description_poste', 'tr_field_offre_profil']\n", "                print(f\"\\n🎯 Checking if target fields match dynamic patterns:\")\n", "                \n", "                for target in target_fields:\n", "                    matching_patterns = []\n", "                    for df in dynamic_fields:\n", "                        pattern = df.get('name', '')\n", "                        # Simple pattern matching (Solr uses * as wildcard)\n", "                        if pattern.startswith('*') and target.endswith(pattern[1:]):\n", "                            matching_patterns.append(pattern)\n", "                        elif pattern.endswith('*') and target.startswith(pattern[:-1]):\n", "                            matching_patterns.append(pattern)\n", "                        elif pattern == '*':\n", "                            matching_patterns.append(pattern)\n", "                    \n", "                    if matching_patterns:\n", "                        print(f\"  ✅ {target} matches patterns: {matching_patterns}\")\n", "                    else:\n", "                        print(f\"  ❌ {target} doesn't match any dynamic patterns\")\n", "                \n", "                return {\n", "                    'dynamic_fields': dynamic_fields,\n", "                    'patterns_found': len(dynamic_fields) > 0\n", "                }\n", "            else:\n", "                print(\"❌ No dynamic field patterns found\")\n", "                return {'dynamic_fields': [], 'patterns_found': False}\n", "        else:\n", "            print(f\"❌ Failed to get dynamic fields: {response.status_code}\")\n", "            return {'error': f'HTTP {response.status_code}'}\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error investigating dynamic fields: {e}\")\n", "        return {'error': str(e)}\n", "\n", "# Investigate dynamic fields\n", "dynamic_field_investigation = investigate_dynamic_fields()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Investigation 4: Immediate Workaround Solution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_workaround_solution():\n", "    \"\"\"\n", "    Create an immediate workaround using existing tr_* fields.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"🔧 INVESTIGATION 4: IMMEDIATE WORKAROUND SOLUTION\")\n", "    print(\"=\"*80)\n", "    \n", "    # Get existing tr_* fields\n", "    existing_tr_fields = field_investigation.get('tr_fields', [])\n", "    \n", "    if len(existing_tr_fields) < 2:\n", "        print(\"❌ Not enough existing tr_* fields for workaround\")\n", "        return {'error': 'Insufficient existing fields'}\n", "    \n", "    print(f\"📋 Available tr_* fields for workaround: {len(existing_tr_fields)}\")\n", "    \n", "    # Find suitable fields (preferably text type and multiValued)\n", "    suitable_fields = []\n", "    \n", "    try:\n", "        # Get field information from schema\n", "        response = requests.get(f\"{SCHEMA_URL}/fields\", params={'wt': 'json'}, timeout=30)\n", "        \n", "        if response.status_code == 200:\n", "            schema_data = response.json()\n", "            all_schema_fields = schema_data.get('fields', [])\n", "            \n", "            # Find details for existing tr_* fields\n", "            for field_name in existing_tr_fields:\n", "                field_info = next((f for f in all_schema_fields if f['name'] == field_name), None)\n", "                if field_info:\n", "                    field_type = field_info.get('type', 'unknown')\n", "                    is_multivalued = field_info.get('multiValued', False)\n", "                    \n", "                    print(f\"  📄 {field_name}: type={field_type}, multiValued={is_multivalued}\")\n", "                    \n", "                    # Prefer text fields that are multiValued\n", "                    if 'text' in field_type.lower() and is_multivalued:\n", "                        suitable_fields.append({\n", "                            'name': field_name,\n", "                            'type': field_type,\n", "                            'multiValued': is_multivalued,\n", "                            'priority': 1  # High priority\n", "                        })\n", "                    elif is_multivalued:\n", "                        suitable_fields.append({\n", "                            'name': field_name,\n", "                            'type': field_type,\n", "                            'multiValued': is_multivalued,\n", "                            'priority': 2  # Medium priority\n", "                        })\n", "                    else:\n", "                        suitable_fields.append({\n", "                            'name': field_name,\n", "                            'type': field_type,\n", "                            'multiValued': is_multivalued,\n", "                            'priority': 3  # Low priority\n", "                        })\n", "        \n", "        # Sort by priority and select best candidates\n", "        suitable_fields.sort(key=lambda x: x['priority'])\n", "        \n", "        if len(suitable_fields) >= 2:\n", "            # Select two best fields for our translation mapping\n", "            desc_field = suitable_fields[0]\n", "            profil_field = suitable_fields[1]\n", "            \n", "            print(f\"\\n🎯 WORKAROUND MAPPING:\")\n", "            print(f\"  tr_field_offre_description_poste → {desc_field['name']}\")\n", "            print(f\"  tr_field_offre_profil → {profil_field['name']}\")\n", "            \n", "            # Test the workaround\n", "            print(f\"\\n🧪 Testing workaround with PySOLR...\")\n", "            \n", "            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "            test_desc = f\"[WORKAROUND DESC TEST] {timestamp}\"\n", "            test_profil = f\"[WORKAROUND PROFIL TEST] {timestamp}\"\n", "            \n", "            # Test updates\n", "            workaround_results = []\n", "            \n", "            for field_info, test_value, purpose in [\n", "                (desc_field, [test_desc], 'description'),\n", "                (profil_field, [test_profil], 'profil')\n", "            ]:\n", "                try:\n", "                    field_name = field_info['name']\n", "                    print(f\"\\n🔄 Testing {purpose} → {field_name}\")\n", "                    \n", "                    # PySOLR update\n", "                    update_doc = {'id': TEST_DOC_ID, field_name: {'set': test_value}}\n", "                    solr.add([update_doc])\n", "                    solr.commit()\n", "                    \n", "                    # Wait and verify\n", "                    time.sleep(2)\n", "                    verify_results = solr.search(f'id:\"{TEST_DOC_ID}\"', fl=f'id,{field_name}')\n", "                    \n", "                    if verify_results.hits > 0:\n", "                        verify_doc = verify_results.docs[0]\n", "                        new_value = verify_doc.get(field_name, 'NOT_FOUND')\n", "                        \n", "                        test_found = \"WORKAROUND\" in str(new_value)\n", "                        \n", "                        if test_found:\n", "                            print(f\"  ✅ SUCCESS! {purpose} workaround works\")\n", "                            workaround_results.append({\n", "                                'purpose': purpose,\n", "                                'field': field_name,\n", "                                'success': True,\n", "                                'value': new_value\n", "                            })\n", "                        else:\n", "                            print(f\"  ❌ FAILED! {purpose} workaround didn't work\")\n", "                            workaround_results.append({\n", "                                'purpose': purpose,\n", "                                'field': field_name,\n", "                                'success': <PERSON><PERSON><PERSON>,\n", "                                'value': new_value\n", "                            })\n", "                    else:\n", "                        print(f\"  ❌ Document not found during verification\")\n", "                        workaround_results.append({\n", "                            'purpose': purpose,\n", "                            'field': field_name,\n", "                            'success': <PERSON><PERSON><PERSON>,\n", "                            'error': 'Document not found'\n", "                        })\n", "                        \n", "                except Exception as e:\n", "                    print(f\"  ❌ Error testing {purpose}: {e}\")\n", "                    workaround_results.append({\n", "                        'purpose': purpose,\n", "                        'field': field_info['name'],\n", "                        'success': <PERSON><PERSON><PERSON>,\n", "                        'error': str(e)\n", "                    })\n", "            \n", "            # Analyze workaround results\n", "            successful_workarounds = [r for r in workaround_results if r.get('success', False)]\n", "            \n", "            if len(successful_workarounds) >= 2:\n", "                print(f\"\\n🎉 WORKAROUND SUCCESS! Both fields work!\")\n", "                print(f\"\\n🚀 IMMEDIATE SOLUTION:\")\n", "                print(f\"Update your pipeline configuration:\")\n", "                print(f\"SOURCE_FIELDS = {{\")\n", "                print(f\"    'sm_field_offre_description_poste': '{desc_field['name']}',\")\n", "                print(f\"    'sm_field_offre_profil': '{profil_field['name']}'\")\n", "                print(f\"}}\")\n", "                \n", "                return {\n", "                    'workaround_success': True,\n", "                    'mapping': {\n", "                        'sm_field_offre_description_poste': desc_field['name'],\n", "                        'sm_field_offre_profil': profil_field['name']\n", "                    },\n", "                    'test_results': workaround_results\n", "                }\n", "            elif len(successful_workarounds) == 1:\n", "                print(f\"\\n⚠️ PARTIAL WORKAROUND: One field works\")\n", "                return {\n", "                    'workaround_success': 'partial',\n", "                    'test_results': workaround_results\n", "                }\n", "            else:\n", "                print(f\"\\n❌ WORKAROUND FAILED: No fields work\")\n", "                return {\n", "                    'workaround_success': <PERSON><PERSON><PERSON>,\n", "                    'test_results': workaround_results\n", "                }\n", "        else:\n", "            print(f\"❌ Not enough suitable fields found\")\n", "            return {'error': 'Insufficient suitable fields'}\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error creating workaround: {e}\")\n", "        return {'error': str(e)}\n", "\n", "# Create and test workaround\n", "workaround_solution = create_workaround_solution()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Final Analysis and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*80)\n", "print(\"📋 DEEP INVESTIGATION FINAL ANALYSIS\")\n", "print(\"=\"*80)\n", "\n", "# Compile all results\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "final_report = {\n", "    'timestamp': timestamp,\n", "    'field_investigation': field_investigation,\n", "    'existing_field_test': existing_field_test,\n", "    'dynamic_field_investigation': dynamic_field_investigation,\n", "    'workaround_solution': workaround_solution\n", "}\n", "\n", "# Determine the best path forward\n", "print(\"\\n🔍 ANALYSIS:\")\n", "print(\"-\" * 50)\n", "\n", "if existing_field_test.get('success', False):\n", "    print(\"✅ PySOLR works perfectly with existing fields\")\n", "    print(\"✅ The issue is specifically with creating new fields\")\nelse:\n", "    print(\"❌ PySOLR has issues even with existing fields\")\n", "    print(\"❌ This suggests deeper Solr configuration problems\")\n", "\n", "if workaround_solution.get('workaround_success') == True:\n", "    print(\"\\n🎉 IMMEDIATE SOLUTION AVAILABLE!\")\n", "    print(\"✅ Workaround using existing fields works perfectly\")\n", "    mapping = workaround_solution.get('mapping', {})\n", "    print(f\"\\n🚀 UPDATE YOUR PIPELINE NOW:\")\n", "    print(f\"Replace your SOURCE_FIELDS configuration with:\")\n", "    print(f\"SOURCE_FIELDS = {json.dumps(mapping, indent=4)}\")\n", "    print(f\"\\n✅ This will allow immediate deployment of your translation pipeline!\")\n", "    \n", "elif workaround_solution.get('workaround_success') == 'partial':\n", "    print(\"\\n⚠️ PARTIAL SOLUTION AVAILABLE\")\n", "    print(\"✅ Some existing fields work - can proceed with limited functionality\")\n", "    \nelse:\n", "    print(\"\\n❌ NO IMMEDIATE WORKAROUND AVAILABLE\")\n", "    print(\"🔧 Manual schema modification is required\")\n", "\n", "# Save comprehensive report\n", "report_filename = f\"pysolr_deep_investigation_report_{timestamp}.json\"\n", "report_filepath = os.path.join(RESULTS_DIR, report_filename)\n", "\n", "with open(report_filepath, 'w', encoding='utf-8') as f:\n", "    json.dump(final_report, f, ensure_ascii=False, indent=2)\n", "\n", "print(f\"\\n📁 Comprehensive investigation report saved to: {report_filepath}\")\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"🎯 DEEP INVESTIGATION COMPLETE\")\n", "print(\"=\"*80)\n", "print(\"\\nRecommendation based on investigation results above.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}