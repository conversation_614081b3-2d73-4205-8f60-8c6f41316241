{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Job Offer Translation - Morocco Mini Test\n", "\n", "This notebook is a focused test implementation for Morocco only, using the same logic and functionality as JOB_OFFER_TRANSLATION_NEW.ipynb. It's designed for thorough testing and validation before applying changes to all countries.\n", "\n", "## Purpose\n", "- Test the working Solr update logic with atomic updates\n", "- Validate the complete translation pipeline with a single country\n", "- Store document IDs for further investigation\n", "- Ensure all functionality works correctly in a controlled environment\n", "\n", "## Key Features\n", "- Uses the proven atomic update logic from SOLR_UPDATE_TESTING.ipynb\n", "- Focuses only on Morocco (maroc) for targeted testing\n", "- Maintains detailed logging and ID tracking\n", "- Includes all error handling and configuration options"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "try:\n", "    import json\n", "    import logging\n", "    import os\n", "    import time\n", "    import re\n", "    import html\n", "    import shutil\n", "    import glob\n", "    import statistics\n", "    from datetime import datetime\n", "    import uuid\n", "    print(\"✅ Basic Python libraries successfully imported\")\n", "except ImportError as e:\n", "    print(f\"❌ Error importing basic libraries: {e}\")\n", "\n", "# Try importing external libraries\n", "missing_libraries = []\n", "\n", "try:\n", "    import requests\n", "    print(\"✅ requests library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"requests\")\n", "    print(\"❌ requests library not found\")\n", "\n", "try:\n", "    import h5py\n", "    import numpy as np\n", "    print(\"✅ h5py library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"h5py numpy\")\n", "    print(\"❌ h5py library not found\")\n", "\n", "try:\n", "    import pysolr\n", "    print(\"✅ pysolr library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"pysolr\")\n", "    print(\"❌ pysolr library not found\")\n", "\n", "try:\n", "    from openai import OpenAI\n", "    print(\"✅ openai library successfully imported\")\n", "except ImportError:\n", "    missing_libraries.append(\"openai\")\n", "    print(\"❌ openai library not found\")\n", "\n", "# If any libraries are missing, print installation instructions\n", "if missing_libraries:\n", "    print(\"\\n⚠️ Some required libraries are missing. Please install them using pip:\")\n", "    for lib in missing_libraries:\n", "        print(f\"pip install {lib}\")\n", "    print(\"\\nAfter installing, restart the kernel and run this cell again.\")\n", "else:\n", "    print(\"\\n✅ All required libraries are installed!\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('morocco_translation_test')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Morocco Test Configuration\n", "import json\n", "import logging\n", "import os\n", "from datetime import datetime\n", "\n", "# Test-specific configuration\n", "TEST_COUNTRY = \"maroc\"  # Focus only on Morocco\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "CONFIG_DIR = \"morocco_test_config\"\n", "CONFIG_FILE = os.path.join(CONFIG_DIR, \"morocco_test_config.json\")\n", "BATCH_DIR = \"morocco_test_batches\"\n", "RESULTS_DIR = \"morocco_test_results\"\n", "TEST_IDS_DIR = \"morocco_test_ids\"  # Directory to store document IDs for investigation\n", "\n", "# Create necessary directories\n", "for directory in [CONFIG_DIR, BATCH_DIR, RESULTS_DIR, TEST_IDS_DIR]:\n", "    os.makedirs(directory, exist_ok=True)\n", "\n", "# Fields we're interested in for job offers\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "# Morocco-specific test configuration\n", "MOROCCO_TEST_CONFIG = {\n", "    \"general\": {\n", "        \"test_country\": TEST_COUNTRY,\n", "        \"test_batch_size\": 3,  # Small batch for testing\n", "        \"config_version\": \"1.0.0-morocco-test\",\n", "        \"last_updated\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    },\n", "    \"openai\": {\n", "        \"models\": {\n", "            \"default\": \"gpt-4.1-nano-2025-04-14\"\n", "        },\n", "        \"translation\": {\n", "            \"temperature\": 0.3,\n", "            \"system_prompt\": \"You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text.\",\n", "            \"user_prompt_template\": \"Translate the following French job offer text to English and return as JSON:\\n\\n{text}\"\n", "        },\n", "        \"batch_api\": {\n", "            \"max_wait_time\": 600,  # 10 minutes for testing\n", "            \"wait_interval\": 10,\n", "            \"retry_count\": 3,\n", "            \"retry_delay\": 10\n", "        }\n", "    },\n", "    \"solr\": {\n", "        \"connection\": {\n", "            \"timeout\": 30,\n", "            \"base_url\": SOLR_BASE_URL\n", "        },\n", "        \"query\": {\n", "            \"rows_per_query\": 100,\n", "            \"sort_field\": \"entity_id\",\n", "            \"sort_order\": \"asc\"\n", "        },\n", "        \"update\": {\n", "            \"commit_within\": 5000,\n", "            \"batch_size\": 3,  # Small batch for testing\n", "            \"final_commit\": True\n", "        }\n", "    },\n", "    \"error_handling\": {\n", "        \"max_consecutive_errors\": 3,\n", "        \"error_threshold_percentage\": 50,  # More lenient for testing\n", "        \"continue_on_error\": True\n", "    },\n", "    \"logging\": {\n", "        \"level\": \"DEBUG\",  # More verbose logging for testing\n", "        \"format\": \"%(asctime)s - %(levelname)s - %(message)s\"\n", "    },\n", "    \"testing\": {\n", "        \"save_document_ids\": True,  # Save IDs for investigation\n", "        \"dry_run_mode\": False,  # Set to True to test without actual updates\n", "        \"max_documents_to_process\": 5  # Limit for testing\n", "    }\n", "}\n", "\n", "# Save configuration\n", "def save_morocco_config(config):\n", "    try:\n", "        config['general']['last_updated'] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:\n", "            json.dump(config, f, ensure_ascii=False, indent=2)\n", "        print(f\"✅ Morocco test configuration saved to {CONFIG_FILE}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"❌ Error saving configuration: {e}\")\n", "        return False\n", "\n", "# Load or create configuration\n", "if os.path.exists(CONFIG_FILE):\n", "    with open(CONFIG_FILE, 'r', encoding='utf-8') as f:\n", "        CONFIG = json.load(f)\n", "    print(f\"✅ Configuration loaded from {CONFIG_FILE}\")\n", "else:\n", "    CONFIG = MOROCCO_TEST_CONFIG\n", "    save_morocco_config(CONFIG)\n", "    print(f\"✅ Default Morocco test configuration created\")\n", "\n", "# Configure logging based on configuration\n", "log_level = getattr(logging, CONFIG['logging']['level'])\n", "logging.basicConfig(level=log_level, format=CONFIG['logging']['format'])\n", "logger = logging.getLogger('morocco_translation_test')\n", "\n", "print(f\"\\n📋 Morocco Test Configuration:\")\n", "print(f\"Test Country: {CONFIG['general']['test_country']}\")\n", "print(f\"Test Batch Size: {CONFIG['general']['test_batch_size']}\")\n", "print(f\"OpenAI Model: {CONFIG['openai']['models']['default']}\")\n", "print(f\"Dry Run Mode: {CONFIG['testing']['dry_run_mode']}\")\n", "print(f\"Max Documents: {CONFIG['testing']['max_documents_to_process']}\")\n", "print(f\"Log Level: {CONFIG['logging']['level']}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}