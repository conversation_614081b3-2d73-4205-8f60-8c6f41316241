"""
Solr testing functions for the job offer translation pipeline.
"""

import time
import logging

logger = logging.getLogger(__name__)

def test_solr_connection(COUNTRIES=None, CONFIG=None):
    """
    Test connectivity to Solr for all countries.
    
    Args:
        COUNTRIES (list): List of country codes
        CONFIG (dict): Configuration dictionary
    """
    print("\n" + "=" * 50)
    print("🔍 SOLR CONNECTION TEST")
    print("=" * 50)
    
    # Import necessary functions from the main notebook
    from JOB_OFFER_TRANSLATION_NEW import get_solr_info, get_jobsearch_connection, query_solr_direct
    
    # Get Solr information
    print("\nTesting Solr server information...")
    solr_info = get_solr_info()
    if solr_info:
        print(f"✅ Solr server is accessible")
        print(f"  - Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}")
        print(f"  - Solr Implementation: {solr_info.get('lucene', {}).get('solr-impl-version', 'Unknown')}")
        print(f"  - JVM: {solr_info.get('jvm', {}).get('name', 'Unknown')} {solr_info.get('jvm', {}).get('version', 'Unknown')}")
    else:
        print("❌ Could not connect to Solr server. Check connectivity.")
    
    # Test connection to each country
    if not COUNTRIES:
        print("\nNo countries specified. Skipping country-specific tests.")
        return
    
    print("\nTesting connection to country-specific cores...")
    
    results = []
    for country in COUNTRIES:
        print(f"\nTesting connection to {country}...")
        
        # Test PySOLR connection
        try:
            start_time = time.time()
            solr = get_jobsearch_connection(country)
            
            if solr:
                # Try a simple query
                query_results = solr.search('*:*', rows=1)
                end_time = time.time()
                query_time = (end_time - start_time) * 1000  # Convert to ms
                
                # Get number of documents
                num_found = query_results.hits
                
                print(f"✅ PySOLR connection successful")
                print(f"  - Documents found: {num_found}")
                print(f"  - Query time: {query_time:.2f} ms")
                
                results.append({
                    'country': country,
                    'method': 'PySOLR',
                    'status': 'OK',
                    'num_found': num_found,
                    'query_time_ms': query_time
                })
            else:
                print(f"❌ Failed to create PySOLR connection")
                results.append({
                    'country': country,
                    'method': 'PySOLR',
                    'status': 'Error',
                    'error': 'Failed to create connection'
                })
        except Exception as e:
            print(f"❌ PySOLR connection error: {e}")
            results.append({
                'country': country,
                'method': 'PySOLR',
                'status': 'Error',
                'error': str(e)
            })
        
        # Test direct HTTP connection
        try:
            start_time = time.time()
            params = {
                'q': '*:*',
                'rows': 1,
                'wt': 'json'
            }
            
            direct_result = query_solr_direct(country, params)
            end_time = time.time()
            query_time = (end_time - start_time) * 1000  # Convert to ms
            
            if direct_result:
                num_found = direct_result.get('response', {}).get('numFound', 0)
                
                print(f"✅ Direct HTTP connection successful")
                print(f"  - Documents found: {num_found}")
                print(f"  - Query time: {query_time:.2f} ms")
                
                results.append({
                    'country': country,
                    'method': 'Direct HTTP',
                    'status': 'OK',
                    'num_found': num_found,
                    'query_time_ms': query_time
                })
            else:
                print(f"❌ Failed to query using direct HTTP")
                results.append({
                    'country': country,
                    'method': 'Direct HTTP',
                    'status': 'Error',
                    'error': 'Failed to get response'
                })
        except Exception as e:
            print(f"❌ Direct HTTP connection error: {e}")
            results.append({
                'country': country,
                'method': 'Direct HTTP',
                'status': 'Error',
                'error': str(e)
            })
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 CONNECTION TEST SUMMARY")
    print("=" * 50)
    
    success_count = sum(1 for r in results if r['status'] == 'OK')
    total_tests = len(results)
    
    print(f"\nSuccessful connections: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    # Group by country
    country_results = {}
    for result in results:
        country = result['country']
        if country not in country_results:
            country_results[country] = []
        country_results[country].append(result)
    
    # Print results by country
    for country, country_result in country_results.items():
        print(f"\n{country}:")
        for result in country_result:
            status = "✅" if result['status'] == 'OK' else "❌"
            method = result['method']
            if result['status'] == 'OK':
                print(f"  {status} {method}: {result['num_found']} docs in {result['query_time_ms']:.2f} ms")
            else:
                print(f"  {status} {method}: {result.get('error', 'Unknown error')}")
    
    return results
