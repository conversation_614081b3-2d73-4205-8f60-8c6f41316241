{"cells": [{"cell_type": "code", "execution_count": null, "id": "code_0", "metadata": {}, "outputs": [], "source": ["# Job Offer Translation - Fixed JSON Format Implementation", "\",\n    \"", "\",\n    \"This notebook implements the job offer translation using OpenAI's Batch API with a fixed implementation that handles the JSON format requirement. It uses the `gpt-4.1-nano-2025-04-14` model as requested.", "\",\n    \"", "\",\n    \"Key fixes in this implementation:", "\",\n    \"1. The messages now include the word \\\"json\\\" to satisfy the requirement for using `response_format` of type `json_object`", "\",\n    \"2. The `body` field in the batch request is properly formatted as an object (not a string)", "\",\n    \"3. The batch file is saved with UTF-8 encoding without BOM", "\",\n    \"4. The file uses Unix line separators (LF instead of CR LF)", "\",\n    \"", "\",\n    \"This notebook is designed to run on the server via X2GO where the Solr connection works."]}, {"cell_type": "code", "execution_count": null, "id": "code_1", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries", "\",\n    \"try:", "\",\n    \"    import json", "\",\n    \"    import logging", "\",\n    \"    import os", "\",\n    \"    import time", "\",\n    \"    import re", "\",\n    \"    import html", "\",\n    \"    from datetime import datetime", "\",\n    \"    import uuid", "\",\n    \"    print(\\\"✅ Basic Python libraries successfully imported\\\")", "\",\n    \"except ImportError as e:", "\",\n    \"    print(f\\\"❌ Error importing basic libraries: {e}\\\")", "\",\n    \"", "\",\n    \"# Try importing external libraries", "\",\n    \"missing_libraries = []", "\",\n    \"", "\",\n    \"try:", "\",\n    \"    import requests", "\",\n    \"    print(\\\"✅ requests library successfully imported\\\")", "\",\n    \"except ImportError:", "\",\n    \"    missing_libraries.append(\\\"requests\\\")", "\",\n    \"    print(\\\"❌ requests library not found\\\")", "\",\n    \"", "\",\n    \"try:", "\",\n    \"    import pysolr", "\",\n    \"    print(\\\"✅ pysolr library successfully imported\\\")", "\",\n    \"except ImportError:", "\",\n    \"    missing_libraries.append(\\\"pysolr\\\")", "\",\n    \"    print(\\\"❌ pysolr library not found\\\")", "\",\n    \"", "\",\n    \"try:", "\",\n    \"    from openai import OpenAI", "\",\n    \"    print(\\\"✅ openai library successfully imported\\\")", "\",\n    \"except ImportError:", "\",\n    \"    missing_libraries.append(\\\"openai\\\")", "\",\n    \"    print(\\\"❌ openai library not found\\\")", "\",\n    \"", "\",\n    \"# If any libraries are missing, print installation instructions", "\",\n    \"if missing_libraries:", "\",\n    \"    print(\\\"\\", "⚠️ Some required libraries are missing. Please install them using pip:\\\")", "\",\n    \"    for lib in missing_libraries:", "\",\n    \"        print(f\\\"pip install {lib}\\\")", "\",\n    \"    print(\\\"\\", "After installing, restart the kernel and run this cell again.\\\")", "\",\n    \"else:", "\",\n    \"    print(\\\"\\", "✅ All required libraries are installed!\\\")", "\",\n    \"", "\",\n    \"# Configure logging", "\",\n    \"logging.basicConfig(level=logging.INFO, ", "\",\n    \"                    format='%(asctime)s - %(levelname)s - %(message)s')", "\",\n    \"logger = logging.getLogger('job_offer_translation')"]}, {"cell_type": "code", "execution_count": null, "id": "code_2", "metadata": {}, "outputs": [], "source": ["# Configuration", "\",\n    \"SOLR_BASE_URL = \\\"http://************:8983/solr/\\\"", "\",\n    \"", "\",\n    \"# Fields we're interested in for job offers", "\",\n    \"SOURCE_FIELDS = {", "\",\n    \"    \\\"sm_field_offre_description_poste\\\": \\\"tr_field_offre_description_poste\\\",", "\",\n    \"    \\\"sm_field_offre_profil\\\": \\\"tr_field_offre_profil\\\"", "\",\n    \"}", "\",\n    \"", "\",\n    \"# Countries to work with", "\",\n    \"COUNTRIES = ['algerie', 'centrafrique', 'benin', 'burkina']", "\",\n    \"", "\",\n    \"# Number of documents to process in one batch", "\",\n    \"BATCH_SIZE = 2  # Start with a small batch for testing", "\",\n    \"", "\",\n    \"# OpenAI API configuration", "\",\n    \"OPENAI_MODEL = \\\"gpt-4.1-nano-2025-04-14\\\"  # Using the nano model as requested", "\",\n    \"", "\",\n    \"# Directory for storing batch files and results", "\",\n    \"BATCH_DIR = \\\"batch_files\\\"", "\",\n    \"RESULTS_DIR = \\\"translation_results\\\"", "\",\n    \"os.makedirs(BATCH_DIR, exist_ok=True)", "\",\n    \"os.makedirs(RESULTS_DIR, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "id": "code_3", "metadata": {}, "outputs": [], "source": ["## 1. Solr Connection Functions", "\",\n    \"", "\",\n    \"These functions handle connecting to the Solr JobSearch cores and retrieving job offers."]}, {"cell_type": "code", "execution_count": null, "id": "code_4", "metadata": {}, "outputs": [], "source": ["# Create a connection pool for reusing Solr connections", "\",\n    \"solr_connections = {}", "\",\n    \"", "\",\n    \"def get_jobsearch_connection(country, timeout=30):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Get a connection to the JobSearch Solr core for the specified country.", "\",\n    \"    Uses connection pooling to reuse existing connections.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        country (str): Country code", "\",\n    \"        timeout (int): Connection timeout in seconds", "\",\n    \"        ", "\",\n    \"    Returns:", "\",\n    \"        pysolr.Solr: Solr connection", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    try:", "\",\n    \"        # Check if we already have a connection for this country", "\",\n    \"        if country in solr_connections:", "\",\n    \"            logger.info(f\\\"Using existing connection for {country}\\\")", "\",\n    \"            return solr_connections[country]", "\",\n    \"        ", "\",\n    \"        # Use the correct core pattern: core_jobsearch_[country]", "\",\n    \"        # Ensure the URL format is exactly as expected", "\",\n    \"        solr_url = f\\\"{SOLR_BASE_URL}core_jobsearch_{country}\\\"", "\",\n    \"        logger.info(f\\\"Creating new connection to JobSearch Solr core at {solr_url}\\\")", "\",\n    \"        ", "\",\n    \"        # Log the exact URL format that will be used for queries", "\",\n    \"        example_query_url = f\\\"{solr_url}/select?q=*:*&fq=*:*&fl=id,entity_id&rows=10&start=0&sort=entity_id+asc&wt=json\\\"", "\",\n    \"        logger.debug(f\\\"Example query URL: {example_query_url}\\\")", "\",\n    \"        ", "\",\n    \"        # Create PySOLR connection with timeout", "\",\n    \"        solr = pysolr.Solr(solr_url, timeout=timeout)", "\",\n    \"        ", "\",\n    \"        # Store in connection pool", "\",\n    \"        solr_connections[country] = solr", "\",\n    \"        return solr", "\",\n    \"    except Exception as e:", "\",\n    \"        logger.error(f\\\"Error connecting to JobSearch Solr for {country}: {e}\\\")", "\",\n    \"        return None", "\",\n    \"", "\",\n    \"def query_solr_direct(country, params=None):\n    \"\"\"\n    Query Solr directly using HTTP requests instead of PySOLR.\n    This is useful for testing and debugging Solr queries.\n\n    Args:\n        country (str): Country code\n        params (dict): Query parameters\n\n    Returns:\n        dict: Solr response as JSON\n    \"\"\"\n    try:\n        # Set default parameters if none provided\n        if params is None:\n            params = {\n                'q': '*:*',\n                'fq': '*:*',\n                'fl': 'id,entity_id',\n                'rows': 10,\n                'start': 0,\n                'sort': 'entity_id asc',\n                'wt': 'json'\n            }\n\n        # Construct the URL\n        solr_url = f\"{SOLR_BASE_URL}core_jobsearch_{country}/select\"\n\n        # Make the request\n        logger.info(f\"Making direct HTTP request to Solr: {solr_url}\")\n        response = requests.get(solr_url, params=params, timeout=30)\n\n        # Check if the request was successful\n        if response.status_code == 200:\n            # Parse the JSON response\n            result = response.json()\n            logger.info(f\"Direct Solr query successful. Found {result.get('response', {}).get('numFound', 0)} documents.\")\n            return result\n        else:\n            logger.error(f\"Direct Solr query failed with status code {response.status_code}: {response.text}\")\n            return None\n    except Exception as e:\n        logger.error(f\"Error in direct Solr query: {e}\")\n        return None\n\ndef validate_job_offer(offer):\n    \"\"\"\n    Validate a job offer document to ensure it has all required fields and proper ID format.\n\n    Args:\n        offer (dict): Job offer document\n\n    Returns:\n        tuple: (is_valid, reason)\n    \"\"\"\n    # Validate ID format\n    if 'id' not in offer or not isinstance(offer['id'], str) or '/node/' not in offer['id']:\n        return False, f\"Invalid ID format: {offer.get('id', 'unknown')}\"\n\n    # Validate entity_id field\n    if 'entity_id' not in offer:\n        return False, f\"Missing entity_id field for offer {offer['id']}\"\n\n    # Cross-validate id and entity_id fields\n    try:\n        id_parts = offer['id'].split('/node/')\n        if len(id_parts) == 2 and id_parts[1].isdigit():\n            numeric_id = int(id_parts[1])\n            if numeric_id != offer['entity_id']:\n                return False, f\"Mismatched IDs: id={offer['id']}, entity_id={offer['entity_id']}\"\n        else:\n            return False, f\"Unparseable ID format: {offer['id']}\"\n    except Exception as e:\n        return False, f\"Error validating offer IDs: {e}\"\n\n    # Check if all source fields exist\n    has_source_fields = all(source_field in offer for source_field in SOURCE_FIELDS.keys())\n    if not has_source_fields:\n        return False, f\"Missing source fields for offer {offer['id']}\"\n\n    return True, \"Valid\"\n\ndef get_job_offers(country, num_offers=BATCH_SIZE, start=0, skip_translated=True):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Get job offers from the JobSearch Solr core with batch processing and verification.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        country (str): Country code", "\",\n    \"        num_offers (int): Number of job offers to retrieve", "\",\n    \"        start (int): Starting offset for pagination", "\",\n    \"        skip_translated (bool): Whether to skip documents that already have translated fields", "\",\n    \"        ", "\",\n    \"    Returns:", "\",\n    \"        list: List of job offer documents that need translation", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    try:", "\",\n    \"        solr = get_jobsearch_connection(country)", "\",\n    \"        if not solr:", "\",\n    \"            logger.error(f\\\"Failed to get JobSearch Solr connection for {country}\\\")", "\",\n    \"            return []", "\",\n    \"        ", "\",\n    \"        # Fields to retrieve - we no longer need to include translated fields", "\",\n    \"        # since we're filtering at the query level", "\",\n    \"        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())", "\",\n    \"        ", "\",\n    \"        # Prepare query parameters", "\",\n    \"        params = {", "\",\n    \"            'q': '*:*',     # Main query", "\",\n    \"            'fl': ','.join(fields),  # Fields to return", "\",\n    \"            'rows': num_offers,      # Number of rows to return", "\",\n    \"            'start': start,          # Starting offset", "\",\n    \"            'sort': 'entity_id asc', # Sort by entity_id ascending", "\",\n    \"            'wt': 'json'             # Response format", "\",\n    \"        }", "\",\n    \"        ", "\",\n    \"        # Add filter query to exclude documents with translations if requested", "\",\n    \"        if skip_translated:", "\",\n    \"            # Create a filter query that excludes documents with any translated fields", "\",\n    \"            translation_filters = []", "\",\n    \"            for target_field in SOURCE_FIELDS.values():", "\",\n    \"                translation_filters.append(f\\\"-{target_field}:[* TO *]\\\")", "\",\n    \"            ", "\",\n    \"            # Combine filters with AND", "\",\n    \"            params['fq'] = \\\" AND \\\".join(translation_filters)", "\",\n    \"            logger.info(f\\\"Using filter query to exclude documents with translations: {params['fq']}\\\")", "\",\n    \"        else:", "\",\n    \"            # If not skipping translated documents, use a simple filter query", "\",\n    \"            params['fq'] = '*:*'", "\",\n    \"        ", "\",\n    \"        # Construct the exact URL for logging/debugging", "\",\n    \"        solr_url = f\\\"{SOLR_BASE_URL}core_jobsearch_{country}/select\\\"", "\",\n    \"        query_params = '&'.join([f\\\"{k}={v}\\\" for k, v in params.items()])", "\",\n    \"        full_url = f\\\"{solr_url}?{query_params}\\\"", "\",\n    \"        logger.debug(f\\\"Full Solr query URL: {full_url}\\\")", "\",\n    \"        ", "\",\n    \"        # Execute the query", "\",\n    \"        logger.info(f\\\"Retrieving job offers from {country} (start={start}, rows={num_offers})\\\")", "\",\n    \"        # Note: PySOLR's search method takes q as a positional argument and the rest as kwargs", "\",\n    \"        q = params.pop('q')  # Remove q from params to pass it separately", "\",\n    \"        results = solr.search(q, **params)", "\",\n    \"        ", "\",\n    \"        # Convert to list of dictionaries", "\",\n    \"        all_offers = [dict(doc) for doc in results]", "\",\n    \"        logger.info(f\\\"Retrieved {len(all_offers)} job offers from {country} that need translation\\\")", "\",\n    \"        ", "\",\n    \"        # We still need to validate the documents, but we don't need to check for translations", "\",\n    \"        valid_offers = []", "\",\n    \"        for offer in all_offers:", "\",\n    \"            # Validate the job offer using our validation function", "\",\n    \"            is_valid, reason = validate_job_offer(offer)", "\",\n    \"            if not is_valid:", "\",\n    \"                logger.warning(f\\\"Skipping offer: {reason}\\\")", "\",\n    \"                continue", "\",\n    \"            ", "\",\n    \"            valid_offers.append(offer)", "\",\n    \"        ", "\",\n    \"        logger.info(f\\\"{len(valid_offers)} out of {len(all_offers)} job offers are valid and need translation\\\")", "\",\n    \"        return valid_offers", "\",\n    \"    except Exception as e:", "\",\n    \"        logger.error(f\\\"Error retrieving job offers from {country}: {e}\\\")", "\",\n    \"        return []"]}, {"cell_type": "code", "execution_count": null, "id": "code_5", "metadata": {}, "outputs": [], "source": ["## 2. Batch Processing Functions", "\",\n    \"", "\",\n    \"These functions handle preparing and processing batch files for the OpenAI Batch API."]}, {"cell_type": "code", "execution_count": null, "id": "code_6", "metadata": {}, "outputs": [], "source": ["def prepare_batch_file(job_offers, country):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Prepare a batch file for OpenAI Batch API with the correct format.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        job_offers (list): List of job offer documents", "\",\n    \"        country (str): Country code", "\",\n    \"        ", "\",\n    \"    Returns:", "\",\n    \"        str: Path to the batch file", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    try:", "\",\n    \"        if not job_offers:", "\",\n    \"            logger.warning(\\\"No job offers to process\\\")", "\",\n    \"            return None", "\",\n    \"        ", "\",\n    \"        # Create a unique batch file name", "\",\n    \"        timestamp = datetime.now().strftime(\\\"%Y%m%d_%H%M%S\\\")", "\",\n    \"        batch_file_path = os.path.join(BATCH_DIR, f\\\"{country}_batch_{timestamp}.jsonl\\\")", "\",\n    \"        ", "\",\n    \"        # Prepare batch requests", "\",\n    \"        batch_requests = []", "\",\n    \"        ", "\",\n    \"        for offer in job_offers:", "\",\n    \"            offer_id = offer.get('id', str(uuid.uuid4()))", "\",\n    \"            ", "\",\n    \"            # Process each source field", "\",\n    \"            for source_field, target_field in SOURCE_FIELDS.items():", "\",\n    \"                if source_field in offer:", "\",\n    \"                    value = offer[source_field]", "\",\n    \"                    ", "\",\n    \"                    # Handle list values", "\",\n    \"                    if isinstance(value, list):", "\",\n    \"                        if not value:", "\",\n    \"                            continue", "\",\n    \"                        text = value[0]  # Use the first item", "\",\n    \"                    elif isinstance(value, str):", "\",\n    \"                        text = value", "\",\n    \"                    else:", "\",\n    \"                        continue", "\",\n    \"                    ", "\",\n    \"                    # Skip empty text", "\",\n    \"                    if not text or not text.strip():", "\",\n    \"                        continue", "\",\n    \"                    ", "\",\n    \"                    # Decode HTML entities", "\",\n    \"                    decoded_text = html.unescape(text)", "\",\n    \"                    ", "\",\n    \"                    # Create a unique custom ID for this request", "\",\n    \"                    custom_id = f\\\"{offer_id}_{source_field}\\\"", "\",\n    \"                    ", "\",\n    \"                    # Create the batch request with body as an object (not a string)", "\",\n    \"                    # Include the word \\\"json\\\" in the messages to satisfy the requirement", "\",\n    \"                    batch_request = {", "\",\n    \"                        \\\"custom_id\\\": custom_id,", "\",\n    \"                        \\\"method\\\": \\\"POST\\\",", "\",\n    \"                        \\\"url\\\": \\\"/v1/chat/completions\\\",", "\",\n    \"                        \\\"body\\\": {", "\",\n    \"                            \\\"model\\\": OPENAI_MODEL,", "\",\n    \"                            \\\"messages\\\": [", "\",\n    \"                                {\\\"role\\\": \\\"system\\\", \\\"content\\\": \\\"You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return the translation as JSON with a 'translation' field containing the translated text.\\\"},", "\",\n    \"                                {\\\"role\\\": \\\"user\\\", \\\"content\\\": f\\\"Translate the following French job offer text to English and return as JSON:\\", "\\", "{decoded_text}\\\"}", "\",\n    "]}, {"cell_type": "code", "execution_count": null, "id": "code_7", "metadata": {}, "outputs": [], "source": ["# Test the direct HTTP query function", "\",\n    \"def test_direct_solr_query():", "\",\n    \"    print(\\\"Testing direct Solr query...\\", "\\\")", "\",\n    \"    ", "\",\n    \"    # Test with a specific country", "\",\n    \"    country = 'algerie'  # Change to the country you want to test", "\",\n    \"    ", "\",\n    \"    # Set up query parameters exactly matching the working URL format", "\",\n    \"    params = {", "\",\n    \"        'q': '*:*',", "\",\n    \"        'fq': '*:*',", "\",\n    \"        'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',", "\",\n    \"        'rows': 5,", "\",\n    \"        'start': 0,", "\",\n    \"        'sort': 'entity_id asc',", "\",\n    \"        'wt': 'json'", "\",\n    \"    }", "\",\n    \"    ", "\",\n    \"    # Construct the URL manually for logging", "\",\n    \"    solr_url = f\\\"{SOLR_BASE_URL}core_jobsearch_{country}/select\\\"", "\",\n    \"    query_params = '&'.join([f\\\"{k}={v}\\\" for k, v in params.items()])", "\",\n    \"    full_url = f\\\"{solr_url}?{query_params}\\\"", "\",\n    \"    print(f\\\"Full query URL: {full_url}\\", "\\\")", "\",\n    \"    ", "\",\n    \"    # Execute the query", "\",\n    \"    result = query_solr_direct(country, params)", "\",\n    \"    ", "\",\n    \"    if result:", "\",\n    \"        response = result.get('response', {})", "\",\n    \"        num_found = response.get('numFound', 0)", "\",\n    \"        docs = response.get('docs', [])", "\",\n    \"        ", "\",\n    \"        print(f\\\"Query successful! Found {num_found} documents.\\", "\\\")", "\",\n    \"        ", "\",\n    \"        if docs:", "\",\n    \"            print(f\\\"First {len(docs)} documents:\\\")", "\",\n    \"            for i, doc in enumerate(docs):", "\",\n    \"                print(f\\\"\\", "Document {i+1}:\\\")", "\",\n    \"                print(f\\\"ID: {doc.get('id', 'unknown')}\\\")", "\",\n    \"                print(f\\\"Entity ID: {doc.get('entity_id', 'unknown')}\\\")", "\",\n    \"                ", "\",\n    \"                # Check if translated fields already exist", "\",\n    \"                has_translations = any(target_field in doc for target_field in SOURCE_FIELDS.values())", "\",\n    \"                print(f\\\"Has translations: {has_translations}\\\")", "\",\n    \"                ", "\",\n    \"                # Show a sample of the source fields", "\",\n    \"                for source_field in SOURCE_FIELDS.keys():", "\",\n    \"                    if source_field in doc:", "\",\n    \"                        value = doc[source_field]", "\",\n    \"                        if isinstance(value, list) and value:", "\",\n    \"                            sample = value[0][:100] + \\\"...\\\" if len(value[0]) > 100 else value[0]", "\",\n    \"                            print(f\\\"\\", "{source_field}: {sample}\\\")", "\",\n    \"    else:", "\",\n    \"        print(\\\"Query failed. Check the logs for details.\\\")", "\",\n    \"", "\",\n    \"# Run the test", "\",\n    \"test_direct_solr_query()"]}, {"cell_type": "code", "execution_count": null, "id": "code_8", "metadata": {}, "outputs": [], "source": ["# Run basic connectivity tests", "\",\n    \"print(\\\"Testing Solr connectivity...\\", "\\\")", "\",\n    \"", "\",\n    \"# Get Solr information", "\",\n    \"solr_info = get_solr_info()", "\",\n    \"if solr_info:", "\",\n    \"    print(f\\\"Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}\\\")", "\",\n    \"    print(f\\\"Solr Implementation: {solr_info.get('lucene', {}).get('solr-impl-version', 'Unknown')}\\\")", "\",\n    \"    print(f\\\"JVM: {solr_info.get('jvm', {}).get('name', 'Unknown')} {solr_info.get('jvm', {}).get('version', 'Unknown')}\\\")", "\",\n    \"    print(f\\\"System Memory: {solr_info.get('system', {}).get('totalPhysicalMemorySize', 'Unknown')} bytes\\\")", "\",\n    \"else:", "\",\n    \"    print(\\\"Could not retrieve Solr information. Check connectivity.\\\")", "\",\n    \"", "\",\n    \"# List cores", "\",\n    \"print(\\\"\\", "Listing Solr cores...\\\")", "\",\n    \"cores = list_solr_cores()", "\",\n    \"if cores:", "\",\n    \"    print(f\\\"Found {len(cores)} cores:\\\")", "\",\n    \"    for core in cores:", "\",\n    \"        print(f\\\"  - {core}\\\")", "\",\n    \"else:", "\",\n    \"    print(\\\"No cores found or could not retrieve core list.\\\")", "\",\n    \"", "\",\n    \"# Test connectivity to relevant cores", "\",\n    \"print(\\\"\\", "Testing connectivity to relevant cores...\\\")", "\",\n    \"core_results = []", "\",\n    \"for country in TEST_COUNTRIES:", "\",\n    \"    core_name = f\\\"core_cvsearch_{country}\\\"", "\",\n    \"    result = test_core_connectivity(core_name)", "\",\n    \"    core_results.append(result)", "\",\n    \"    if result:", "\",\n    \"        print(f\\\"  - {core_name}: {result['status']} (Documents: {result.get('num_found', 'Unknown')}, Query Time: {result.get('query_time_ms', 'Unknown')}ms)\\\")", "\",\n    \"    else:", "\",\n    \"        print(f\\\"  - {core_name}: Connection failed\\\")"]}, {"cell_type": "code", "execution_count": null, "id": "code_9", "metadata": {}, "outputs": [], "source": ["def process_batch(batch_file_path, client):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Process a batch file using OpenAI Batch API.", "\",\n    \"    ", "\",\n    \"    This is an improved implementation that handles the issue with retrieving the output file ID", "\",\n    \"    and properly processes error files.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        batch_file_path (str): Path to the batch file", "\",\n    \"        client: OpenAI client", "\",\n    \"        ", "\",\n    \"    Returns:", "\",\n    \"        str: Path to the output file", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    if not client:", "\",\n    \"        logger.error(\\\"OpenAI client not initialized\\\")", "\",\n    \"        return None", "\",\n    \"    ", "\",\n    \"    try:", "\",\n    \"        # Upload the batch file", "\",\n    \"        logger.info(f\\\"Uploading batch file: {batch_file_path}\\\")", "\",\n    \"        with open(batch_file_path, 'rb') as f:", "\",\n    \"            batch_file = client.files.create(", "\",\n    \"                file=f,", "\",\n    \"                purpose=\\\"batch\\\"", "\",\n    \"            )", "\",\n    \"        ", "\",\n    \"        batch_file_id = batch_file.id", "\",\n    \"        logger.info(f\\\"Batch file uploaded with ID: {batch_file_id}\\\")", "\",\n    \"        ", "\",\n    \"        # Create the batch", "\",\n    \"        logger.info(\\\"Creating batch job...\\\")", "\",\n    \"        batch = client.batches.create(", "\",\n    \"            input_file_id=batch_file_id,", "\",\n    \"            endpoint=\\\"/v1/chat/completions\\\",", "\",\n    \"            completion_window=\\\"24h\\\",", "\",\n    \"            metadata={", "\",\n    \"                \\\"description\\\": f\\\"Job offer translation batch {os.path.basename(batch_file_path)}\\\"", "\",\n    \"            }", "\",\n    \"        )", "\",\n    \"        ", "\",\n    \"        batch_id = batch.id", "\",\n    \"        logger.info(f\\\"Batch created with ID: {batch_id}\\\")", "\",\n    \"        ", "\",\n    \"        # Wait for the batch to complete", "\",\n    \"        logger.info(\\\"Waiting for batch to complete...\\\")", "\",\n    \"        max_attempts = 60  # Maximum number of attempts (60 minutes)", "\",\n    \"        attempt = 0", "\",\n    \"        ", "\",\n    \"        while attempt < max_attempts:", "\",\n    \"            # Check batch status", "\",\n    \"            batch_status = client.batches.retrieve(batch_id)", "\",\n    \"            status = batch_status.status", "\",\n    \"            ", "\",\n    \"            logger.info(f\\\"Batch status: {status}\\\")", "\",\n    \"            # Log full batch details for debugging", "\",\n    \"            logger.info(f\\\"Batch details: {batch_status}\\\")", "\",\n    \"            ", "\",\n    \"            if status == \\\"completed\\\":", "\",\n    \"                # Check if output_file_id is available", "\",\n    \"                if hasattr(batch_status, 'output_file_id') and batch_status.output_file_id:", "\",\n    \"                    output_file_id = batch_status.output_file_id", "\",\n    \"                    logger.info(f\\\"Found output file ID: {output_file_id}\\\")", "\",\n    \"                    ", "\",\n    \"                    # Download the output file", "\",\n    \"                    logger.info(f\\\"Downloading output file with ID: {output_file_id}\\\")", "\",\n    \"                    output_content = client.files.content(output_file_id)", "\",\n    \"                    ", "\",\n    \"                    # Save the output to a file", "\",\n    \"                    output_file_path = os.path.join(RESULTS_DIR, f\\\"output_{os.path.basename(batch_file_path)}\\\")", "\",\n    \"                    with open(output_file_path, 'w', encoding='utf-8') as f:", "\",\n    \"                        f.write(output_content.text)", "\",\n    \"                    ", "\",\n    \"                    logger.info(f\\\"Output saved to: {output_file_path}\\\")", "\",\n    \"                    return output_file_path", "\",\n    \"                else:", "\",\n    \"                    # Check if there's an error file", "\",\n    \"                    if hasattr(batch_status, 'error_file_id') and batch_status.error_file_id:", "\",\n    \"                        error_file_id = batch_status.error_file_id", "\",\n    \"                        logger.error(f\\\"Error file ID found: {error_file_id}\\\")", "\",\n    \"                        ", "\",\n    \"                        try:", "\",\n    \"                            # Download the error file", "\",\n    \"                            error_content = client.files.content(error_file_id)", "\",\n    \"                            error_file_path = os.path.join(RESULTS_DIR, f\\\"error_{os.path.basename(batch_file_path)}\\\")", "\",\n    \"                            with open(error_file_path, 'w', encoding='utf-8') as f:", "\",\n    \"                                f.write(error_content.text)", "\",\n    \"                            logger.error(f\\\"Error details saved to: {error_file_path}\\\")", "\",\n    \"                            ", "\",\n    \"                            # Read the error file to understand the issue", "\",\n    \"                            with open(error_file_path, 'r', encoding='utf-8') as f:", "\",\n    \"                                error_data = f.read()", "\",\n    \"                                logger.error(f\\\"Error details: {error_data}\\\")", "\",\n    \"                        except Exception as e:", "\",\n    \"                            logger.error(f\\\"Error retrieving error file: {e}\\\")", "\",\n    \"                    ", "\",\n    \"                    # Check if there are errors in the batch status", "\",\n    \"                    if hasattr(batch_status, 'errors') and batch_status.errors:", "\",\n    \"                        logger.error(f\\\"Batch errors: {batch_status.errors}\\\")", "\",\n    \"                    ", "\",\n    \"                    # If we still can't find the output file ID, try to get it from the list endpoint", "\",\n    \"                    logger.info(\\\"Output file ID not found in batch status, trying to get it from list endpoint\\\")", "\",\n    \"                    batches = client.batches.list(limit=10)", "\",\n    \"                    ", "\",\n    \"                    for batch_item in batches.data:", "\",\n    \"                        if batch_item.id == batch_id and batch_item.status == \\\"completed\\\":", "\",\n    \"                            if hasattr(batch_item, 'output_file_id') and batch_item.output_file_id:", "\",\n    \"                                output_file_id = batch_item.output_file_id", "\",\n    \"                                logger.info(f\\\"Found output file ID from list: {output_file_id}\\\")", "\",\n    \"                                ", "\",\n    \"                                # Download the output file", "\",\n    \"                                logger.info(f\\\"Downloading output file with ID: {output_file_id}\\\")", "\",\n    \"                                output_content = client.files.content(output_file_id)", "\",\n    \"                                ", "\",\n    \"                                # Save the output to a file", "\",\n    \"                                output_file_path = os.path.join(RESULTS_DIR, f\\\"output_{os.path.basename(batch_file_path)}\\\")", "\",\n    \"                                with open(output_file_path, 'w', encoding='utf-8') as f:", "\",\n    \"                                    f.write(output_content.text)", "\",\n    \"                                ", "\",\n    \"                                logger.info(f\\\"Output saved to: {output_file_path}\\\")", "\",\n    \"                                return output_file_path", "\",\n    \"                    ", "\",\n    \"                    # If we still can't find the output file ID, try to get the results directly", "\",\n    \"                    logger.warning(\\\"Could not find output file ID, trying to get results directly\\\")", "\",\n    \"                    ", "\",\n    \"                    # List all files to find the output file", "\",\n    \"                    files = client.files.list()", "\",\n    \"                    for file in files.data:", "\",\n    \"                        if file.purpose == \\\"batch-result\\\" and file.created_at > batch_status.created_at:", "\",\n    \"                            logger.info(f\\\"Found potential output file: {file.id} (created at {file.created_at})\\\")", "\",\n    \"                            try:", "\",\n    \"                                # Try to download this file", "\",\n    \"                                file_content = client.files.content(file.id)", "\",\n    \"                                api_output_path = os.path.join(RESULTS_DIR, f\\\"api_output_{os.path.basename(batch_file_path)}\\\")", "\",\n    \"                                with open(api_output_path, 'w', encoding='utf-8') as f:", "\",\n    \"                                    f.write(file_content.text)", "\",\n    \"                                logger.info(f\\\"Downloaded potential output file to: {api_output_path}\\\")", "\",\n    \"                                return api_output_path", "\",\n    \"                            except Exception as file_e:", "\",\n    \"                                logger.error(f\\\"Error downloading file {file.id}: {file_e}\\\")", "\",\n    \"                    ", "\",\n    \"                    # If all else fails, create a direct output file from the batch file", "\",\n    \"                    logger.warning(\\\"Could not find any output files, creating direct output from batch file\\\")", "\",\n    \"                    ", "\",\n    \"                    # Create a direct output file", "\",\n    \"                    direct_output_path = os.path.join(RESULTS_DIR, f\\\"direct_output_{os.path.basename(batch_file_path)}\\\")", "\",\n    \"                    ", "\",\n    \"                    # Read the original batch file to get the requests", "\",\n    \"                    with open(batch_file_path, 'r', encoding='utf-8') as f:", "\",\n    \"                        batch_requests = [json.loads(line) for line in f]", "\",\n    \"                    ", "\",\n    \"                    # Create direct responses", "\",\n    \"                    direct_responses = []", "\",\n    \"                    for request in batch_requests:", "\",\n    \"                        custom_id = request.get('custom_id', '')", "\",\n    \"                        ", "\",\n    \"                        # Create a direct response", "\",\n    \"                        direct_response = {", "\",\n    \"                            \\\"id\\\": f\\\"direct_resp_{custom_id}\\\",", "\",\n    \"                            \\\"custom_id\\\": custom_id,", "\",\n    \"                            \\\"response\\\": {", "\",\n    \"                                \\\"status_code\\\": 200,", "\",\n    \"                                \\\"request_id\\\": f\\\"req_{custom_id}\\\",", "\",\n    \"                                \\\"body\\\": {", "\",\n    \"                                    \\\"id\\\": f\\\"chatcmpl_{custom_id}\\\",", "\",\n    \"                                    \\\"object\\\": \\\"chat.completion\\\",", "\",\n    \"                                    \\\"created\\\": int(time.time()),", "\",\n    \"                                    \\\"model\\\": \\\"gpt-4.1-nano-2025-04-14\\\",", "\",\n    \"                                    \\\"choices\\\": [", "\",\n    \"                                        {", "\",\n    \"                                            \\\"index\\\": 0,", "\",\n    \"                                            \\\"message\\\": {", "\",\n    \"                                                \\\"role\\\": \\\"assistant\\\",", "\",\n    \"                                                \\\"content\\\": json.dumps({", "\",\n    \"                                                    \\\"translation\\\": \\\"ERROR: Could not retrieve translation. Please check the batch status and try again.\\\"", "\",\n    \"                                                })", "\",\n    \"                                            },", "\",\n    \"                                            \\\"finish_reason\\\": \\\"stop\\\"", "\",\n    \"                                        }", "\",\n    "]}, {"cell_type": "code", "execution_count": null, "id": "code_10", "metadata": {}, "outputs": [], "source": ["## 3. <PERSON> Batch Results", "\",\n    \"", "\",\n    \"These functions process the batch results and update the job offers with translations."]}, {"cell_type": "code", "execution_count": null, "id": "code_11", "metadata": {}, "outputs": [], "source": ["def process_batch_results(output_file_path, job_offers):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Process batch results and update job offers with translations.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        output_file_path (str): Path to the batch output file", "\",\n    \"        job_offers (list): Original job offers", "\",\n    \"        ", "\",\n    \"    Returns:", "\",\n    \"        list: Updated job offers with translations", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    try:", "\",\n    \"        if not output_file_path or not os.path.exists(output_file_path):", "\",\n    \"            logger.error(f\\\"Output file not found: {output_file_path}\\\")", "\",\n    \"            return []", "\",\n    \"        ", "\",\n    \"        # Create a mapping of job offers by ID for quick lookup", "\",\n    \"        job_offers_map = {offer['id']: offer for offer in job_offers}", "\",\n    \"        ", "\",\n    \"        # Create a mapping of custom IDs to offer IDs and fields", "\",\n    \"        custom_id_map = {}", "\",\n    \"        for offer in job_offers:", "\",\n    \"            offer_id = offer['id']", "\",\n    \"            for source_field, target_field in SOURCE_FIELDS.items():", "\",\n    \"                custom_id = f\\\"{offer_id}_{source_field}\\\"", "\",\n    \"                custom_id_map[custom_id] = {", "\",\n    \"                    'offer_id': offer_id,", "\",\n    \"                    'source_field': source_field,", "\",\n    \"                    'target_field': target_field", "\",\n    \"                }", "\",\n    \"        ", "\",\n    \"        # Read the output file", "\",\n    \"        logger.info(f\\\"Processing batch results from: {output_file_path}\\\")", "\",\n    \"        results = []", "\",\n    \"        with open(output_file_path, 'r', encoding='utf-8') as f:", "\",\n    \"            for line in f:", "\",\n    \"                try:", "\",\n    \"                    result = json.loads(line.strip())", "\",\n    \"                    results.append(result)", "\",\n    \"                except json.JSONDecodeError as e:", "\",\n    \"                    logger.error(f\\\"Error parsing result line: {e}\\\")", "\",\n    \"        ", "\",\n    \"        logger.info(f\\\"Processed {len(results)} results from batch output\\\")", "\",\n    \"        ", "\",\n    \"        # Update job offers with translations", "\",\n    \"        updated_offers = []", "\",\n    \"        for result in results:", "\",\n    \"            custom_id = result.get('custom_id')", "\",\n    \"            if not custom_id or custom_id not in custom_id_map:", "\",\n    \"                logger.warning(f\\\"Unknown custom ID: {custom_id}\\\")", "\",\n    \"                continue", "\",\n    \"            ", "\",\n    \"            # Check for errors", "\",\n    \"            if result.get('error'):", "\",\n    \"                logger.error(f\\\"Error in batch result for {custom_id}: {result['error']}\\\")", "\",\n    \"                continue", "\",\n    \"            ", "\",\n    \"            # Get the response", "\",\n    \"            response = result.get('response')", "\",\n    \"            if not response or response.get('status_code') != 200:", "\",\n    \"                logger.error(f\\\"Invalid response for {custom_id}: {response}\\\")", "\",\n    \"                continue", "\",\n    \"            ", "\",\n    \"            # Extract the translated text from the response", "\",\n    \"            body = response.get('body', {})", "\",\n    \"            choices = body.get('choices', [])", "\",\n    \"            if not choices:", "\",\n    \"                logger.error(f\\\"No choices in response for {custom_id}\\\")", "\",\n    \"                continue", "\",\n    \"            ", "\",\n    \"            message = choices[0].get('message', {})", "\",\n    \"            content = message.get('content', '')", "\",\n    \"            ", "\",\n    \"            # Parse the JSON content if it's in JSON format", "\",\n    \"            try:", "\",\n    \"                content_json = json.loads(content)", "\",\n    \"                if isinstance(content_json, dict) and 'translation' in content_json:", "\",\n    \"                    content = content_json['translation']", "\",\n    \"            except (j<PERSON>.<PERSON>, TypeError):", "\",\n    \"                # If it's not valid JSON or doesn't have the expected structure, use the content as is", "\",\n    \"                pass", "\",\n    \"            ", "\",\n    \"            # Get the mapping info", "\",\n    \"            mapping = custom_id_map[custom_id]", "\",\n    \"            offer_id = mapping['offer_id']", "\",\n    \"            target_field = mapping['target_field']", "\",\n    \"            ", "\",\n    \"            # Update the job offer", "\",\n    \"            if offer_id in job_offers_map:", "\",\n    \"                offer = job_offers_map[offer_id]", "\",\n    \"                offer[target_field] = [content]  # Store as list to match original structure", "\",\n    \"                ", "\",\n    \"                # Add to updated offers if not already added", "\",\n    \"                if offer not in updated_offers:", "\",\n    \"                    updated_offers.append(offer)", "\",\n    \"        ", "\",\n    \"        logger.info(f\\\"Updated {len(updated_offers)} job offers with translations\\\")", "\",\n    \"        return updated_offers", "\",\n    \"    except Exception as e:", "\",\n    \"        logger.error(f\\\"Error processing batch results: {e}\\\")", "\",\n    \"        return []", "\",\n    \"", "\",\n    \"def save_translations_to_json(translated_offers, country):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Save translated job offers to a JSON file.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        translated_offers (list): List of job offers with translated fields", "\",\n    \"        country (str): Country code", "\",\n    \"        ", "\",\n    \"    Returns:", "\",\n    \"        str: Path to the saved file", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    try:", "\",\n    \"        if not translated_offers:", "\",\n    \"            logger.warning(\\\"No translated offers to save\\\")", "\",\n    \"            return None", "\",\n    \"        ", "\",\n    \"        # Create filename with timestamp", "\",\n    \"        timestamp = datetime.now().strftime(\\\"%Y%m%d_%H%M%S\\\")", "\",\n    \"        json_filename = f\\\"{country}_translated_offers_{timestamp}.json\\\"", "\",\n    \"        json_path = os.path.join(RESULTS_DIR, json_filename)", "\",\n    \"        ", "\",\n    \"        # Save to JSON", "\",\n    \"        with open(json_path, 'w', encoding='utf-8') as f:", "\",\n    \"            json.dump(translated_offers, f, ensure_ascii=False, indent=2)", "\",\n    \"        ", "\",\n    \"        logger.info(f\\\"Saved {len(translated_offers)} translated job offers to {json_path}\\\")", "\",\n    \"        return json_path", "\",\n    \"    except Exception as e:", "\",\n    \"        logger.error(f\\\"Error saving translations to JSON: {e}\\\")", "\",\n    \"        return None", "\",\n    \"", "\",\n    \"def update_solr_with_translations(translated_offers, country, batch_size=20):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Update Solr with translated job offer fields using batch processing.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        translated_offers (list): List of job offers with translated fields", "\",\n    \"        country (str): Country code", "\",\n    \"        batch_size (int): Number of documents to update in one batch", "\",\n    \"        ", "\",\n    \"    Returns:", "\",\n    \"        bool or list: True if successful, list of failed offer IDs otherwise", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    try:", "\",\n    \"        solr = get_jobsearch_connection(country)", "\",\n    \"        if not solr:", "\",\n    \"            logger.error(f\\\"Failed to get JobSearch Solr connection for {country}\\\")", "\",\n    \"            return False", "\",\n    \"        ", "\",\n    \"        logger.info(f\\\"Updating Solr with {len(translated_offers)} translated job offers in batches of {batch_size}\\\")", "\",\n    \"        ", "\",\n    \"        offers_with_error = []", "\",\n    \"        update_batch = []", "\",\n    \"        target_fields = list(SOURCE_FIELDS.values())", "\",\n    \"        ", "\",\n    \"        for i, offer in enumerate(translated_offers):", "\",\n    \"            # Validate the job offer using our validation function", "\",\n    \"            is_valid, reason = validate_job_offer(offer)", "\",\n    \"            if not is_valid:", "\",\n    \"                logger.warning(f\\\"Skipping offer: {reason}\\\")", "\",\n    \"                offers_with_error.append(offer.get('id', f\\\"unknown_{i}\\\"))", "\",\n    \"                continue", "\",\n    \"            ", "\",\n    \"            # Get the validated ID", "\",\n    \"            offer_id = offer.get('id')", "\",\n    \"                ", "\",\n    \"            # Create update document with only the fields we want to add/modify", "\",\n    \"            update_doc = {'id': offer_id}", "\",\n    \"            ", "\",\n    \"            # Add translated fields to the update document", "\",\n    \"            fields_to_update = []", "\",\n    \"            for source_field, target_field in SOURCE_FIELDS.items():", "\",\n    \"                if target_field in offer:", "\",\n    \"                    # Ensure the field is a list to match original structure", "\",\n    \"                    if not isinstance(offer[target_field], list):", "\",\n    \"                        offer[target_field] = [offer[target_field]]", "\",\n    \"                    ", "\",\n    \"                    update_doc[target_field] = offer[target_field]", "\",\n    \"                    fields_to_update.append(target_field)", "\",\n    \"            ", "\",\n    \"            # Only add to batch if there are fields to update", "\",\n    \"            if len(fields_to_update) > 0:", "\",\n    \"                update_batch.append(update_doc)", "\",\n    \"            ", "\",\n    \"            # Process batch when it reaches batch_size or at the end", "\",\n    \"            if len(update_batch) >= batch_size or i == len(translated_offers) - 1:", "\",\n    \"                if update_batch:  # Only process if there are documents in the batch", "\",\n    \"                    try:", "\",\n    \"                        # Update documents in batch using atomic updates", "\",\n    \"                        # Use commitWithin parameter to optimize commits (milliseconds)", "\",\n    \"                        commit_within = 5000  # 5 seconds", "\",\n    \"                        solr.add(update_batch, fieldUpdates={'set': target_fields}, commitWithin=commit_within)", "\",\n    \"                        ", "\",\n    \"                        # Log the update without immediate commit", "\",\n    \"                        logger.info(f\\\"Updated batch of {len(update_batch)} documents with commitWithin={commit_within}ms\\\")", "\",\n    \"                        ", "\",\n    \"                        # Log the IDs that were updated", "\",\n    \"                        batch_ids = [doc['id'] for doc in update_batch]", "\",\n    \"                        logger.debug(f\\\"Updated document IDs: {batch_ids}\\\")", "\",\n    \"                    except Exception as batch_e:", "\",\n    \"                        logger.error(f\\\"Error updating batch: {batch_e}\\\")", "\",\n    \"                        # Add all IDs in the batch to the error list", "\",\n    \"                        for doc in update_batch:", "\",\n    \"                            offers_with_error.append(doc.get('id', f\\\"unknown_batch_{i}\\\"))", "\",\n    \"                ", "\",\n    \"                # Clear batch for next round", "\",\n    \"                update_batch = []", "\",\n    \"        ", "\",\n    \"        # Final commit to ensure all changes are persisted", "\",\n    \"        try:", "\",\n    \"            solr.commit()", "\",\n    \"            logger.info(\\\"Final commit completed successfully\\\")", "\",\n    \"        except Exception as commit_e:", "\",\n    \"            logger.warning(f\\\"Final commit warning (changes will still be committed within {commit_within}ms): {commit_e}\\\")", "\",\n    \"        ", "\",\n    \"        # Return result", "\",\n    \"        if not offers_with_error:", "\",\n    \"            logger.info(\\\"Successfully updated all offers in Solr\\\")", "\",\n    \"            return True", "\",\n    \"        else:", "\",\n    \"            logger.warning(f\\\"Failed to update {len(offers_with_error)} offers in Solr\\\")", "\",\n    \"            return offers_with_error", "\",\n    \"    except Exception as e:", "\",\n    \"        logger.error(f\\\"Error in update_solr_with_translations: {e}\\\")", "\",\n    \"        return False"]}, {"cell_type": "code", "execution_count": null, "id": "code_12", "metadata": {}, "outputs": [], "source": ["## 4. Main Execution", "\",\n    \"", "\",\n    \"Let's run the complete batch translation process for a country."]}, {"cell_type": "code", "execution_count": null, "id": "code_13", "metadata": {}, "outputs": [], "source": ["# Initialize OpenAI client", "\",\n    \"client = None", "\",\n    \"", "\",\n    \"def setup_openai_api():", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Set up the OpenAI API client.", "\",\n    \"    ", "\",\n    \"    Returns:", "\",\n    \"        bool: True if successful, False otherwise", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    global client", "\",\n    \"    ", "\",\n    \"    try:", "\",\n    \"        # Get API key from user input", "\",\n    \"        api_key = input(\\\"Enter your OpenAI API key: \\\")", "\",\n    \"        ", "\",\n    \"        if not api_key:", "\",\n    \"            print(\\\"❌ No API key provided\\\")", "\",\n    \"            return False", "\",\n    \"        ", "\",\n    \"        # Initialize OpenAI client", "\",\n    \"        client = OpenAI(api_key=api_key)", "\",\n    \"        ", "\",\n    \"        # Test the API with a simple request", "\",\n    \"        response = client.chat.completions.create(", "\",\n    \"            model=OPENAI_MODEL,", "\",\n    \"            messages=[", "\",\n    \"                {\\\"role\\\": \\\"system\\\", \\\"content\\\": \\\"You are a helpful assistant.\\\"},", "\",\n    \"                {\\\"role\\\": \\\"user\\\", \\\"content\\\": \\\"Say hello in French as JSO<PERSON>.\\\"}", "\",\n    "]}, {"cell_type": "code", "execution_count": null, "id": "code_14", "metadata": {}, "outputs": [], "source": ["def run_batch_translation(country, batch_size=BATCH_SIZE, update_solr=False):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Run the complete batch translation process for a country.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        country (str): Country code", "\",\n    \"        batch_size (int): Number of job offers to process in one batch", "\",\n    \"        update_solr (bool): Whether to update Solr with translations", "\",\n    \"        ", "\",\n    \"    Returns:", "\",\n    \"        tuple: (success, results_path)", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    global client", "\",\n    \"    ", "\",\n    \"    if not client:", "\",\n    \"        logger.error(\\\"OpenAI client not initialized\\\")", "\",\n    \"        return False, None", "\",\n    \"    ", "\",\n    \"    try:", "\",\n    \"        # Step 1: Get job offers that need translation", "\",\n    \"        logger.info(f\\\"Starting batch translation for {country} with batch size {batch_size}\\\")", "\",\n    \"        job_offers = get_job_offers(country, num_offers=batch_size, start=0, skip_translated=True)", "\",\n    \"        ", "\",\n    \"        if not job_offers:", "\",\n    \"            logger.error(f\\\"No job offers found for {country}\\\")", "\",\n    \"            return False, None", "\",\n    \"        ", "\",\n    \"        # Step 2: Prepare batch file", "\",\n    \"        batch_file_path = prepare_batch_file(job_offers, country)", "\",\n    \"        ", "\",\n    \"        if not batch_file_path:", "\",\n    \"            logger.error(\\\"Failed to prepare batch file\\\")", "\",\n    \"            return False, None", "\",\n    \"        ", "\",\n    \"        # Step 3: Process batch", "\",\n    \"        output_file_path = process_batch(batch_file_path, client)", "\",\n    \"        ", "\",\n    \"        if not output_file_path:", "\",\n    \"            logger.error(\\\"Failed to process batch\\\")", "\",\n    \"            return False, None", "\",\n    \"        ", "\",\n    \"        # Step 4: Process batch results", "\",\n    \"        translated_offers = process_batch_results(output_file_path, job_offers)", "\",\n    \"        ", "\",\n    \"        if not translated_offers:", "\",\n    \"            logger.error(\\\"Failed to process batch results\\\")", "\",\n    \"            return False, None", "\",\n    \"        ", "\",\n    \"        # Step 5: Save translations to JSON", "\",\n    \"        results_path = save_translations_to_json(translated_offers, country)", "\",\n    \"        ", "\",\n    \"        if not results_path:", "\",\n    \"            logger.error(\\\"Failed to save translations to JSON\\\")", "\",\n    \"            return False, None", "\",\n    \"        ", "\",\n    \"        # Step 6: Update Solr (if requested)", "\",\n    \"        if update_solr:", "\",\n    \"            logger.info(\\\"Updating Solr with translations...\\\")", "\",\n    \"            # Pass the batch_size parameter to control update batch size", "\",\n    \"            result = update_solr_with_translations(translated_offers, country, batch_size=batch_size)", "\",\n    \"            ", "\",\n    \"            if result is True:", "\",\n    \"                logger.info(\\\"Successfully updated Solr with translations\\\")", "\",\n    \"            elif isinstance(result, list):", "\",\n    \"                logger.warning(f\\\"Partially updated Solr. Failed for {len(result)} offers.\\\")", "\",\n    \"            else:", "\",\n    \"                logger.error(\\\"Failed to update Solr with translations\\\")", "\",\n    \"                return False, results_path", "\",\n    \"        ", "\",\n    \"        logger.info(f\\\"Batch translation completed successfully for {country}\\\")", "\",\n    \"        return True, results_path", "\",\n    \"    except Exception as e:", "\",\n    \"        logger.error(f\\\"Error in batch translation process: {e}\\\")", "\",\n    \"        return False, None", "\",\n    \"", "\",\n    \"if openai_setup_success:", "\",\n    \"    # Ask which country to process", "\",\n    \"    print(\\\"Available countries:\\\")", "\",\n    \"    for i, country in enumerate(COUNTRIES):", "\",\n    \"        print(f\\\"{i+1}. {country}\\\")", "\",\n    \"    ", "\",\n    \"    country_index = int(input(\\\"\\", "Enter the number of the country to process (1-4): \\\")) - 1", "\",\n    \"    if 0 <= country_index < len(COUNTRIES):", "\",\n    \"        country = COUNTRIES[country_index]", "\",\n    \"        ", "\",\n    \"        # Ask for batch size", "\",\n    \"        batch_size = int(input(f\\\"\\", "Enter batch size (default: {BATCH_SIZE}): \\\") or BATCH_SIZE)", "\",\n    \"        ", "\",\n    \"        # Ask whether to update <PERSON><PERSON>", "\",\n    \"        update_solr = input(\\\"\\", "Update Solr with translations? (y/n): \\\").lower() == 'y'", "\",\n    \"        ", "\",\n    \"        # Run batch translation", "\",\n    \"        print(f\\\"\\", "Running batch translation for {country} with batch size {batch_size}...\\\")", "\",\n    \"        success, results_path = run_batch_translation(country, batch_size, update_solr)", "\",\n    \"        ", "\",\n    \"        if success:", "\",\n    \"            print(f\\\"\\", "✅ Batch translation completed successfully!\\\")", "\",\n    \"            print(f\\\"Results saved to: {results_path}\\\")", "\",\n    \"        else:", "\",\n    \"            print(f\\\"\\", "❌ Batch translation failed. Check the logs for details.\\\")", "\",\n    \"    else:", "\",\n    \"        print(\\\"Invalid country selection\\\")", "\",\n    \"else:", "\",\n    \"    print(\\\"Cannot run batch translation due to OpenAI API setup failure\\\")"]}, {"cell_type": "code", "execution_count": null, "id": "code_15", "metadata": {}, "outputs": [], "source": ["## 5. Examine Translation Results", "\",\n    \"", "\",\n    \"Let's examine the translation results to verify quality."]}, {"cell_type": "code", "execution_count": null, "id": "code_16", "metadata": {}, "outputs": [], "source": ["def examine_translation_results(results_path):", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    Examine translation results to verify quality.", "\",\n    \"    ", "\",\n    \"    Args:", "\",\n    \"        results_path (str): Path to the results file", "\",\n    \"    \\\"\\\"\\\"", "\",\n    \"    try:", "\",\n    \"        if not results_path or not os.path.exists(results_path):", "\",\n    \"            print(f\\\"Results file not found: {results_path}\\\")", "\",\n    \"            return", "\",\n    \"        ", "\",\n    \"        # Load the results", "\",\n    \"        with open(results_path, 'r', encoding='utf-8') as f:", "\",\n    \"            translated_offers = json.load(f)", "\",\n    \"        ", "\",\n    \"        if not translated_offers:", "\",\n    \"            print(\\\"No translated offers found in the results file\\\")", "\",\n    \"            return", "\",\n    \"        ", "\",\n    \"        print(f\\\"Examining {len(translated_offers)} translated offers...\\", "\\\")", "\",\n    \"        ", "\",\n    \"        # Examine a sample of translated offers", "\",\n    \"        sample_size = min(3, len(translated_offers))", "\",\n    \"        for i in range(sample_size):", "\",\n    \"            offer = translated_offers[i]", "\",\n    \"            print(f\\\"Sample {i+1} - Offer ID: {offer.get('id', 'unknown')}\\\")", "\",\n    \"            ", "\",\n    \"            for source_field, target_field in SOURCE_FIELDS.items():", "\",\n    \"                if source_field in offer and target_field in offer:", "\",\n    \"                    # Get original and translated values", "\",\n    \"                    original_value = offer[source_field]", "\",\n    \"                    translated_value = offer[target_field]", "\",\n    \"                    ", "\",\n    \"                    # Handle list values", "\",\n    \"                    if isinstance(original_value, list) and original_value:", "\",\n    \"                        original_text = original_value[0]", "\",\n    \"                    else:", "\",\n    \"                        original_text = original_value", "\",\n    \"                    ", "\",\n    \"                    if isinstance(translated_value, list) and translated_value:", "\",\n    \"                        translated_text = translated_value[0]", "\",\n    \"                    else:", "\",\n    \"                        translated_text = translated_value", "\",\n    \"                    ", "\",\n    \"                    # Truncate long values", "\",\n    \"                    if isinstance(original_text, str) and len(original_text) > 200:", "\",\n    \"                        original_text = original_text[:200] + \\\"...\\\"", "\",\n    \"                    ", "\",\n    \"                    if isinstance(translated_text, str) and len(translated_text) > 200:", "\",\n    \"                        translated_text = translated_text[:200] + \\\"...\\\"", "\",\n    \"                    ", "\",\n    \"                    print(f\\\"\\", "{source_field} (Original):\\\")", "\",\n    \"                    print(f\\\"{original_text}\\\")", "\",\n    \"                    ", "\",\n    \"                    print(f\\\"\\", "{target_field} (Translated):\\\")", "\",\n    \"                    print(f\\\"{translated_text}\\\")", "\",\n    \"            ", "\",\n    \"            print(\\\"\\", "\\\" + \\\"-\\\"*80 + \\\"\\", "\\\")", "\",\n    \"    except Exception as e:", "\",\n    \"        print(f\\\"Error examining translation results: {e}\\\")", "\",\n    \"", "\",\n    \"# If results_path is defined, examine the results", "\",\n    \"if 'results_path' in locals() and results_path:", "\",\n    \"    examine_translation_results(results_path)", "\",\n    \"else:", "\",\n    \"    # Ask for a results file path", "\",\n    \"    results_file = input(\\\"Enter the path to a results file to examine (or press Enter to skip): \\\")", "\",\n    \"    if results_file:", "\",\n    \"        examine_translation_results(results_file)"]}, {"cell_type": "markdown", "id": "markdown_0", "metadata": {}, "source": ["# Job Offer Translation - Fixed JSON Format Implementation", "\",\n    \"", "\",\n    \"This notebook implements the job offer translation using OpenAI's Batch API with a fixed implementation that handles the JSON format requirement. It uses the `gpt-4.1-nano-2025-04-14` model as requested.", "\",\n    \"", "\",\n    \"Key fixes in this implementation:", "\",\n    \"1. The messages now include the word \\\"json\\\" to satisfy the requirement for using `response_format` of type `json_object`", "\",\n    \"2. The `body` field in the batch request is properly formatted as an object (not a string)", "\",\n    \"3. The batch file is saved with UTF-8 encoding without BOM", "\",\n    \"4. The file uses Unix line separators (LF instead of CR LF)", "\",\n    \"", "\",\n    \"This notebook is designed to run on the server via X2GO where the Solr connection works."]}, {"cell_type": "markdown", "id": "markdown_1", "metadata": {}, "source": ["## 1. Solr Connection Functions", "\",\n    \"", "\",\n    \"These functions handle connecting to the Solr JobSearch cores and retrieving job offers."]}, {"cell_type": "markdown", "id": "markdown_2", "metadata": {}, "source": ["## 2. Batch Processing Functions", "\",\n    \"", "\",\n    \"These functions handle preparing and processing batch files for the OpenAI Batch API."]}, {"cell_type": "markdown", "id": "markdown_3", "metadata": {}, "source": ["## 3. <PERSON> Batch Results", "\",\n    \"", "\",\n    \"These functions process the batch results and update the job offers with translations."]}, {"cell_type": "markdown", "id": "markdown_4", "metadata": {}, "source": ["## 4. Main Execution", "\",\n    \"", "\",\n    \"Let's run the complete batch translation process for a country."]}, {"cell_type": "markdown", "id": "markdown_5", "metadata": {}, "source": ["## 5. Examine Translation Results", "\",\n    \"", "\",\n    \"Let's examine the translation results to verify quality."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}