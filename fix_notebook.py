import json
import nbformat
import sys

try:
    # Try to read the notebook
    print("Attempting to read the notebook...")
    nb = nbformat.read('JOB_OFFER_TRANSLATION_JSON_FORMAT.ipynb', as_version=4)
    print("Successfully read notebook")
    
    # Write the fixed notebook
    nbformat.write(nb, 'JOB_OFFER_TRANSLATION_JSON_FORMAT_fixed.ipynb')
    print("Successfully wrote fixed notebook")
except Exception as e:
    print(f"Error: {e}")
    
    # Try to manually fix the JSON
    try:
        print("Attempting to manually fix the JSON...")
        with open('JOB_OFFER_TRANSLATION_JSON_FORMAT.ipynb', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse the JSON to identify errors
        try:
            json.loads(content)
            print("JSON is valid, but nbformat couldn't read it")
        except json.JSONDecodeError as je:
            print(f"JSON error at line {je.lineno}, column {je.colno}: {je.msg}")
            print(f"Error context: {content[max(0, je.pos-50):min(len(content), je.pos+50)]}")
    except Exception as e2:
        print(f"Error during manual fix attempt: {e2}")
