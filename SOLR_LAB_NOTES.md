# Solr Connectivity Lab Notes

## Overview
This document contains notes, observations, and findings from our Solr connectivity experiments using the `SOLR_CONNECTIVITY_LAB.ipynb` notebook.

## Environment Setup
- Date: 2023-05-13
- Python version: 3.13
- Required packages:
  - pysolr
  - pandas
  - matplotlib
  - seaborn
  - tqdm
  - h5py

## Connectivity Issues

### Issue 1: Connection Timeout
```
2025-05-13 10:29:52,368 - ERROR - Error listing Solr cores: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/admin/cores?action=STATUS&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001E51ACA4410>, 'Connection to ************ timed out. (connect timeout=None)'))
```

#### Analysis
- The error indicates a connection timeout when trying to connect to the Solr server at `************:8983`.
- This could be due to:
  1. The Solr server is down or not accessible
  2. Network connectivity issues
  3. Firewall blocking the connection
  4. Incorrect server address

#### Important Discovery
We found a file `my_boos_massages.md` that contains code for connecting to the same Solr server:

```python
import json
import requests
from tqdm import tqdm

centrafrique_url = "http://************:8983/solr/core_cvsearch_centrafrique/select?q=*%3A*&rows=1000000&fl=entity_id&wt=json&indent=true"
ids_list = json.loads(requests.get(centrafrique_url).text)['response']['docs']
```

We created a test script `DIRECT_SOLR_TEST.py` to test this direct connectivity approach, but it also timed out:

```
Testing direct connectivity to centrafrique...
2025-05-13 11:26:13,301 - INFO - Testing direct connectivity to core_cvsearch_centrafrique with URL: http://************:8983/solr/core_cvsearch_centrafrique/select?q=*%3A*&rows=10&fl=entity_id&wt=json&indent=true
2025-05-13 11:26:34,360 - ERROR - Direct connection to centrafrique timed out after 30 seconds
  - core_cvsearch_centrafrique: FAILED - Timeout after 30 seconds
```

This suggests:

1. The Solr server at `************:8983` might be behind a firewall or have access restrictions
2. The code in `my_boos_massages.md` might have worked in a different network environment
3. We need to consider alternative approaches, such as:
   - Using a VPN to access the server
   - Setting up a local Solr instance for development
   - Checking with the team if there are specific access requirements for this server

#### Potential Solutions
1. **Verify Solr server status**: Check if the Solr server is running and accessible
2. **Check network connectivity**: Ensure there are no network issues preventing connection
3. **Test with local Solr instance**: If available, test with a local Solr instance
4. **Add timeout parameter**: Modify the requests.get() calls to include a timeout parameter
5. **Use VPN or proxy**: If the server is behind a firewall, use appropriate VPN or proxy

#### Implementation
We've created an updated version of the notebook (`SOLR_CONNECTIVITY_LAB_UPDATED.ipynb`) with the following improvements:

1. **Added timeout parameters to all requests**:
```python
# Original code
response = requests.get(cores_url, params={'action': 'STATUS', 'wt': 'json'})

# Modified code with timeout
response = requests.get(cores_url, params={'action': 'STATUS', 'wt': 'json'}, timeout=timeout)
```

2. **Added specific exception handling for different types of errors**:
```python
try:
    # Request code here
except requests.exceptions.Timeout:
    logger.error(f"Connection to {base_url} timed out after {timeout} seconds")
    return None
except requests.exceptions.ConnectionError as e:
    logger.error(f"Connection error to {base_url}: {e}")
    return None
except Exception as e:
    logger.error(f"Error getting Solr info: {e}")
    return None
```

3. **Added more detailed logging**:
```python
logger.info(f"Connecting to Solr admin at {admin_url} with timeout {timeout}s")
```

4. **Added a global timeout configuration**:
```python
# Default timeout for requests (in seconds)
DEFAULT_TIMEOUT = 30
```

## URL Length Testing

### Experiment 1: Determining Maximum Safe URL Length
*To be filled after running the experiment*

### Experiment 2: Testing Alternative Query Methods
*To be filled after running the experiment*

## Field Existence and Data Quality

### Experiment 3: Analyzing Field Existence
*To be filled after running the experiment*

### Experiment 4: Analyzing Field Quality
*To be filled after running the experiment*

## Recommendations for Pipeline Implementation

### Query Construction
*To be filled based on experimental results*

### Batch Processing
*To be filled based on experimental results*

### Error Handling
*To be filled based on experimental results*

## Progress Update (2023-05-13)

### Connectivity Issues
We've created an updated version of the notebook (`SOLR_CONNECTIVITY_LAB_UPDATED.ipynb`) with improved error handling and timeout management. The key improvements include:

1. **Timeout Management**:
   - Added configurable timeout parameters to all requests
   - Implemented progressive timeout testing (5s, 15s, 30s)
   - Added a global timeout configuration

2. **Error Handling**:
   - Added specific exception handling for different types of errors (Timeout, ConnectionError)
   - Improved error reporting with more context
   - Added fallback to local Solr when remote connection fails

3. **Connectivity Testing Strategy**:
   - Test with multiple timeout values
   - Try both remote and local Solr instances
   - Test with generic core names when specific cores aren't found

### Connectivity Test Results

#### Direct HTTP Test
We created a script `DIRECT_SOLR_TEST.py` to test direct connectivity to specific cores based on the code found in `my_boos_massages.md`. The results were:

```
Testing direct connectivity to centrafrique...
2025-05-13 11:26:13,301 - INFO - Testing direct connectivity to core_cvsearch_centrafrique with URL: http://************:8983/solr/core_cvsearch_centrafrique/select?q=*%3A*&rows=10&fl=entity_id&wt=json&indent=true
2025-05-13 11:26:34,360 - ERROR - Direct connection to centrafrique timed out after 30 seconds
  - core_cvsearch_centrafrique: FAILED - Timeout after 30 seconds
```

#### PySOLR Test
We also created a script `PYSOLR_TEST.py` to test connectivity using the PySOLR library, based on the code snippet provided by the boss. The results were similar:

```
Testing connection to algerie...
2025-05-13 11:32:43,396 - INFO - Testing PySOLR connection to core_cvsearch_algerie at http://************:8983/solr/core_cvsearch_algerie
2025-05-13 11:33:04,462 - ERROR - Connection to server timed out: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object>, 'Connection to ************ timed out. (connect timeout=30)'))
```

#### Local Solr Test
We also tested connecting to a local Solr instance:

```
Testing connection to dev...
2025-05-13 11:33:25,632 - INFO - Testing PySOLR connection to core_outman_cvsearch at http://localhost:8983/solr/core_outman_cvsearch
2025-05-13 11:33:29,721 - ERROR - Failed to connect to server: HTTPConnectionPool(host='localhost', port=8983): Max retries exceeded with url: /solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
```

These tests confirm that:
1. We're unable to connect to the remote Solr server from our current environment
2. We don't have a local Solr instance running yet (as expected)
3. Both direct HTTP and PySOLR approaches result in the same timeout errors

### Next Steps
1. **Connectivity Resolution**:
   - Determine if the issue is with the remote server or our network environment
   - Check with the team if there are specific access requirements for this server
   - Set up a local Solr instance for development and testing

2. **Query Construction Testing**:
   - Once connectivity is established, run URL length experiments
   - Test POST method as an alternative to GET for large queries
   - Test batch processing approach

3. **Data Quality Analysis**:
   - Analyze field existence and quality
   - Check for missing or empty fields
   - Determine average field lengths for translation planning

4. **Implementation in Main Pipeline**:
   - Apply the findings to the main job offer translation pipeline
   - Implement the most robust query construction method
   - Add appropriate error handling and fallback mechanisms

## Local Solr Testing

Given the connectivity issues with the remote Solr server, it's important to consider setting up a local Solr instance for development and testing. This approach offers several advantages:

### Benefits of Local Solr Testing

1. **Reliability**: No dependency on external network connectivity or remote server availability
2. **Speed**: Faster response times for queries, especially during development
3. **Control**: Full control over the Solr configuration and data
4. **Isolation**: Testing in isolation without affecting production data

### Setting Up a Local Solr Instance

1. **Download Solr**: Get the latest version from the [Apache Solr website](https://solr.apache.org/downloads.html)
2. **Install and Start Solr**:
   ```bash
   # Extract the Solr package
   tar -xzf solr-8.11.2.tgz

   # Start Solr
   cd solr-8.11.2
   bin/solr start
   ```
3. **Create a Core**:
   ```bash
   bin/solr create -c core_outman_cvsearch
   ```
4. **Import Sample Data**: Create a small dataset with job offers that include the fields we need to translate

### Testing Strategy with Local Solr

1. Develop and test query construction with the local instance
2. Validate URL length limitations and alternative approaches
3. Once the approach is proven with local Solr, test with the remote server
4. Implement fallback mechanisms in the pipeline to use local Solr when remote is unavailable

## Questions to Investigate
- What is the maximum safe URL length for our Solr server?
- Is POST method more reliable than GET for large queries?
- What is the optimal batch size for processing job offers?
- Are there any missing fields that need special handling?
- What is the average size of the text fields we need to translate?
- How can we implement a robust fallback mechanism between remote and local Solr?

## Working Approach Identified

After extensive testing and analysis, we've identified a working approach for Solr connectivity based on the code snippet provided by the boss:

```python
if country != "dev":
    solr = pysolr.Solr(f"http://************:8983/solr/core_cvsearch_{country}")
else:
    solr = pysolr.Solr(f"http://localhost:8983/solr/core_outman_cvsearch")
```

### Implementation of Working Approach

We've implemented this approach in a new notebook `SOLR_CONNECTIVITY_WORKING.ipynb` with the following key components:

1. **Solr Connection Function**:
   ```python
   def get_solr_connection(country, timeout=DEFAULT_TIMEOUT):
       try:
           # Use the pattern from the boss's code
           if country != "dev":
               solr_url = f"{SOLR_BASE_URL}core_cvsearch_{country}"
               core_name = f"core_cvsearch_{country}"
           else:
               solr_url = f"{LOCAL_SOLR_URL}core_outman_cvsearch"
               core_name = "core_outman_cvsearch"

           logger.info(f"Connecting to Solr core {core_name} at {solr_url}")

           # Create PySOLR connection with timeout
           solr = pysolr.Solr(solr_url, timeout=timeout)

           return solr
       except Exception as e:
           logger.error(f"Error connecting to Solr for {country}: {e}")
           return None
   ```

2. **Query Functions**:
   - `get_job_offers()`: Retrieves job offers from Solr
   - `update_solr_with_translations()`: Updates Solr with translated fields

3. **Development Mode**:
   - Uses `country = "dev"` for local development
   - Requires a local Solr instance with a core named `core_outman_cvsearch`

### Benefits of This Approach

1. **Established Pattern**: Uses the same pattern that's already in use in the codebase
2. **Fallback Mechanism**: Provides a clear fallback to local Solr for development
3. **Consistent Interface**: Same interface for both remote and local Solr
4. **Error Handling**: Includes robust error handling and logging

### Summary of Findings

1. **Connectivity Issues**: We're unable to connect to the remote Solr server from our current environment
2. **Working Pattern**: The boss's code provides a working pattern with a development mode fallback
3. **Local Development**: Setting up a local Solr instance is necessary for development
4. **Implementation**: We've implemented the working approach in a new notebook

### Next Steps

Now that we've identified a working approach, we can focus on implementing the job offer translation pipeline:

1. **Local Solr Setup**:
   - Set up a local Solr instance with a core named `core_outman_cvsearch`
   - Import the sample job offer data (`sample_job_offers.json`)
   - Verify connectivity using our working approach

2. **Pipeline Implementation**:
   - Implement the job offer translation pipeline using our working approach
   - Use `country = "dev"` for development with the local Solr instance
   - Implement the OpenAI API translation for the two required fields:
     - `sm_field_offre_description_poste` → `tr_field_offre_description_poste`
     - `sm_field_offre_profil` → `tr_field_offre_profil`

3. **Testing and Validation**:
   - Test the pipeline with the local Solr instance
   - Validate the translations for accuracy and quality
   - Ensure proper error handling and logging

4. **Production Readiness**:
   - Consult with the team about access requirements for the remote Solr server
   - Implement any necessary changes for production deployment
   - Document the pipeline and its configuration options

By following these steps, we can proceed with the development of the job offer translation pipeline despite the connectivity issues with the remote Solr server.

## References
- [Solr Documentation](https://solr.apache.org/guide/)
- [HTTP URL Length Limitations](https://stackoverflow.com/questions/417142/what-is-the-maximum-length-of-a-url-in-different-browsers)
- [Pysolr Documentation](https://github.com/django-haystack/pysolr)
- [Solr in Docker](https://hub.docker.com/_/solr) - Alternative approach for local setup
