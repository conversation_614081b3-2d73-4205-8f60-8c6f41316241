{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Verify Morocco Translation Updates\n", "\n", "This notebook verifies the successful translation updates in the Morocco Solr database by checking specific document IDs.\n", "\n", "## Target Documents\n", "We will verify the following documents that were reported as successfully updated:\n", "- c8xu1x/node/157221\n", "- c8xu1x/node/157235\n", "- c8xu1x/node/157253\n", "- c8xu1x/node/157258\n", "- c8xu1x/node/157268\n", "\n", "## Verification Steps\n", "1. Connect to Morocco Solr core\n", "2. Query each document by ID\n", "3. Check for presence of translated fields\n", "4. Compare original vs translated content\n", "5. Generate verification report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import requests\n", "import logging\n", "from datetime import datetime\n", "import os\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('morocco_verification')\n", "\n", "# Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "COUNTRY = \"maroc\"\n", "CORE_NAME = f\"core_jobsearch_{COUNTRY}\"\n", "SOLR_URL = f\"{SOLR_BASE_URL}{CORE_NAME}\"\n", "\n", "# Target document IDs from successful updates\n", "TARGET_DOCUMENT_IDS = [\n", "    \"c8xu1x/node/157221\",\n", "    \"c8xu1x/node/157235\", \n", "    \"c8xu1x/node/157253\",\n", "    \"c8xu1x/node/157258\",\n", "    \"c8xu1x/node/157268\"\n", "]\n", "\n", "# Fields to check\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "# Create results directory\n", "RESULTS_DIR = \"verification_results\"\n", "os.makedirs(RESULTS_DIR, exist_ok=True)\n", "\n", "print(f\"✅ Configuration loaded\")\n", "print(f\"Solr URL: {SOLR_URL}\")\n", "print(f\"Target Documents: {len(TARGET_DOCUMENT_IDS)}\")\n", "print(f\"Results Directory: {RESULTS_DIR}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def query_document_by_id(doc_id):\n", "    \"\"\"\n", "    Query a specific document by its ID from Solr.\n", "    \n", "    Args:\n", "        doc_id (str): Document ID to query\n", "        \n", "    Returns:\n", "        dict: Document data or None if not found\n", "    \"\"\"\n", "    try:\n", "        # Construct query URL\n", "        query_url = f\"{SOLR_URL}/select\"\n", "        \n", "        # Query parameters\n", "        params = {\n", "            'q': f'id:\"{doc_id}\"',  # Exact ID match with quotes for special characters\n", "            'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil,tr_field_offre_description_poste,tr_field_offre_profil',\n", "            'rows': 1,\n", "            'wt': 'json'\n", "        }\n", "        \n", "        logger.debug(f\"Querying document: {doc_id}\")\n", "        logger.debug(f\"Query URL: {query_url}\")\n", "        logger.debug(f\"Query params: {params}\")\n", "        \n", "        response = requests.get(query_url, params=params, timeout=30)\n", "        \n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            docs = data.get('response', {}).get('docs', [])\n", "            \n", "            if docs:\n", "                logger.info(f\"✅ Found document: {doc_id}\")\n", "                return docs[0]\n", "            else:\n", "                logger.warning(f\"❌ Document not found: {doc_id}\")\n", "                return None\n", "        else:\n", "            logger.error(f\"❌ Query failed for {doc_id}: {response.status_code} - {response.text}\")\n", "            return None\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"❌ Error querying document {doc_id}: {e}\")\n", "        return None\n", "\n", "def analyze_document(doc):\n", "    \"\"\"\n", "    Analyze a document to check translation status.\n", "    \n", "    Args:\n", "        doc (dict): Document data\n", "        \n", "    Returns:\n", "        dict: Analysis results\n", "    \"\"\"\n", "    if not doc:\n", "        return None\n", "    \n", "    analysis = {\n", "        'id': doc.get('id'),\n", "        'entity_id': doc.get('entity_id'),\n", "        'fields': {},\n", "        'translation_status': 'unknown',\n", "        'has_translations': <PERSON><PERSON><PERSON>,\n", "        'translation_count': 0\n", "    }\n", "    \n", "    # Check each field pair\n", "    for source_field, target_field in SOURCE_FIELDS.items():\n", "        field_analysis = {\n", "            'source_field': source_field,\n", "            'target_field': target_field,\n", "            'has_source': source_field in doc and bool(doc[source_field]),\n", "            'has_target': target_field in doc and bool(doc[target_field]),\n", "            'source_content': doc.get(source_field, []),\n", "            'target_content': doc.get(target_field, []),\n", "            'source_length': len(str(doc.get(source_field, ''))) if doc.get(source_field) else 0,\n", "            'target_length': len(str(doc.get(target_field, ''))) if doc.get(target_field) else 0\n", "        }\n", "        \n", "        # Check if translation exists\n", "        if field_analysis['has_target']:\n", "            analysis['has_translations'] = True\n", "            analysis['translation_count'] += 1\n", "        \n", "        analysis['fields'][source_field] = field_analysis\n", "    \n", "    # Determine overall translation status\n", "    if analysis['translation_count'] == 0:\n", "        analysis['translation_status'] = 'no_translations'\n", "    elif analysis['translation_count'] == len(SOURCE_FIELDS):\n", "        analysis['translation_status'] = 'fully_translated'\n", "    else:\n", "        analysis['translation_status'] = 'partially_translated'\n", "    \n", "    return analysis\n", "\n", "def print_document_summary(analysis):\n", "    \"\"\"\n", "    Print a summary of document analysis.\n", "    \n", "    Args:\n", "        analysis (dict): Document analysis results\n", "    \"\"\"\n", "    if not analysis:\n", "        print(\"❌ No analysis data available\")\n", "        return\n", "    \n", "    print(f\"\\n📄 Document: {analysis['id']}\")\n", "    print(f\"   Entity ID: {analysis['entity_id']}\")\n", "    print(f\"   Translation Status: {analysis['translation_status']}\")\n", "    print(f\"   Has Translations: {'✅ YES' if analysis['has_translations'] else '❌ NO'}\")\n", "    print(f\"   Translation Count: {analysis['translation_count']}/{len(SOURCE_FIELDS)}\")\n", "    \n", "    for source_field, field_data in analysis['fields'].items():\n", "        print(f\"\\n   📝 {source_field}:\")\n", "        print(f\"      Source: {'✅' if field_data['has_source'] else '❌'} ({field_data['source_length']} chars)\")\n", "        print(f\"      Target: {'✅' if field_data['has_target'] else '❌'} ({field_data['target_length']} chars)\")\n", "        \n", "        # Show content preview\n", "        if field_data['has_source']:\n", "            source_preview = str(field_data['source_content'])[:100] + \"...\" if len(str(field_data['source_content'])) > 100 else str(field_data['source_content'])\n", "            print(f\"      Source Preview: {source_preview}\")\n", "        \n", "        if field_data['has_target']:\n", "            target_preview = str(field_data['target_content'])[:100] + \"...\" if len(str(field_data['target_content'])) > 100 else str(field_data['target_content'])\n", "            print(f\"      Target Preview: {target_preview}\")\n", "\n", "print(\"✅ Verification functions loaded\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute Verification\n", "\n", "Run the verification process for all target documents."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_verification():\n", "    \"\"\"\n", "    Run the complete verification process for all target documents.\n", "    \n", "    Returns:\n", "        dict: Verification results summary\n", "    \"\"\"\n", "    logger.info(\"🔍 Starting Morocco Translation Verification\")\n", "    \n", "    verification_results = {\n", "        'country': COUNTRY,\n", "        'timestamp': datetime.now().isoformat(),\n", "        'target_documents': TARGET_DOCUMENT_IDS.copy(),\n", "        'total_documents': len(TARGET_DOCUMENT_IDS),\n", "        'documents_found': 0,\n", "        'documents_not_found': 0,\n", "        'fully_translated': 0,\n", "        'partially_translated': 0,\n", "        'no_translations': 0,\n", "        'document_analyses': [],\n", "        'summary': {}\n", "    }\n", "    \n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 MOROCCO TRANSLATION VERIFICATION\")\n", "    print(\"=\"*60)\n", "    print(f\"Target Documents: {len(TARGET_DOCUMENT_IDS)}\")\n", "    print(f\"Solr Core: {CORE_NAME}\")\n", "    print(\"=\"*60)\n", "    \n", "    # Process each document\n", "    for i, doc_id in enumerate(TARGET_DOCUMENT_IDS, 1):\n", "        print(f\"\\n🔍 [{i}/{len(TARGET_DOCUMENT_IDS)}] Checking: {doc_id}\")\n", "        \n", "        # Query the document\n", "        doc = query_document_by_id(doc_id)\n", "        \n", "        if doc:\n", "            verification_results['documents_found'] += 1\n", "            \n", "            # Analyze the document\n", "            analysis = analyze_document(doc)\n", "            verification_results['document_analyses'].append(analysis)\n", "            \n", "            # Update counters\n", "            if analysis['translation_status'] == 'fully_translated':\n", "                verification_results['fully_translated'] += 1\n", "            elif analysis['translation_status'] == 'partially_translated':\n", "                verification_results['partially_translated'] += 1\n", "            else:\n", "                verification_results['no_translations'] += 1\n", "            \n", "            # Print summary\n", "            print_document_summary(analysis)\n", "            \n", "        else:\n", "            verification_results['documents_not_found'] += 1\n", "            print(f\"❌ Document not found: {doc_id}\")\n", "    \n", "    # Generate summary\n", "    verification_results['summary'] = {\n", "        'success_rate': (verification_results['documents_found'] / verification_results['total_documents']) * 100,\n", "        'translation_rate': (verification_results['fully_translated'] / verification_results['documents_found']) * 100 if verification_results['documents_found'] > 0 else 0,\n", "        'status': 'success' if verification_results['fully_translated'] == verification_results['documents_found'] else 'partial' if verification_results['fully_translated'] > 0 else 'failed'\n", "    }\n", "    \n", "    # Print final summary\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"📊 VERIFICATION SUMMARY\")\n", "    print(\"=\"*60)\n", "    print(f\"Total Documents Checked: {verification_results['total_documents']}\")\n", "    print(f\"Documents Found: {verification_results['documents_found']} ({verification_results['summary']['success_rate']:.1f}%)\")\n", "    print(f\"Documents Not Found: {verification_results['documents_not_found']}\")\n", "    print(\"\\nTranslation Status:\")\n", "    print(f\"  ✅ Fully Translated: {verification_results['fully_translated']}\")\n", "    print(f\"  ⚠️  Partially Translated: {verification_results['partially_translated']}\")\n", "    print(f\"  ❌ No Translations: {verification_results['no_translations']}\")\n", "    print(f\"\\nTranslation Success Rate: {verification_results['summary']['translation_rate']:.1f}%\")\n", "    print(f\"Overall Status: {verification_results['summary']['status'].upper()}\")\n", "    \n", "    # Save results\n", "    save_verification_results(verification_results)\n", "    \n", "    return verification_results\n", "\n", "def save_verification_results(results):\n", "    \"\"\"\n", "    Save verification results to a file.\n", "    \n", "    Args:\n", "        results (dict): Verification results\n", "    \"\"\"\n", "    try:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        filename = f\"morocco_verification_results_{timestamp}.json\"\n", "        filepath = os.path.join(RESULTS_DIR, filename)\n", "        \n", "        with open(filepath, 'w', encoding='utf-8') as f:\n", "            json.dump(results, f, ensure_ascii=False, indent=2)\n", "        \n", "        logger.info(f\"📋 Verification results saved to {filepath}\")\n", "        print(f\"\\n📁 Detailed results saved to: {filepath}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"❌ Error saving verification results: {e}\")\n", "\n", "# Run the verification\n", "if __name__ == \"__main__\":\n", "    verification_results = run_verification()\n", "    \n", "    # Additional analysis\n", "    if verification_results['document_analyses']:\n", "        print(\"\\n\" + \"=\"*60)\n", "        print(\"🔬 DETAILED FIELD ANALYSIS\")\n", "        print(\"=\"*60)\n", "        \n", "        # Analyze each field across all documents\n", "        for source_field, target_field in SOURCE_FIELDS.items():\n", "            source_count = sum(1 for analysis in verification_results['document_analyses'] \n", "                             if analysis['fields'][source_field]['has_source'])\n", "            target_count = sum(1 for analysis in verification_results['document_analyses'] \n", "                             if analysis['fields'][source_field]['has_target'])\n", "            \n", "            print(f\"\\n📝 {source_field} → {target_field}:\")\n", "            print(f\"   Source documents: {source_count}/{len(verification_results['document_analyses'])}\")\n", "            print(f\"   Translated documents: {target_count}/{len(verification_results['document_analyses'])}\")\n", "            print(f\"   Translation rate: {(target_count/source_count)*100 if source_count > 0 else 0:.1f}%\")\n", "    \n", "    print(\"\\n✅ Verification completed!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}