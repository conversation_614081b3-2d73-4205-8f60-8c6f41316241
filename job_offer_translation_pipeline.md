# Job Offer Translation Pipeline

This document provides a comprehensive overview of the job offer translation pipeline, explaining how data flows through the system, the key components, and how they interact.

## Table of Contents

1. [Pipeline Overview](#pipeline-overview)
2. [Data Flow Diagram](#data-flow-diagram)
3. [Key Components](#key-components)
4. [Solr Integration](#solr-integration)
5. [OpenAI Batch API Integration](#openai-batch-api-integration)
6. [Data Storage](#data-storage)
7. [<PERSON>rror Handling](#error-handling)
8. [Optimization Strategies](#optimization-strategies)
9. [Testing and Debugging](#testing-and-debugging)

## Pipeline Overview

The job offer translation pipeline is designed to:

1. Retrieve job offers from Solr databases for different countries
2. Identify which job offers need translation
3. Prepare and send translation requests to OpenAI's Batch API
4. Process the translation results
5. Store the translations in JSON format
6. Update the Solr database with the translated fields

The pipeline is implemented as a Jupyter notebook (`JOB_OFFER_TRANSLATION_JSON_FORMAT.ipynb`) that can be run on demand to process batches of job offers.

## Data Flow Diagram

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Solr DB    │────▶│ Job Offers  │────▶│  OpenAI     │────▶│ Translation │
│ (Country)   │     │ Retrieval   │     │  Batch API  │     │  Results    │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                           │                                       │
                           │                                       │
                           ▼                                       ▼
                    ┌─────────────┐                        ┌─────────────┐
                    │  Filtering  │                        │   JSON      │
                    │  & Prep     │                        │  Storage    │
                    └─────────────┘                        └─────────────┘
                                                                  │
                                                                  │
                                                                  ▼
                                                           ┌─────────────┐
                                                           │  Solr DB    │
                                                           │  Update     │
                                                           └─────────────┘
```

## Key Components

### 1. `run_batch_translation` Function

This is the main orchestration function that coordinates the entire pipeline. It:

- Takes a country code, batch size, and update_solr flag as inputs
- Calls each component function in sequence
- Handles errors and returns success/failure status

```python
def run_batch_translation(country, batch_size=BATCH_SIZE, update_solr=False):
    # Step 1: Get job offers that need translation
    job_offers = get_job_offers(country, num_offers=batch_size, start=0, skip_translated=True)

    # Step 2: Prepare batch file
    batch_file_path = prepare_batch_file(job_offers, country)

    # Step 3: Process batch
    output_file_path = process_batch(batch_file_path, client)

    # Step 4: Process batch results
    translated_offers = process_batch_results(output_file_path, job_offers)

    # Step 5: Save translations to JSON
    results_path = save_translations_to_json(translated_offers, country)

    # Step 6: Update Solr (if requested)
    if update_solr:
        result = update_solr_with_translations(translated_offers, country, batch_size=batch_size)
```

### 2. `get_job_offers` Function

Retrieves job offers from the Solr database and filters out those that already have translations:

- Connects to the appropriate Solr core for the country
- Constructs a query with the exact URL format required by Solr
- Retrieves job offers with pagination support
- Validates each job offer to ensure it has the required fields
- Filters out offers that already have translations (if skip_translated=True)

### 3. `prepare_batch_file` Function

Prepares a batch file for the OpenAI Batch API:

- Creates a unique batch file name with timestamp
- Processes each job offer and extracts the fields to translate
- Handles HTML entity decoding
- Creates batch requests in the format required by OpenAI
- Writes the batch requests to a JSONL file

### 4. `process_batch` Function

Sends the batch file to OpenAI's Batch API and retrieves the results:

- Uploads the batch file to OpenAI
- Creates a batch job with the appropriate parameters
- Monitors the batch job status until completion
- Downloads the output file when available
- Handles various error conditions and edge cases

### 5. `process_batch_results` Function

Processes the batch results from OpenAI:

- Parses the output file from OpenAI
- Extracts the translated text from each response
- Maps the translations back to the original job offers
- Returns a list of job offers with translated fields

### 6. `save_translations_to_json` Function

Saves the translated job offers to a JSON file:

- Creates a filename with timestamp
- Writes the translated offers to a JSON file with proper encoding
- Returns the path to the saved file

### 7. `update_solr_with_translations` Function

Updates the Solr database with the translated fields:

- Connects to the appropriate Solr core for the country
- Validates each job offer before updating
- Creates update documents with only the translated fields
- Processes updates in batches for efficiency
- Uses atomic updates to modify only the translated fields
- Implements batch commits for better performance

## Solr Integration

### Connection Management

The pipeline uses a connection pool to reuse Solr connections:

```python
# Create a connection pool for reusing Solr connections
solr_connections = {}

def get_jobsearch_connection(country, timeout=30):
    # Check if we already have a connection for this country
    if country in solr_connections:
        return solr_connections[country]

    # Create a new connection
    solr_url = f"{SOLR_BASE_URL}core_jobsearch_{country}"
    solr = pysolr.Solr(solr_url, timeout=timeout)

    # Store in connection pool
    solr_connections[country] = solr
    return solr
```

### Query Format

The pipeline uses a specific query format that has been tested and verified to work:

```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq=*:*&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows={batch_size}&start={start}&sort=entity_id+asc&wt=json
```

This query format is implemented in the code as:

```python
params = {
    'q': '*:*',     # Main query
    'fq': '*:*',    # Filter query
    'fl': ','.join(fields),  # Fields to return
    'rows': num_offers,      # Number of rows to return
    'start': start,          # Starting offset
    'sort': 'entity_id asc', # Sort by entity_id ascending
    'wt': 'json'             # Response format
}
```

### Document Identification

The pipeline uses the full "id" field (e.g., "c8xu1x/node/160086") for document identification during updates, not just the entity_id. This ensures that the correct document is updated in Solr.

### Batch Updates

The pipeline implements batch processing for Solr updates to improve performance:

```python
# Update documents in batch using atomic updates
commit_within = 5000  # 5 seconds
solr.add(update_batch, fieldUpdates={'set': target_fields}, commitWithin=commit_within)
```

## OpenAI Batch API Integration

### Batch Request Format

The pipeline creates batch requests in the format required by OpenAI's Batch API:

```python
batch_request = {
    "custom_id": custom_id,
    "method": "POST",
    "url": "/v1/chat/completions",
    "body": {
        "model": OPENAI_MODEL,
        "messages": [
            {"role": "system", "content": "You are a professional translator..."},
            {"role": "user", "content": f"Translate the following French job offer text to English and return as JSON:\n\n{decoded_text}"}
        ],
        "response_format": {"type": "json_object"},
        "temperature": 0.3
    }
}
```

### JSON Output Format

The pipeline uses the JSON output format for translations, which requires:

1. Setting `"response_format": {"type": "json_object"}` in the request
2. Including the word "json" in the messages
3. Using the gpt-4.1-nano-2025-04-14 model

### Batch Processing Mode

The pipeline uses OpenAI's batch processing mode, which allows for:

1. Asynchronous processing of multiple translation requests
2. Better handling of rate limits
3. More efficient use of the API
4. Reduced costs for large-scale translation tasks

## Data Storage

### Batch Files

Batch files are stored in the `batch_files` directory with a naming convention that includes the country code and timestamp:

```
{country}_batch_{timestamp}.jsonl
```

### Translation Results

Translation results are stored in the `translation_results` directory with a naming convention that includes the country code and timestamp:

```
{country}_translated_offers_{timestamp}.json
```

## Error Handling

The pipeline implements comprehensive error handling at multiple levels:

1. **Function-level error handling**: Each function has try-except blocks to catch and log errors
2. **Validation**: Job offers are validated before processing to ensure they have the required fields
3. **Batch error handling**: Errors in batch processing are logged and tracked
4. **Solr update error handling**: Failed Solr updates are tracked and reported

## Optimization Strategies

The pipeline implements several optimization strategies:

1. **Connection pooling**: Reuses Solr connections to reduce overhead
2. **Batch processing**: Processes job offers in batches for efficiency
3. **Skip translated**: Skips job offers that already have translations
4. **Batch commits**: Uses batch commits for Solr updates
5. **commitWithin parameter**: Optimizes Solr commits using the commitWithin parameter

## Testing and Debugging

The pipeline includes several testing and debugging features:

1. **Direct HTTP query function**: Provides a way to test Solr queries directly
2. **URL logging**: Logs the full Solr query URL for debugging
3. **Batch file inspection**: Saves batch files for inspection
4. **Result examination**: Includes a function to examine translation results

## Pipeline Evolution

The job offer translation pipeline has evolved through several iterations to address various challenges:

### Initial Implementation Challenges

1. **Solr Connectivity**: Initially, there were challenges connecting to the Solr database. The solution was to use the correct core pattern (`core_jobsearch_{country}` instead of `core_cvsearch_{country}`).

2. **Field Structure**: Understanding that job offer fields (`sm_field_offre_description_poste` and `sm_field_offre_profil`) are stored as lists of strings with HTML entities.

3. **OpenAI API Format**: The initial implementation struggled with the OpenAI Batch API format. The key insight was that the 'body' field must be an object, not a string.

### Improvements Over Time

1. **Batch Processing**: The pipeline evolved from processing one job offer at a time to batch processing for better efficiency.

2. **Skip Translated**: Added the ability to skip job offers that already have translations to avoid redundant processing.

3. **Validation**: Added comprehensive validation to ensure job offers have the required fields and proper ID format.

4. **Error Handling**: Enhanced error handling to better track and report issues.

5. **Performance Optimization**: Implemented connection pooling, batch commits, and other optimizations.

### Current State

The current implementation represents a robust, efficient pipeline that:

1. Correctly connects to Solr using the exact URL format required
2. Properly identifies job offers that need translation
3. Efficiently processes translations using OpenAI's Batch API
4. Reliably updates Solr with the translated fields
5. Provides comprehensive logging and error handling

## How to Use the Pipeline

### Basic Usage

1. **Set up the environment**:
   - Ensure you have access to the Solr database
   - Set up an OpenAI API key

2. **Run the notebook**:
   - Open the `JOB_OFFER_TRANSLATION_JSON_FORMAT.ipynb` notebook
   - Run the cells in sequence
   - When prompted, enter your OpenAI API key
   - Select the country to process
   - Enter the batch size
   - Choose whether to update Solr with translations

3. **Examine the results**:
   - Check the logs for any errors or warnings
   - Examine the translation results using the provided function
   - Verify the Solr updates if requested

### Advanced Usage

1. **Custom batch sizes**: Adjust the batch size based on your needs and API rate limits.

2. **Entity ID filtering**: Use the `get_job_offers_by_entity_ids` function to process specific job offers.

3. **Direct Solr queries**: Use the `query_solr_direct` function to test Solr queries directly.

4. **Custom translation prompts**: Modify the system and user messages in the `prepare_batch_file` function to customize the translation style.

## Future Enhancements

Potential future enhancements to the pipeline include:

1. **Parallel processing**: Implement parallel processing to handle multiple countries simultaneously.

2. **Incremental updates**: Implement a mechanism to track which job offers have been processed to enable incremental updates.

3. **Quality assessment**: Add a step to assess the quality of translations and flag potential issues.

4. **User interface**: Develop a user interface to make it easier to run and monitor the pipeline.

5. **Scheduled execution**: Set up scheduled execution to automatically process new job offers.

## Conclusion

The job offer translation pipeline provides a robust, efficient solution for translating job offers from French to English. It integrates with Solr for data retrieval and storage, and uses OpenAI's Batch API for high-quality translations. The pipeline includes comprehensive error handling, logging, and optimization strategies to ensure reliable operation.
