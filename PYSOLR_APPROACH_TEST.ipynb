{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PySOLR Approach Test - Alternative Update Method\n", "\n", "Testing the PySOLR library approach instead of direct HTTP requests.\n", "This method might bypass schema issues by handling requests differently.\n", "\n", "## The Approach\n", "```python\n", "def index_translations(en_translation, country):\n", "    solr = pysolr.Solr(f\"http://************:8983/solr/core_cvsearch_{country}\")\n", "    node_id = en_translation['id']\n", "   \n", "    for key, value in en_translation.items():\n", "        if key.startswith('tr_'):\n", "            solr.add({'id':node_id, key: {'set': value}})\n", "    solr.commit()\n", "    return str(node_id)+ \"DONE\"\n", "```\n", "\n", "## Key Differences\n", "- Uses PySOLR library instead of direct HTTP\n", "- Individual field updates instead of bulk document update\n", "- Different request formatting and error handling\n", "- May handle schema issues more gracefully\n", "\n", "## Test Strategy\n", "1. Test PySOLR connectivity\n", "2. Try the exact approach with our test document\n", "3. Compare with direct HTTP approach\n", "4. Verify results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pysolr\n", "import json\n", "import requests\n", "import logging\n", "import time\n", "from datetime import datetime\n", "import os\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('pysolr_test')\n", "\n", "# Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "COUNTRY = \"maroc\"\n", "CORE_NAME = f\"core_jobsearch_{COUNTRY}\"  # Note: using jobsearch, not cvsearch\n", "SOLR_CORE_URL = f\"{SOLR_BASE_URL}{CORE_NAME}\"\n", "\n", "# Test document\n", "TEST_DOC_ID = \"c8xu1x/node/157221\"\n", "\n", "# Create results directory\n", "RESULTS_DIR = \"pysolr_test_results\"\n", "os.makedirs(RESULTS_DIR, exist_ok=True)\n", "\n", "print(\"🔧 PySOLR Approach Test Initialized\")\n", "print(f\"Core: {CORE_NAME}\")\n", "print(f\"Core URL: {SOLR_CORE_URL}\")\n", "print(f\"Test document: {TEST_DOC_ID}\")\n", "print(f\"Results directory: {RESULTS_DIR}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Test PySOLR Connectivity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_pysolr_connectivity():\n", "    \"\"\"\n", "    Test PySOLR connectivity and basic operations.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 STEP 1: TESTING PYSOLR CONNECTIVITY\")\n", "    print(\"=\"*60)\n", "    \n", "    connectivity_results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'pysolr_connection': <PERSON><PERSON><PERSON>,\n", "        'basic_query': <PERSON><PERSON><PERSON>,\n", "        'test_document_found': <PERSON><PERSON><PERSON>,\n", "        'error': None\n", "    }\n", "    \n", "    try:\n", "        # Create PySOLR connection\n", "        print(f\"🔗 Creating PySOLR connection to: {SOLR_CORE_URL}\")\n", "        solr = pysolr.Solr(SOLR_CORE_URL)\n", "        \n", "        # Test basic query\n", "        print(\"🔍 Testing basic query...\")\n", "        results = solr.search('*:*', rows=1)\n", "        doc_count = results.hits\n", "        \n", "        print(f\"✅ PySOLR connection successful\")\n", "        print(f\"✅ Basic query successful: {doc_count} documents found\")\n", "        \n", "        connectivity_results['pysolr_connection'] = True\n", "        connectivity_results['basic_query'] = True\n", "        connectivity_results['document_count'] = doc_count\n", "        \n", "        # Test specific document query\n", "        print(f\"\\n📄 Testing specific document query: {TEST_DOC_ID}\")\n", "        doc_results = solr.search(f'id:\"{TEST_DOC_ID}\"')\n", "        \n", "        if doc_results.hits > 0:\n", "            doc = doc_results.docs[0]\n", "            print(f\"✅ Test document found: {doc.get('id')}\")\n", "            print(f\"📋 Document fields: {list(doc.keys())}\")\n", "            \n", "            # Check for source fields\n", "            has_desc = 'sm_field_offre_description_poste' in doc\n", "            has_profil = 'sm_field_offre_profil' in doc\n", "            print(f\"📝 Has description: {'✅' if has_desc else '❌'}\")\n", "            print(f\"📝 Has profil: {'✅' if has_profil else '❌'}\")\n", "            \n", "            # Check for existing translation fields\n", "            has_tr_desc = 'tr_field_offre_description_poste' in doc\n", "            has_tr_profil = 'tr_field_offre_profil' in doc\n", "            print(f\"🔄 Has translated description: {'✅' if has_tr_desc else '❌'}\")\n", "            print(f\"🔄 Has translated profil: {'✅' if has_tr_profil else '❌'}\")\n", "            \n", "            connectivity_results['test_document_found'] = True\n", "            connectivity_results['test_document'] = doc\n", "        else:\n", "            print(f\"❌ Test document not found: {TEST_DOC_ID}\")\n", "            connectivity_results['test_document_found'] = False\n", "        \n", "        return solr, connectivity_results\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ PySOLR connectivity error: {e}\")\n", "        connectivity_results['error'] = str(e)\n", "        return None, connectivity_results\n", "\n", "# Test PySOLR connectivity\n", "solr_connection, connectivity_results = test_pysolr_connectivity()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Test the Exact PySOLR Approach"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_pysolr_translation_approach(solr):\n", "    \"\"\"\n", "    Test the exact PySOLR approach from the provided function.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🧪 STEP 2: TESTING PYSOLR TRANSLATION APPROACH\")\n", "    print(\"=\"*60)\n", "    \n", "    if not solr:\n", "        print(\"❌ No PySOLR connection available\")\n", "        return {'success': False, 'error': 'No connection'}\n", "    \n", "    test_results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'approach': 'PySOLR individual field updates',\n", "        'success': <PERSON><PERSON><PERSON>,\n", "        'updates_attempted': [],\n", "        'errors': []\n", "    }\n", "    \n", "    # Create test translation data\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    test_translation = {\n", "        'id': TEST_DOC_ID,\n", "        'tr_field_offre_description_poste': [f\"[PYSOLR TEST DESC] {timestamp}\"],\n", "        'tr_field_offre_profil': [f\"[PYSOLR TEST PROFIL] {timestamp}\"]\n", "    }\n", "    \n", "    print(f\"🎯 Test translation data: {json.dumps(test_translation, indent=2)}\")\n", "    \n", "    # Implement the exact approach from the function\n", "    def index_translations_test(en_translation, solr_connection):\n", "        \"\"\"\n", "        Exact implementation of the provided function for testing.\n", "        \"\"\"\n", "        node_id = en_translation['id']\n", "        print(f\"\\n📝 Processing document: {node_id}\")\n", "        \n", "        update_count = 0\n", "        for key, value in en_translation.items():\n", "            if key.startswith('tr_'):\n", "                print(f\"\\n🔄 Updating field: {key}\")\n", "                print(f\"📄 Value: {value}\")\n", "                \n", "                try:\n", "                    # This is the exact approach from the function\n", "                    update_doc = {'id': node_id, key: {'set': value}}\n", "                    print(f\"📋 Update document: {json.dumps(update_doc, indent=2)}\")\n", "                    \n", "                    # Add the update\n", "                    solr_connection.add([update_doc])\n", "                    print(f\"✅ Field {key} update sent successfully\")\n", "                    \n", "                    test_results['updates_attempted'].append({\n", "                        'field': key,\n", "                        'value': value,\n", "                        'success': True\n", "                    })\n", "                    update_count += 1\n", "                    \n", "                except Exception as e:\n", "                    error_msg = f\"Error updating {key}: {str(e)}\"\n", "                    print(f\"❌ {error_msg}\")\n", "                    test_results['errors'].append(error_msg)\n", "                    test_results['updates_attempted'].append({\n", "                        'field': key,\n", "                        'value': value,\n", "                        'success': <PERSON><PERSON><PERSON>,\n", "                        'error': str(e)\n", "                    })\n", "        \n", "        # Commit the changes\n", "        try:\n", "            print(f\"\\n💾 Committing {update_count} updates...\")\n", "            solr_connection.commit()\n", "            print(f\"✅ Commit successful\")\n", "            return f\"{node_id} DONE\"\n", "        except Exception as e:\n", "            error_msg = f\"Commit error: {str(e)}\"\n", "            print(f\"❌ {error_msg}\")\n", "            test_results['errors'].append(error_msg)\n", "            return f\"{node_id} COMMIT_FAILED\"\n", "    \n", "    # Run the test\n", "    try:\n", "        print(f\"\\n🚀 Running PySOLR translation approach...\")\n", "        result = index_translations_test(test_translation, solr)\n", "        print(f\"📋 Function result: {result}\")\n", "        \n", "        if \"DONE\" in result:\n", "            print(f\"✅ PySOLR approach completed successfully\")\n", "            test_results['success'] = True\n", "        else:\n", "            print(f\"⚠️ PySOLR approach completed with issues\")\n", "            \n", "    except Exception as e:\n", "        error_msg = f\"Overall error: {str(e)}\"\n", "        print(f\"❌ {error_msg}\")\n", "        test_results['errors'].append(error_msg)\n", "    \n", "    return test_results\n", "\n", "# Test the PySOLR approach\n", "if solr_connection:\n", "    pysolr_results = test_pysolr_translation_approach(solr_connection)\nelse:\n", "    print(\"❌ Skipping PySOLR approach test - no connection\")\n", "    pysolr_results = {'success': False, 'error': 'No connection'}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Verify Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def verify_pysolr_results(solr):\n", "    \"\"\"\n", "    Verify if the PySOLR approach actually updated the document.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 STEP 3: VERIFYING PYSOLR RESULTS\")\n", "    print(\"=\"*60)\n", "    \n", "    if not solr:\n", "        print(\"❌ No PySOLR connection for verification\")\n", "        return {'verified': False, 'error': 'No connection'}\n", "    \n", "    verification_results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'verified': <PERSON><PERSON><PERSON>,\n", "        'fields_found': {},\n", "        'document_state': None\n", "    }\n", "    \n", "    try:\n", "        print(f\"🔍 Querying document after PySOLR updates: {TEST_DOC_ID}\")\n", "        \n", "        # Wait a moment for updates to be processed\n", "        print(\"⏳ Waiting for updates to be processed...\")\n", "        time.sleep(3)\n", "        \n", "        # Query the document with translation fields\n", "        results = solr.search(\n", "            f'id:\"{TEST_DOC_ID}\"',\n", "            fl='id,tr_field_offre_description_poste,tr_field_offre_profil,sm_field_offre_description_poste,sm_field_offre_profil'\n", "        )\n", "        \n", "        if results.hits > 0:\n", "            doc = results.docs[0]\n", "            print(f\"✅ Document found: {doc.get('id')}\")\n", "            print(f\"📋 Document after PySOLR updates: {json.dumps(doc, indent=2)}\")\n", "            \n", "            verification_results['document_state'] = doc\n", "            \n", "            # Check for translation fields\n", "            tr_desc_found = 'tr_field_offre_description_poste' in doc\n", "            tr_profil_found = 'tr_field_offre_profil' in doc\n", "            \n", "            print(f\"\\n📊 Translation Field Verification:\")\n", "            print(f\"  tr_field_offre_description_poste: {'✅ FOUND' if tr_desc_found else '❌ MISSING'}\")\n", "            print(f\"  tr_field_offre_profil: {'✅ FOUND' if tr_profil_found else '❌ MISSING'}\")\n", "            \n", "            if tr_desc_found:\n", "                desc_content = doc['tr_field_offre_description_poste']\n", "                print(f\"  📄 Description content: {desc_content}\")\n", "                verification_results['fields_found']['tr_field_offre_description_poste'] = desc_content\n", "            \n", "            if tr_profil_found:\n", "                profil_content = doc['tr_field_offre_profil']\n", "                print(f\"  📄 Profil content: {profil_content}\")\n", "                verification_results['fields_found']['tr_field_offre_profil'] = profil_content\n", "            \n", "            # Check if our test content is present\n", "            test_desc_found = tr_desc_found and \"PYSOLR TEST DESC\" in str(doc.get('tr_field_offre_description_poste', ''))\n", "            test_profil_found = tr_profil_found and \"PYSOLR TEST PROFIL\" in str(doc.get('tr_field_offre_profil', ''))\n", "            \n", "            print(f\"\\n🧪 Test Content Verification:\")\n", "            print(f\"  Test description found: {'✅ YES' if test_desc_found else '❌ NO'}\")\n", "            print(f\"  Test profil found: {'✅ YES' if test_profil_found else '❌ NO'}\")\n", "            \n", "            if tr_desc_found and tr_profil_found:\n", "                print(f\"\\n🎉 SUCCESS! PySOLR approach worked!\")\n", "                print(f\"✅ Both translation fields are now present in the document\")\n", "                verification_results['verified'] = True\n", "            elif tr_desc_found or tr_profil_found:\n", "                print(f\"\\n⚠️ PARTIAL SUCCESS! Some translation fields were added\")\n", "                verification_results['verified'] = 'partial'\n", "            else:\n", "                print(f\"\\n❌ NO SUCCESS! Translation fields were not added\")\n", "                verification_results['verified'] = False\n", "        else:\n", "            print(f\"❌ Document not found during verification\")\n", "            verification_results['error'] = 'Document not found'\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Verification error: {e}\")\n", "        verification_results['error'] = str(e)\n", "    \n", "    return verification_results\n", "\n", "# Verify the results\n", "if solr_connection and pysolr_results.get('success', False):\n", "    verification_results = verify_pysolr_results(solr_connection)\nelse:\n", "    print(\"❌ Skipping verification - PySOLR approach was not successful\")\n", "    verification_results = {'verified': False, 'error': 'PySOLR approach failed'}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Compare with Direct HTTP Approach"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compare_approaches():\n", "    \"\"\"\n", "    Compare PySOLR approach with direct HTTP approach.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"📊 STEP 4: APPROACH COMPARISON AND FINAL ANALYSIS\")\n", "    print(\"=\"*80)\n", "    \n", "    comparison_results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'pysolr_approach': {\n", "            'success': pysolr_results.get('success', False),\n", "            'verified': verification_results.get('verified', False),\n", "            'errors': pysolr_results.get('errors', []),\n", "            'updates_attempted': len(pysolr_results.get('updates_attempted', []))\n", "        },\n", "        'direct_http_approach': {\n", "            'success': <PERSON><PERSON><PERSON>,  # We know this failed from previous tests\n", "            'verified': <PERSON><PERSON><PERSON>,\n", "            'errors': ['All update syntax approaches failed', 'Fields not found after update'],\n", "            'issue': 'Schema fields missing or HTTP 400 errors'\n", "        }\n", "    }\n", "    \n", "    print(\"\\n📋 APPROACH COMPARISON:\")\n", "    print(\"-\" * 50)\n", "    \n", "    # PySOLR Results\n", "    pysolr_success = pysolr_results.get('success', False)\n", "    pysolr_verified = verification_results.get('verified', False)\n", "    \n", "    print(f\"\\n🔧 PySOLR Approach:\")\n", "    print(f\"  Request Success: {'✅' if pysolr_success else '❌'}\")\n", "    print(f\"  Field Updates: {'✅' if pysolr_verified else '❌'}\")\n", "    print(f\"  Updates Attempted: {len(pysolr_results.get('updates_attempted', []))}\")\n", "    if pysolr_results.get('errors'):\n", "        print(f\"  Errors: {len(pysolr_results['errors'])}\")\n", "        for error in pysolr_results['errors']:\n", "            print(f\"    ❌ {error}\")\n", "    \n", "    # Direct HTTP Results (from previous tests)\n", "    print(f\"\\n🌐 Direct HTTP Approach:\")\n", "    print(f\"  Request Success: ❌ (HTTP 200 but fields not added)\")\n", "    print(f\"  Field Updates: ❌ (All approaches failed)\")\n", "    print(f\"  Updates Attempted: 4 different syntax approaches\")\n", "    print(f\"  Errors: Schema fields missing, HTTP 400 on field creation\")\n", "    \n", "    # Analysis and Recommendations\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 ANALYSIS AND RECOMMENDATIONS\")\n", "    print(\"=\"*60)\n", "    \n", "    if pysolr_verified:\n", "        print(\"\\n🎉 BREAKTHROUGH! PySOLR approach worked!\")\n", "        print(\"\\n✅ Key Findings:\")\n", "        print(\"  • PySOLR library handles schema issues better than direct HTTP\")\n", "        print(\"  • Individual field updates work even when bulk updates fail\")\n", "        print(\"  • Translation fields can be added without explicit schema modification\")\n", "        \n", "        print(\"\\n🚀 IMMEDIATE ACTION PLAN:\")\n", "        print(\"1. ✅ Update your main pipeline to use PySOLR instead of direct HTTP\")\n", "        print(\"2. ✅ Use the exact approach tested here:\")\n", "        print(\"   ```python\")\n", "        print(\"   solr = pysolr.Solr(f'http://************:8983/solr/core_jobsearch_{country}')\")\n", "        print(\"   for key, value in translation.items():\")\n", "        print(\"       if key.startswith('tr_'):\")\n", "        print(\"           solr.add([{'id': node_id, key: {'set': value}}])\")\n", "        print(\"   solr.commit()\")\n", "        print(\"   ```\")\n", "        print(\"3. ✅ Test with Morocco first, then expand to other countries\")\n", "        print(\"4. ✅ Monitor the first few batches to ensure consistency\")\n", "        \n", "        comparison_results['recommendation'] = 'Use PySOLR approach - it works!'\n", "        \n", "    elif pysolr_success and not pysolr_verified:\n", "        print(\"\\n⚠️ MIXED RESULTS: PySOLR requests succeeded but verification unclear\")\n", "        print(\"\\n🔍 This suggests:\")\n", "        print(\"  • PySOLR handles requests better than direct HTTP\")\n", "        print(\"  • But schema issues may still exist\")\n", "        print(\"  • Need to investigate field creation vs. field updates\")\n", "        \n", "        print(\"\\n🔧 NEXT STEPS:\")\n", "        print(\"1. 🔍 Check if fields were created but with different names\")\n", "        print(\"2. 🔍 Verify field types and schema configuration\")\n", "        print(\"3. 🔧 Try PySOLR approach with existing tr_* fields as workaround\")\n", "        \n", "        comparison_results['recommendation'] = 'PySOLR shows promise - investigate further'\n", "        \n", "    else:\n", "        print(\"\\n❌ BOTH APPROACHES FAILED: Fundamental schema issue\")\n", "        print(\"\\n🔍 This confirms:\")\n", "        print(\"  • Schema modification is required\")\n", "        print(\"  • Neither PySOLR nor direct HTTP can bypass missing field definitions\")\n", "        print(\"  • Manual schema.xml modification is necessary\")\n", "        \n", "        print(\"\\n🔧 REQUIRED ACTIONS:\")\n", "        print(\"1. 🛠️ Add translation fields to schema.xml manually\")\n", "        print(\"2. 🔄 Reload Solr core\")\n", "        print(\"3. 🧪 Test either approach after schema fix\")\n", "        \n", "        comparison_results['recommendation'] = 'Manual schema modification required'\n", "    \n", "    # Save comprehensive results\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    final_report = {\n", "        'timestamp': timestamp,\n", "        'connectivity_results': connectivity_results,\n", "        'pysolr_test_results': pysolr_results,\n", "        'verification_results': verification_results,\n", "        'comparison_analysis': comparison_results\n", "    }\n", "    \n", "    report_filename = f\"pysolr_approach_test_report_{timestamp}.json\"\n", "    report_filepath = os.path.join(RESULTS_DIR, report_filename)\n", "    \n", "    with open(report_filepath, 'w', encoding='utf-8') as f:\n", "        json.dump(final_report, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"\\n📁 Comprehensive test report saved to: {report_filepath}\")\n", "    \n", "    return comparison_results\n", "\n", "# Run the comparison\n", "final_analysis = compare_approaches()\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"🎯 PYSOLR APPROACH TEST COMPLETE\")\n", "print(\"=\"*80)\n", "print(f\"\\n💡 Recommendation: {final_analysis.get('recommendation', 'See detailed analysis above')}\")\n", "print(\"\\n🔧 Next steps based on results above.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}