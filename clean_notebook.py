import json
import re
import nbformat
from nbformat.v4 import new_notebook, new_code_cell, new_markdown_cell

def deep_clean_code(text):
    """
    Perform a deep cleaning of code cell text to remove all JSON artifacts.

    Args:
        text (str): The text to clean

    Returns:
        str: Cleaned text
    """
    # First pass: handle the most common patterns
    text = re.sub(r'"\s*,\s*\\n\s*"', '\n', text)
    text = re.sub(r'\\",\s*",$', '', text)
    text = re.sub(r'\\",\s*",', '\n', text)

    # Remove leading and trailing quotes
    text = re.sub(r'^"', '', text)
    text = re.sub(r'"$', '', text)

    # Unescape escaped quotes and backslashes
    text = text.replace('\\"', '"')
    text = text.replace('\\\\', '\\')

    # Fix escape sequences
    text = text.replace('\\n', '\n')
    text = text.replace('\\t', '\t')

    # Second pass: handle any remaining artifacts
    lines = text.split('\n')
    cleaned_lines = []

    for line in lines:
        # Remove trailing "\",", pattern
        line = re.sub(r'\\",\s*"$', '', line)

        # Remove any remaining quotes at the beginning of lines
        line = re.sub(r'^"', '', line)

        # Remove any remaining quotes at the end of lines
        line = re.sub(r'"$', '', line)

        # Fix any remaining escape sequences
        line = line.replace('\\"', '"')

        cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)

def clean_notebook(input_path, output_path):
    """
    Clean a notebook with formatting issues and create a properly formatted version.

    Args:
        input_path (str): Path to the input notebook
        output_path (str): Path to save the cleaned notebook
    """
    print(f"Reading notebook from {input_path}...")

    # Read the notebook
    with open(input_path, 'r', encoding='utf-8') as f:
        notebook_data = json.load(f)

    # Create a new notebook
    new_nb = new_notebook()

    # Process each cell
    for cell in notebook_data['cells']:
        cell_type = cell['cell_type']
        source = cell['source']

        # Clean the source
        if isinstance(source, list):
            # Join the list into a single string
            source_text = ''.join(source)

            # Clean up the text
            if cell_type == 'code':
                # Use our deep cleaning function for code cells
                source_text = deep_clean_code(source_text)

            # Split back into lines
            clean_source = source_text.split('\n')
        else:
            clean_source = source

        # Create a new cell
        if cell_type == 'code':
            new_cell = new_code_cell(source=clean_source)
        elif cell_type == 'markdown':
            new_cell = new_markdown_cell(source=clean_source)
        else:
            # Skip unknown cell types
            continue

        # Add the cell to the new notebook
        new_nb.cells.append(new_cell)

    # Write the cleaned notebook
    print(f"Writing cleaned notebook to {output_path}...")
    with open(output_path, 'w', encoding='utf-8') as f:
        nbformat.write(new_nb, f)

    print("Notebook cleaning completed successfully!")

if __name__ == "__main__":
    input_path = "JOB_OFFER_TRANSLATION_JSON_FORMAT_new.ipynb"
    output_path = "JOB_OFFER_TRANSLATION_JSON_FORMAT_clean.ipynb"
    clean_notebook(input_path, output_path)
