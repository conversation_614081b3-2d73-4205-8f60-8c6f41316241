{"cells": [{"cell_type": "markdown", "id": "entity-id-query-section", "metadata": {}, "source": ["# Job Offer Translation - Entity ID Query\n", "\n", "This notebook demonstrates how to query and translate job offers by specific entity IDs."]}, {"cell_type": "code", "execution_count": null, "id": "imports", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import os\n", "import json\n", "import html\n", "import uuid\n", "import time\n", "import logging\n", "import pysolr\n", "import requests\n", "from datetime import datetime\n", "from openai import OpenAI\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(levelname)s - %(message)s'\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "# Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "LOCAL_SOLR_URL = \"http://localhost:8983/solr/\"\n", "BATCH_SIZE = 2  # Small batch size for testing\n", "COUNTRIES = ['algerie', 'centrafrique', 'benin', 'burkina']\n", "TEST_COUNTRIES = COUNTRIES\n", "OPENAI_MODEL = \"gpt-4.1-nano-2025-04-14\"\n", "\n", "# Create directories if they don't exist\n", "BATCH_DIR = os.path.join(os.getcwd(), \"batch_files\")\n", "RESULTS_DIR = os.path.join(os.getcwd(), \"results\")\n", "os.makedirs(BATCH_DIR, exist_ok=True)\n", "os.makedirs(RESULTS_DIR, exist_ok=True)\n", "\n", "# Fields we're interested in for job offers\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}"]}, {"cell_type": "markdown", "id": "entity-id-query-functions", "metadata": {}, "source": ["## Entity ID Query Functions\n", "\n", "These functions allow you to query job offers by specific entity IDs."]}, {"cell_type": "code", "execution_count": null, "id": "solr-connection-functions", "metadata": {}, "outputs": [], "source": ["# Create a connection pool for reusing Solr connections\n", "solr_connections = {}\n", "\n", "def get_jobsearch_connection(country, timeout=30):\n", "    \"\"\"\n", "    Get a connection to the JobSearch Solr core for the specified country.\n", "    Uses connection pooling to reuse existing connections.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        timeout (int): Connection timeout in seconds\n", "        \n", "    Returns:\n", "        pysolr.Solr: Solr connection\n", "    \"\"\"\n", "    try:\n", "        # Check if we already have a connection for this country\n", "        if country in solr_connections:\n", "            logger.info(f\"Using existing connection for {country}\")\n", "            return solr_connections[country]\n", "        \n", "        # Use the correct core pattern: core_jobsearch_[country]\n", "        solr_url = f\"{SOLR_BASE_URL}core_jobsearch_{country}\"\n", "        logger.info(f\"Creating new connection to JobSearch Solr core at {solr_url}\")\n", "        \n", "        # Create PySOLR connection with timeout\n", "        solr = pysolr.Solr(solr_url, timeout=timeout)\n", "        \n", "        # Store in connection pool\n", "        solr_connections[country] = solr\n", "        return solr\n", "    except Exception as e:\n", "        logger.error(f\"Error connecting to JobSearch Solr for {country}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "id": "entity-id-query-function", "metadata": {}, "outputs": [], "source": ["def get_job_offers_by_entity_ids(country, entity_ids):\n", "    \"\"\"\n", "    Get job offers by a list of entity IDs.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        entity_ids (list): List of entity IDs to filter by\n", "        \n", "    Returns:\n", "        list: List of job offer documents\n", "    \"\"\"\n", "    try:\n", "        solr = get_jobsearch_connection(country)\n", "        if not solr:\n", "            logger.error(f\"Failed to get JobSearch Solr connection for {country}\")\n", "            return []\n", "        \n", "        # Fields to retrieve\n", "        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Construct entity_id filter query\n", "        entity_ids_query = \" OR \".join([f\"entity_id:{eid}\" for eid in entity_ids])\n", "        \n", "        # Construct query parameters\n", "        params = {\n", "            'fl': ','.join(fields),\n", "            'fq': f'({entity_ids_query})',\n", "            'rows': len(entity_ids)\n", "        }\n", "        \n", "        # Execute the query\n", "        logger.info(f\"Retrieving job offers from {country} by entity_ids: {entity_ids}\")\n", "        results = solr.search('*:*', **params)\n", "        \n", "        # Convert to list of dictionaries\n", "        job_offers = [dict(doc) for doc in results]\n", "        \n", "        logger.info(f\"Retrieved {len(job_offers)} job offers by entity_ids\")\n", "        return job_offers\n", "    except Exception as e:\n", "        logger.error(f\"Error retrieving job offers by entity_ids: {e}\")\n", "        return []\n", "\n", "def get_job_offer_by_entity_id(country, entity_id):\n", "    \"\"\"\n", "    Get a job offer by its entity_id.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        entity_id (int): Entity ID to filter by\n", "        \n", "    Returns:\n", "        dict: Job offer document or None if not found\n", "    \"\"\"\n", "    job_offers = get_job_offers_by_entity_ids(country, [entity_id])\n", "    if job_offers:\n", "        return job_offers[0]\n", "    else:\n", "        logger.warning(f\"No job offer found with entity_id {entity_id}\")\n", "        return None"]}, {"cell_type": "markdown", "id": "usage-examples", "metadata": {}, "source": ["## Usage Examples\n", "\n", "Here are examples of how to use the entity ID query functions."]}, {"cell_type": "code", "execution_count": null, "id": "example-usage", "metadata": {}, "outputs": [], "source": ["# Example: Query a single job offer by entity_id\n", "country = 'algerie'  # Change to the country you want to query\n", "entity_id = 123456  # Change to the entity_id you want to query\n", "\n", "job_offer = get_job_offer_by_entity_id(country, entity_id)\n", "if job_offer:\n", "    print(f\"Found job offer with ID: {job_offer['id']}\")\n", "    print(f\"Entity ID: {job_offer['entity_id']}\")\n", "    \n", "    # Print source fields\n", "    for source_field in SOURCE_FIELDS.keys():\n", "        if source_field in job_offer:\n", "            value = job_offer[source_field]\n", "            if isinstance(value, list) and value:\n", "                print(f\"\\n{source_field}: {value[0][:200]}...\")\n", "            elif isinstance(value, str):\n", "                print(f\"\\n{source_field}: {value[:200]}...\")\n", "else:\n", "    print(f\"No job offer found with entity_id {entity_id}\")"]}, {"cell_type": "code", "execution_count": null, "id": "example-multiple", "metadata": {}, "outputs": [], "source": ["# Example: Query multiple job offers by entity_ids\n", "country = 'algerie'  # Change to the country you want to query\n", "entity_ids = [123456, 789012, 345678]  # Change to the entity_ids you want to query\n", "\n", "job_offers = get_job_offers_by_entity_ids(country, entity_ids)\n", "print(f\"Found {len(job_offers)} job offers\")\n", "\n", "for i, offer in enumerate(job_offers):\n", "    print(f\"\\nJob <PERSON> {i+1}:\")\n", "    print(f\"ID: {offer['id']}\")\n", "    print(f\"Entity ID: {offer['entity_id']}\")\n", "    \n", "    # Check if translated fields already exist\n", "    has_translations = any(target_field in offer for target_field in SOURCE_FIELDS.values())\n", "    print(f\"Has translations: {has_translations}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}