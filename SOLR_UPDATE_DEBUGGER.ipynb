{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solr Update Debugger - Advanced Troubleshooting\n", "\n", "This notebook provides sophisticated debugging tools to investigate and fix Solr update issues.\n", "We'll test different update approaches, validate requests, and ensure updates are applied correctly.\n", "\n", "## Problem\n", "The atomic update logic reported success but no translated fields are visible in Solr.\n", "This suggests the updates might be:\n", "1. Going to the wrong core/URL\n", "2. Using incorrect atomic update syntax\n", "3. Being committed but not persisted\n", "4. Being overwritten by other processes\n", "\n", "## Investigation Strategy\n", "1. Test basic Solr connectivity and core access\n", "2. Validate atomic update syntax with simple test\n", "3. Test different update approaches (PySOLR vs direct HTTP)\n", "4. Monitor commit behavior and timing\n", "5. Implement step-by-step verification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import requests\n", "import logging\n", "import time\n", "from datetime import datetime\n", "import os\n", "import pysolr\n", "import urllib.parse\n", "\n", "# Configure detailed logging\n", "logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('solr_debugger')\n", "\n", "# Configuration\n", "SOLR_BASE_URL = \"http://51.195.54.52:8983/solr/\"\n", "COUNTRY = \"maroc\"\n", "CORE_NAME = f\"core_jobsearch_{COUNTRY}\"\n", "SOLR_CORE_URL = f\"{SOLR_BASE_URL}{CORE_NAME}\"\n", "UPDATE_URL = f\"{SOLR_CORE_URL}/update\"\n", "SELECT_URL = f\"{SOLR_CORE_URL}/select\"\n", "\n", "# Test document ID (one of the ones that should have been updated)\n", "TEST_DOC_ID = \"c8xu1x/node/157221\"\n", "\n", "# Create debug results directory\n", "DEBUG_DIR = \"solr_debug_results\"\n", "os.makedirs(DEBUG_DIR, exist_ok=True)\n", "\n", "print(\"🔧 Solr Update Debugger Initialized\")\n", "print(f\"Solr Base URL: {SOLR_BASE_URL}\")\n", "print(f\"Core Name: {CORE_NAME}\")\n", "print(f\"Core URL: {SOLR_CORE_URL}\")\n", "print(f\"Update URL: {UPDATE_URL}\")\n", "print(f\"Test Document: {TEST_DOC_ID}\")\n", "print(f\"Debug Directory: {DEBUG_DIR}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Basic Connectivity and Core Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_solr_connectivity():\n", "    \"\"\"\n", "    Test basic Solr connectivity and core access.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 STEP 1: TESTING SOLR CONNECTIVITY\")\n", "    print(\"=\"*60)\n", "    \n", "    results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'tests': {}\n", "    }\n", "    \n", "    # Test 1: Solr admin cores endpoint\n", "    try:\n", "        admin_url = f\"{SOLR_BASE_URL}admin/cores\"\n", "        print(f\"\\n🔗 Testing Solr admin endpoint: {admin_url}\")\n", "        response = requests.get(admin_url, params={'action': 'STATUS', 'wt': 'json'}, timeout=10)\n", "        \n", "        if response.status_code == 200:\n", "            cores_data = response.json()\n", "            available_cores = list(cores_data.get('status', {}).keys())\n", "            print(f\"✅ Solr admin accessible\")\n", "            print(f\"📋 Available cores: {available_cores}\")\n", "            \n", "            if CORE_NAME in available_cores:\n", "                print(f\"✅ Target core '{CORE_NAME}' found\")\n", "                results['tests']['core_exists'] = True\n", "            else:\n", "                print(f\"❌ Target core '{CORE_NAME}' NOT found\")\n", "                print(f\"💡 Available cores: {available_cores}\")\n", "                results['tests']['core_exists'] = False\n", "        else:\n", "            print(f\"❌ Solr admin not accessible: {response.status_code}\")\n", "            results['tests']['admin_accessible'] = False\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error accessing Solr admin: {e}\")\n", "        results['tests']['admin_error'] = str(e)\n", "    \n", "    # Test 2: Core ping\n", "    try:\n", "        ping_url = f\"{SOLR_CORE_URL}/admin/ping\"\n", "        print(f\"\\n🏓 Testing core ping: {ping_url}\")\n", "        response = requests.get(ping_url, params={'wt': 'json'}, timeout=10)\n", "        \n", "        if response.status_code == 200:\n", "            ping_data = response.json()\n", "            status = ping_data.get('status')\n", "            print(f\"✅ Core ping successful: {status}\")\n", "            results['tests']['core_ping'] = True\n", "        else:\n", "            print(f\"❌ Core ping failed: {response.status_code}\")\n", "            results['tests']['core_ping'] = False\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error pinging core: {e}\")\n", "        results['tests']['ping_error'] = str(e)\n", "    \n", "    # Test 3: Basic query\n", "    try:\n", "        print(f\"\\n🔍 Testing basic query: {SELECT_URL}\")\n", "        response = requests.get(SELECT_URL, params={'q': '*:*', 'rows': 1, 'wt': 'json'}, timeout=10)\n", "        \n", "        if response.status_code == 200:\n", "            query_data = response.json()\n", "            num_found = query_data.get('response', {}).get('numFound', 0)\n", "            print(f\"✅ Basic query successful: {num_found} documents found\")\n", "            results['tests']['basic_query'] = True\n", "            results['tests']['document_count'] = num_found\n", "        else:\n", "            print(f\"❌ Basic query failed: {response.status_code}\")\n", "            results['tests']['basic_query'] = False\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error with basic query: {e}\")\n", "        results['tests']['query_error'] = str(e)\n", "    \n", "    # Test 4: Check test document exists\n", "    try:\n", "        print(f\"\\n📄 Testing test document query: {TEST_DOC_ID}\")\n", "        params = {\n", "            'q': f'id:\"{TEST_DOC_ID}\"',\n", "            'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil,tr_field_offre_description_poste,tr_field_offre_profil',\n", "            'wt': 'json'\n", "        }\n", "        response = requests.get(SELECT_URL, params=params, timeout=10)\n", "        \n", "        if response.status_code == 200:\n", "            doc_data = response.json()\n", "            docs = doc_data.get('response', {}).get('docs', [])\n", "            \n", "            if docs:\n", "                doc = docs[0]\n", "                print(f\"✅ Test document found: {doc.get('id')}\")\n", "                print(f\"📋 Entity ID: {doc.get('entity_id')}\")\n", "                \n", "                # Check for source fields\n", "                has_desc = 'sm_field_offre_description_poste' in doc\n", "                has_profil = 'sm_field_offre_profil' in doc\n", "                print(f\"📝 Has description: {'✅' if has_desc else '❌'}\")\n", "                print(f\"📝 Has profil: {'✅' if has_profil else '❌'}\")\n", "                \n", "                # Check for translated fields\n", "                has_tr_desc = 'tr_field_offre_description_poste' in doc\n", "                has_tr_profil = 'tr_field_offre_profil' in doc\n", "                print(f\"🔄 Has translated description: {'✅' if has_tr_desc else '❌'}\")\n", "                print(f\"🔄 Has translated profil: {'✅' if has_tr_profil else '❌'}\")\n", "                \n", "                results['tests']['test_document'] = {\n", "                    'found': True,\n", "                    'has_source_desc': has_desc,\n", "                    'has_source_profil': has_profil,\n", "                    'has_translated_desc': has_tr_desc,\n", "                    'has_translated_profil': has_tr_profil,\n", "                    'document': doc\n", "                }\n", "            else:\n", "                print(f\"❌ Test document NOT found: {TEST_DOC_ID}\")\n", "                results['tests']['test_document'] = {'found': False}\n", "        else:\n", "            print(f\"❌ Test document query failed: {response.status_code}\")\n", "            results['tests']['test_document'] = {'error': response.status_code}\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error querying test document: {e}\")\n", "        results['tests']['test_document'] = {'error': str(e)}\n", "    \n", "    return results\n", "\n", "# Run connectivity tests\n", "connectivity_results = test_solr_connectivity()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Atomic Update Syntax Testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_atomic_update_syntax():\n", "    \"\"\"\n", "    Test different atomic update syntax approaches.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🧪 STEP 2: TESTING ATOMIC UPDATE SYNTAX\")\n", "    print(\"=\"*60)\n", "    \n", "    # First, let's create a simple test field to avoid affecting real data\n", "    test_field_name = \"test_translation_field\"\n", "    test_value = f\"TEST_UPDATE_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "    \n", "    print(f\"🎯 Testing with document: {TEST_DOC_ID}\")\n", "    print(f\"🎯 Test field: {test_field_name}\")\n", "    print(f\"🎯 Test value: {test_value}\")\n", "    \n", "    # Test different atomic update approaches\n", "    test_approaches = [\n", "        {\n", "            'name': 'Atomic Update with Set (Array)',\n", "            'document': {\n", "                'id': TEST_DOC_ID,\n", "                test_field_name: {'set': [test_value]}\n", "            }\n", "        },\n", "        {\n", "            'name': 'Atomic Update with Set (String)',\n", "            'document': {\n", "                'id': TEST_DOC_ID,\n", "                test_field_name: {'set': test_value}\n", "            }\n", "        },\n", "        {\n", "            'name': 'Direct Field Assignment (Array)',\n", "            'document': {\n", "                'id': TEST_DOC_ID,\n", "                test_field_name: [test_value]\n", "            }\n", "        },\n", "        {\n", "            'name': 'Direct Field Assignment (String)',\n", "            'document': {\n", "                'id': TEST_DOC_ID,\n", "                test_field_name: test_value\n", "            }\n", "        }\n", "    ]\n", "    \n", "    results = []\n", "    \n", "    for i, approach in enumerate(test_approaches, 1):\n", "        print(f\"\\n🧪 [{i}/{len(test_approaches)}] Testing: {approach['name']}\")\n", "        \n", "        try:\n", "            # Prepare the update request\n", "            update_doc = approach['document']\n", "            print(f\"📄 Update document: {json.dumps(update_doc, indent=2)}\")\n", "            \n", "            # Send the update\n", "            headers = {'Content-Type': 'application/json'}\n", "            params = {'commit': 'true', 'wt': 'json'}\n", "            \n", "            print(f\"🚀 Sending update to: {UPDATE_URL}\")\n", "            response = requests.post(\n", "                UPDATE_URL,\n", "                params=params,\n", "                headers=headers,\n", "                data=json.dumps([update_doc]),\n", "                timeout=30\n", "            )\n", "            \n", "            print(f\"📡 Response status: {response.status_code}\")\n", "            print(f\"📡 Response text: {response.text}\")\n", "            \n", "            if response.status_code == 200:\n", "                response_json = response.json()\n", "                solr_status = response_json.get('responseHeader', {}).get('status', -1)\n", "                \n", "                if solr_status == 0:\n", "                    print(f\"✅ Update request successful\")\n", "                    \n", "                    # Wait a moment for the update to be processed\n", "                    time.sleep(2)\n", "                    \n", "                    # Verify the update\n", "                    verify_params = {\n", "                        'q': f'id:\"{TEST_DOC_ID}\"',\n", "                        'fl': f'id,{test_field_name}',\n", "                        'wt': 'json'\n", "                    }\n", "                    \n", "                    verify_response = requests.get(SELECT_URL, params=verify_params, timeout=10)\n", "                    \n", "                    if verify_response.status_code == 200:\n", "                        verify_data = verify_response.json()\n", "                        docs = verify_data.get('response', {}).get('docs', [])\n", "                        \n", "                        if docs and test_field_name in docs[0]:\n", "                            actual_value = docs[0][test_field_name]\n", "                            print(f\"✅ Update verified! Field value: {actual_value}\")\n", "                            \n", "                            results.append({\n", "                                'approach': approach['name'],\n", "                                'success': True,\n", "                                'expected_value': test_value,\n", "                                'actual_value': actual_value,\n", "                                'update_document': update_doc,\n", "                                'response_status': response.status_code,\n", "                                'solr_status': solr_status\n", "                            })\n", "                        else:\n", "                            print(f\"❌ Update not verified - field not found or empty\")\n", "                            results.append({\n", "                                'approach': approach['name'],\n", "                                'success': <PERSON><PERSON><PERSON>,\n", "                                'error': 'Field not found after update',\n", "                                'update_document': update_doc\n", "                            })\n", "                    else:\n", "                        print(f\"❌ Verification query failed: {verify_response.status_code}\")\n", "                        results.append({\n", "                            'approach': approach['name'],\n", "                            'success': <PERSON><PERSON><PERSON>,\n", "                            'error': f'Verification failed: {verify_response.status_code}',\n", "                            'update_document': update_doc\n", "                        })\n", "                else:\n", "                    print(f\"❌ Solr returned error status: {solr_status}\")\n", "                    results.append({\n", "                        'approach': approach['name'],\n", "                        'success': <PERSON><PERSON><PERSON>,\n", "                        'error': f'Solr error status: {solr_status}',\n", "                        'update_document': update_doc\n", "                    })\n", "            else:\n", "                print(f\"❌ HTTP request failed: {response.status_code}\")\n", "                results.append({\n", "                    'approach': approach['name'],\n", "                    'success': <PERSON><PERSON><PERSON>,\n", "                    'error': f'HTTP error: {response.status_code}',\n", "                    'update_document': update_doc\n", "                })\n", "                \n", "        except Exception as e:\n", "            print(f\"❌ Exception during update: {e}\")\n", "            results.append({\n", "                'approach': approach['name'],\n", "                'success': <PERSON><PERSON><PERSON>,\n", "                'error': str(e),\n", "                'update_document': approach['document']\n", "            })\n", "    \n", "    # Summary\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"📊 ATOMIC UPDATE SYNTAX TEST SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    successful_approaches = [r for r in results if r['success']]\n", "    \n", "    if successful_approaches:\n", "        print(f\"✅ {len(successful_approaches)} successful approach(es):\")\n", "        for result in successful_approaches:\n", "            print(f\"  ✅ {result['approach']}\")\n", "            print(f\"     Expected: {result['expected_value']}\")\n", "            print(f\"     Actual: {result['actual_value']}\")\n", "    else:\n", "        print(\"❌ No successful approaches found\")\n", "    \n", "    failed_approaches = [r for r in results if not r['success']]\n", "    if failed_approaches:\n", "        print(f\"\\n❌ {len(failed_approaches)} failed approach(es):\")\n", "        for result in failed_approaches:\n", "            print(f\"  ❌ {result['approach']}: {result['error']}\")\n", "    \n", "    return results\n", "\n", "# Run atomic update syntax tests\n", "syntax_results = test_atomic_update_syntax()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Test Real Translation Field Updates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_real_translation_updates():\n", "    \"\"\"\n", "    Test updating the actual translation fields using the working syntax.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🎯 STEP 3: TESTING REAL TRANSLATION FIELD UPDATES\")\n", "    print(\"=\"*60)\n", "    \n", "    # Use the successful syntax from previous tests\n", "    # We'll try the most likely working approach first\n", "    \n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    test_translation_desc = f\"[DEBUG TEST TRANSLATION DESC] {timestamp}\"\n", "    test_translation_profil = f\"[DEBUG TEST TRANSLATION PROFIL] {timestamp}\"\n", "    \n", "    print(f\"🎯 Testing with document: {TEST_DOC_ID}\")\n", "    print(f\"🎯 Test translation desc: {test_translation_desc}\")\n", "    print(f\"🎯 Test translation profil: {test_translation_profil}\")\n", "    \n", "    # Test the actual translation field updates\n", "    update_approaches = [\n", "        {\n", "            'name': 'Translation Fields - Atomic Set (Array)',\n", "            'document': {\n", "                'id': TEST_DOC_ID,\n", "                'tr_field_offre_description_poste': {'set': [test_translation_desc]},\n", "                'tr_field_offre_profil': {'set': [test_translation_profil]}\n", "            }\n", "        },\n", "        {\n", "            'name': 'Translation Fields - Direct Assignment (Array)',\n", "            'document': {\n", "                'id': TEST_DOC_ID,\n", "                'tr_field_offre_description_poste': [test_translation_desc],\n", "                'tr_field_offre_profil': [test_translation_profil]\n", "            }\n", "        }\n", "    ]\n", "    \n", "    results = []\n", "    \n", "    for i, approach in enumerate(update_approaches, 1):\n", "        print(f\"\\n🎯 [{i}/{len(update_approaches)}] Testing: {approach['name']}\")\n", "        \n", "        try:\n", "            # Get current document state before update\n", "            print(\"📋 Checking document state before update...\")\n", "            pre_params = {\n", "                'q': f'id:\"{TEST_DOC_ID}\"',\n", "                'fl': 'id,tr_field_offre_description_poste,tr_field_offre_profil',\n", "                'wt': 'json'\n", "            }\n", "            \n", "            pre_response = requests.get(SELECT_URL, params=pre_params, timeout=10)\n", "            pre_state = None\n", "            \n", "            if pre_response.status_code == 200:\n", "                pre_data = pre_response.json()\n", "                pre_docs = pre_data.get('response', {}).get('docs', [])\n", "                if pre_docs:\n", "                    pre_state = pre_docs[0]\n", "                    print(f\"📋 Pre-update state: {json.dumps(pre_state, indent=2)}\")\n", "            \n", "            # Prepare and send the update\n", "            update_doc = approach['document']\n", "            print(f\"📄 Update document: {json.dumps(update_doc, indent=2)}\")\n", "            \n", "            headers = {'Content-Type': 'application/json'}\n", "            params = {'commit': 'true', 'wt': 'json'}\n", "            \n", "            print(f\"🚀 Sending update to: {UPDATE_URL}\")\n", "            response = requests.post(\n", "                UPDATE_URL,\n", "                params=params,\n", "                headers=headers,\n", "                data=json.dumps([update_doc]),\n", "                timeout=30\n", "            )\n", "            \n", "            print(f\"📡 Response status: {response.status_code}\")\n", "            print(f\"📡 Response text: {response.text}\")\n", "            \n", "            if response.status_code == 200:\n", "                response_json = response.json()\n", "                solr_status = response_json.get('responseHeader', {}).get('status', -1)\n", "                \n", "                if solr_status == 0:\n", "                    print(f\"✅ Update request successful\")\n", "                    \n", "                    # Wait for the update to be processed\n", "                    print(\"⏳ Waiting for update to be processed...\")\n", "                    time.sleep(3)\n", "                    \n", "                    # Verify the update\n", "                    verify_params = {\n", "                        'q': f'id:\"{TEST_DOC_ID}\"',\n", "                        'fl': 'id,tr_field_offre_description_poste,tr_field_offre_profil',\n", "                        'wt': 'json'\n", "                    }\n", "                    \n", "                    verify_response = requests.get(SELECT_URL, params=verify_params, timeout=10)\n", "                    \n", "                    if verify_response.status_code == 200:\n", "                        verify_data = verify_response.json()\n", "                        docs = verify_data.get('response', {}).get('docs', [])\n", "                        \n", "                        if docs:\n", "                            post_state = docs[0]\n", "                            print(f\"📋 Post-update state: {json.dumps(post_state, indent=2)}\")\n", "                            \n", "                            # Check if the fields were updated\n", "                            desc_updated = 'tr_field_offre_description_poste' in post_state and test_translation_desc in str(post_state['tr_field_offre_description_poste'])\n", "                            profil_updated = 'tr_field_offre_profil' in post_state and test_translation_profil in str(post_state['tr_field_offre_profil'])\n", "                            \n", "                            if desc_updated and profil_updated:\n", "                                print(f\"✅ Both translation fields updated successfully!\")\n", "                                success = True\n", "                            elif desc_updated or profil_updated:\n", "                                print(f\"⚠️ Partial update - desc: {'✅' if desc_updated else '❌'}, profil: {'✅' if profil_updated else '❌'}\")\n", "                                success = False\n", "                            else:\n", "                                print(f\"❌ No translation fields were updated\")\n", "                                success = False\n", "                            \n", "                            results.append({\n", "                                'approach': approach['name'],\n", "                                'success': success,\n", "                                'desc_updated': desc_updated,\n", "                                'profil_updated': profil_updated,\n", "                                'pre_state': pre_state,\n", "                                'post_state': post_state,\n", "                                'update_document': update_doc\n", "                            })\n", "                        else:\n", "                            print(f\"❌ Document not found after update\")\n", "                            results.append({\n", "                                'approach': approach['name'],\n", "                                'success': <PERSON><PERSON><PERSON>,\n", "                                'error': 'Document not found after update'\n", "                            })\n", "                    else:\n", "                        print(f\"❌ Verification query failed: {verify_response.status_code}\")\n", "                        results.append({\n", "                            'approach': approach['name'],\n", "                            'success': <PERSON><PERSON><PERSON>,\n", "                            'error': f'Verification failed: {verify_response.status_code}'\n", "                        })\n", "                else:\n", "                    print(f\"❌ Solr returned error status: {solr_status}\")\n", "                    results.append({\n", "                        'approach': approach['name'],\n", "                        'success': <PERSON><PERSON><PERSON>,\n", "                        'error': f'Solr error status: {solr_status}'\n", "                    })\n", "            else:\n", "                print(f\"❌ HTTP request failed: {response.status_code}\")\n", "                print(f\"❌ Response text: {response.text}\")\n", "                results.append({\n", "                    'approach': approach['name'],\n", "                    'success': <PERSON><PERSON><PERSON>,\n", "                    'error': f'HTTP error: {response.status_code}'\n", "                })\n", "                \n", "        except Exception as e:\n", "            print(f\"❌ Exception during update: {e}\")\n", "            results.append({\n", "                'approach': approach['name'],\n", "                'success': <PERSON><PERSON><PERSON>,\n", "                'error': str(e)\n", "            })\n", "    \n", "    # Summary\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"📊 REAL TRANSLATION UPDATE TEST SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    successful_approaches = [r for r in results if r['success']]\n", "    \n", "    if successful_approaches:\n", "        print(f\"✅ {len(successful_approaches)} successful approach(es):\")\n", "        for result in successful_approaches:\n", "            print(f\"  ✅ {result['approach']}\")\n", "            print(f\"     Description updated: {'✅' if result['desc_updated'] else '❌'}\")\n", "            print(f\"     Profil updated: {'✅' if result['profil_updated'] else '❌'}\")\n", "    else:\n", "        print(\"❌ No successful approaches found\")\n", "        print(\"\\n🔍 This suggests the issue might be:\")\n", "        print(\"   1. Field permissions/schema issues\")\n", "        print(\"   2. Document ID format problems\")\n", "        print(\"   3. Core configuration issues\")\n", "        print(\"   4. Solr version compatibility\")\n", "    \n", "    return results\n", "\n", "# Run real translation field tests\n", "translation_results = test_real_translation_updates()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Generate Comprehensive Debug Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_debug_report():\n", "    \"\"\"\n", "    Generate a comprehensive debug report with all findings.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"📋 COMPREHENSIVE SOLR UPDATE DEBUG REPORT\")\n", "    print(\"=\"*80)\n", "    \n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    \n", "    # Compile all results\n", "    debug_report = {\n", "        'timestamp': timestamp,\n", "        'test_document_id': TEST_DOC_ID,\n", "        'solr_configuration': {\n", "            'base_url': SOLR_BASE_URL,\n", "            'core_name': CORE_NAME,\n", "            'core_url': SOLR_CORE_URL,\n", "            'update_url': UPDATE_URL\n", "        },\n", "        'connectivity_results': connectivity_results,\n", "        'syntax_test_results': syntax_results,\n", "        'translation_test_results': translation_results\n", "    }\n", "    \n", "    # Analyze results and provide recommendations\n", "    print(\"\\n🔍 ANALYSIS AND RECOMMENDATIONS:\")\n", "    print(\"-\" * 50)\n", "    \n", "    # Check connectivity\n", "    if connectivity_results.get('tests', {}).get('core_exists', False):\n", "        print(\"✅ Core connectivity: GOOD\")\n", "    else:\n", "        print(\"❌ Core connectivity: FAILED\")\n", "        print(\"   → Check core name and Solr URL\")\n", "    \n", "    # Check syntax tests\n", "    successful_syntax = [r for r in syntax_results if r['success']]\n", "    if successful_syntax:\n", "        print(f\"✅ Atomic update syntax: WORKING ({len(successful_syntax)} approaches)\")\n", "        print(f\"   → Recommended approach: {successful_syntax[0]['approach']}\")\n", "    else:\n", "        print(\"❌ Atomic update syntax: ALL FAILED\")\n", "        print(\"   → Check Solr version and schema configuration\")\n", "    \n", "    # Check translation tests\n", "    successful_translation = [r for r in translation_results if r['success']]\n", "    if successful_translation:\n", "        print(f\"✅ Translation field updates: WORKING ({len(successful_translation)} approaches)\")\n", "        print(\"   → The original pipeline should work with the correct syntax\")\n", "    else:\n", "        print(\"❌ Translation field updates: ALL FAILED\")\n", "        print(\"   → Check field schema and permissions\")\n", "    \n", "    # Provide specific recommendations\n", "    print(\"\\n💡 SPECIFIC RECOMMENDATIONS:\")\n", "    print(\"-\" * 50)\n", "    \n", "    if successful_translation:\n", "        working_approach = successful_translation[0]\n", "        print(f\"1. ✅ Use this working approach: {working_approach['approach']}\")\n", "        print(f\"   Update document format: {json.dumps(working_approach['update_document'], indent=6)}\")\n", "        print(\"2. ✅ Update the main pipeline to use this exact syntax\")\n", "        print(\"3. ✅ Test with a small batch first\")\n", "    else:\n", "        print(\"1. ❌ Check Solr schema for translation fields:\")\n", "        print(\"   - tr_field_offre_description_poste\")\n", "        print(\"   - tr_field_offre_profil\")\n", "        print(\"2. ❌ Verify field types and permissions\")\n", "        print(\"3. ❌ Check if fields are defined as multivalued\")\n", "        print(\"4. ❌ Consider using PySOLR instead of direct HTTP\")\n", "    \n", "    # Save the report\n", "    report_filename = f\"solr_debug_report_{timestamp}.json\"\n", "    report_filepath = os.path.join(DEBUG_DIR, report_filename)\n", "    \n", "    with open(report_filepath, 'w', encoding='utf-8') as f:\n", "        json.dump(debug_report, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"\\n📁 Detailed report saved to: {report_filepath}\")\n", "    \n", "    return debug_report\n", "\n", "# Generate the final debug report\n", "final_report = generate_debug_report()\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"🎯 DEBUG SESSION COMPLETE\")\n", "print(\"=\"*80)\n", "print(\"\\nNext steps:\")\n", "print(\"1. Review the debug report above\")\n", "print(\"2. Check the saved JSON report for detailed data\")\n", "print(\"3. Apply the working syntax to your main pipeline\")\n", "print(\"4. Test with a small batch before full deployment\")\n", "print(\"\\n🔧 Happy debugging!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}