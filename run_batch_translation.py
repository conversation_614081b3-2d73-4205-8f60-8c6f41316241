"""
Run Batch Translation

This script uses the fixed implementation to run a batch translation job.
It handles the issue with retrieving the output file ID from the OpenAI Batch API.
"""

import json
import logging
import os
import time
import re
import html
from datetime import datetime
import uuid
import argparse

# Import the fixed implementation
from JOB_OFFER_TRANSLATION_FIXED import process_batch

# Try importing external libraries
try:
    import requests
    print("✅ requests library successfully imported")
except ImportError:
    print("❌ requests library not found. Please install it with: pip install requests")
    exit(1)

try:
    import pysolr
    print("✅ pysolr library successfully imported")
except ImportError:
    print("❌ pysolr library not found. Please install it with: pip install pysolr")
    exit(1)

try:
    from openai import OpenAI
    print("✅ openai library successfully imported")
except ImportError:
    print("❌ openai library not found. Please install it with: pip install openai")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('job_offer_translation')

# Configuration
SOLR_BASE_URL = "http://************:8983/solr/"

# Fields we're interested in for job offers
SOURCE_FIELDS = {
    "sm_field_offre_description_poste": "tr_field_offre_description_poste",
    "sm_field_offre_profil": "tr_field_offre_profil"
}

# Countries to work with
COUNTRIES = ['algerie', 'centrafrique', 'benin', 'burkina']

# Directory for storing batch files and results
BATCH_DIR = "batch_files"
RESULTS_DIR = "translation_results"
os.makedirs(BATCH_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)

# OpenAI model
OPENAI_MODEL = "gpt-4.1-nano-2025-04-14"

def get_jobsearch_connection(country):
    """
    Get a connection to the JobSearch Solr core for the specified country.
    
    Args:
        country (str): Country code
        
    Returns:
        pysolr.Solr: Solr connection
    """
    try:
        # Use the correct core pattern: core_jobsearch_[country]
        solr_url = f"{SOLR_BASE_URL}core_jobsearch_{country}"
        logger.info(f"Connecting to JobSearch Solr core at {solr_url}")
        
        # Create PySOLR connection
        solr = pysolr.Solr(solr_url)
        
        return solr
    except Exception as e:
        logger.error(f"Error connecting to JobSearch Solr for {country}: {e}")
        return None

def get_job_offers(country, num_offers=5):
    """
    Get job offers from the JobSearch Solr core.
    
    Args:
        country (str): Country code
        num_offers (int): Number of job offers to retrieve
        
    Returns:
        list: List of job offer documents
    """
    try:
        solr = get_jobsearch_connection(country)
        if not solr:
            logger.error(f"Failed to get JobSearch Solr connection for {country}")
            return []
        
        # Fields to retrieve
        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())
        
        # Execute the query
        logger.info(f"Retrieving {num_offers} job offers from {country}")
        results = solr.search('*:*', **{'fl': ','.join(fields), 'rows': num_offers})
        
        # Convert to list of dictionaries
        job_offers = [dict(doc) for doc in results]
        
        logger.info(f"Retrieved {len(job_offers)} job offers from {country}")
        return job_offers
    except Exception as e:
        logger.error(f"Error retrieving job offers from {country}: {e}")
        return []

def prepare_batch_file(job_offers, country):
    """
    Prepare a batch file for OpenAI Batch API.
    
    Args:
        job_offers (list): List of job offer documents
        country (str): Country code
        
    Returns:
        str: Path to the batch file
    """
    try:
        if not job_offers:
            logger.warning("No job offers to process")
            return None
        
        # Create a unique batch file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        batch_file_path = os.path.join(BATCH_DIR, f"{country}_batch_{timestamp}.jsonl")
        
        # Prepare batch requests
        batch_requests = []
        
        for offer in job_offers:
            offer_id = offer.get('id', str(uuid.uuid4()))
            
            # Process each source field
            for source_field, target_field in SOURCE_FIELDS.items():
                if source_field in offer:
                    value = offer[source_field]
                    
                    # Handle list values
                    if isinstance(value, list):
                        if not value:
                            continue
                        text = value[0]  # Use the first item
                    elif isinstance(value, str):
                        text = value
                    else:
                        continue
                    
                    # Skip empty text
                    if not text or not text.strip():
                        continue
                    
                    # Decode HTML entities
                    decoded_text = html.unescape(text)
                    
                    # Create a unique custom ID for this request
                    custom_id = f"{offer_id}_{source_field}"
                    
                    # Create the batch request
                    batch_request = {
                        "custom_id": custom_id,
                        "method": "POST",
                        "url": "/v1/chat/completions",
                        "body": {
                            "model": OPENAI_MODEL,
                            "messages": [
                                {"role": "system", "content": "You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Preserve all formatting, bullet points, and special characters. Return only the translated text without any additional comments or explanations."},
                                {"role": "user", "content": f"Translate the following French job offer text to English:\n\n{decoded_text}"}
                            ],
                            "response_format": {"type": "json_object"},
                            "max_tokens": 4000,
                            "temperature": 0.3
                        }
                    }
                    
                    batch_requests.append(batch_request)
        
        # Write batch requests to file
        with open(batch_file_path, 'w', encoding='utf-8') as f:
            for request in batch_requests:
                f.write(json.dumps(request) + '\n')
        
        logger.info(f"Created batch file with {len(batch_requests)} requests: {batch_file_path}")
        return batch_file_path
    except Exception as e:
        logger.error(f"Error preparing batch file: {e}")
        return None

def process_batch_results(output_file_path, job_offers):
    """
    Process batch results and update job offers with translations.
    
    Args:
        output_file_path (str): Path to the batch output file
        job_offers (list): Original job offers
        
    Returns:
        list: Updated job offers with translations
    """
    try:
        if not output_file_path or not os.path.exists(output_file_path):
            logger.error(f"Output file not found: {output_file_path}")
            return []
        
        # Create a mapping of job offers by ID for quick lookup
        job_offers_map = {offer['id']: offer for offer in job_offers}
        
        # Create a mapping of custom IDs to offer IDs and fields
        custom_id_map = {}
        for offer in job_offers:
            offer_id = offer['id']
            for source_field, target_field in SOURCE_FIELDS.items():
                custom_id = f"{offer_id}_{source_field}"
                custom_id_map[custom_id] = {
                    'offer_id': offer_id,
                    'source_field': source_field,
                    'target_field': target_field
                }
        
        # Read the output file
        logger.info(f"Processing batch results from: {output_file_path}")
        results = []
        with open(output_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    result = json.loads(line.strip())
                    results.append(result)
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing result line: {e}")
        
        logger.info(f"Processed {len(results)} results from batch output")
        
        # Update job offers with translations
        updated_offers = []
        for result in results:
            custom_id = result.get('custom_id')
            if not custom_id or custom_id not in custom_id_map:
                logger.warning(f"Unknown custom ID: {custom_id}")
                continue
            
            # Check for errors
            if result.get('error'):
                logger.error(f"Error in batch result for {custom_id}: {result['error']}")
                continue
            
            # Get the response
            response = result.get('response')
            if not response or response.get('status_code') != 200:
                logger.error(f"Invalid response for {custom_id}: {response}")
                continue
            
            # Extract the translated text from the response
            body = response.get('body', {})
            choices = body.get('choices', [])
            if not choices:
                logger.error(f"No choices in response for {custom_id}")
                continue
            
            message = choices[0].get('message', {})
            content = message.get('content', '')
            
            # Parse the JSON content if it's in JSON format
            try:
                content_json = json.loads(content)
                if isinstance(content_json, dict) and 'translation' in content_json:
                    content = content_json['translation']
            except (json.JSONDecodeError, TypeError):
                # If it's not valid JSON or doesn't have the expected structure, use the content as is
                pass
            
            # Get the mapping info
            mapping = custom_id_map[custom_id]
            offer_id = mapping['offer_id']
            target_field = mapping['target_field']
            
            # Update the job offer
            if offer_id in job_offers_map:
                offer = job_offers_map[offer_id]
                offer[target_field] = [content]  # Store as list to match original structure
                
                # Add to updated offers if not already added
                if offer not in updated_offers:
                    updated_offers.append(offer)
        
        logger.info(f"Updated {len(updated_offers)} job offers with translations")
        return updated_offers
    except Exception as e:
        logger.error(f"Error processing batch results: {e}")
        return []

def save_translations_to_json(translated_offers, country):
    """
    Save translated job offers to a JSON file.
    
    Args:
        translated_offers (list): List of job offers with translated fields
        country (str): Country code
        
    Returns:
        str: Path to the saved file
    """
    try:
        if not translated_offers:
            logger.warning("No translated offers to save")
            return None
        
        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"{country}_translated_offers_{timestamp}.json"
        json_path = os.path.join(RESULTS_DIR, json_filename)
        
        # Save to JSON
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(translated_offers, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved {len(translated_offers)} translated job offers to {json_path}")
        return json_path
    except Exception as e:
        logger.error(f"Error saving translations to JSON: {e}")
        return None

def run_batch_translation(api_key, country, batch_size=5):
    """
    Run the complete batch translation process for a country.
    
    Args:
        api_key (str): OpenAI API key
        country (str): Country code
        batch_size (int): Number of job offers to process in one batch
        
    Returns:
        tuple: (success, results_path)
    """
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=api_key)
        
        # Step 1: Get job offers
        logger.info(f"Starting batch translation for {country} with batch size {batch_size}")
        job_offers = get_job_offers(country, num_offers=batch_size)
        
        if not job_offers:
            logger.error(f"No job offers found for {country}")
            return False, None
        
        # Step 2: Prepare batch file
        batch_file_path = prepare_batch_file(job_offers, country)
        
        if not batch_file_path:
            logger.error("Failed to prepare batch file")
            return False, None
        
        # Step 3: Process batch using the fixed implementation
        output_file_path = process_batch(client, batch_file_path)
        
        if not output_file_path:
            logger.error("Failed to process batch")
            return False, None
        
        # Step 4: Process batch results
        translated_offers = process_batch_results(output_file_path, job_offers)
        
        if not translated_offers:
            logger.error("Failed to process batch results")
            return False, None
        
        # Step 5: Save translations to JSON
        results_path = save_translations_to_json(translated_offers, country)
        
        if not results_path:
            logger.error("Failed to save translations to JSON")
            return False, None
        
        logger.info(f"Batch translation completed successfully for {country}")
        return True, results_path
    except Exception as e:
        logger.error(f"Error in batch translation process: {e}")
        return False, None

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run batch translation for job offers")
    parser.add_argument("--country", choices=COUNTRIES, help="Country to process")
    parser.add_argument("--batch-size", type=int, default=5, help="Number of job offers to process")
    parser.add_argument("--api-key", help="OpenAI API key")
    args = parser.parse_args()
    
    # Get API key
    api_key = args.api_key
    if not api_key:
        api_key = input("Enter your OpenAI API key: ")
    
    if not api_key:
        print("No API key provided. Exiting.")
        return
    
    # Get country
    country = args.country
    if not country:
        print("Available countries:")
        for i, c in enumerate(COUNTRIES):
            print(f"{i+1}. {c}")
        
        country_index = int(input("\nEnter the number of the country to process (1-4): ")) - 1
        if 0 <= country_index < len(COUNTRIES):
            country = COUNTRIES[country_index]
        else:
            print("Invalid country selection. Exiting.")
            return
    
    # Get batch size
    batch_size = args.batch_size
    if not batch_size:
        batch_size = int(input(f"Enter batch size (default: 5): ") or 5)
    
    # Run batch translation
    print(f"\nRunning batch translation for {country} with batch size {batch_size}...")
    success, results_path = run_batch_translation(api_key, country, batch_size)
    
    if success:
        print(f"\n✅ Batch translation completed successfully!")
        print(f"Results saved to: {results_path}")
    else:
        print(f"\n❌ Batch translation failed. Check the logs for details.")

if __name__ == "__main__":
    main()
