"""
Test Translation Script

This script tests the translation functionality using the sample job offer data
without requiring a running Solr instance.
"""

import json
import logging
import os
import time
from datetime import datetime
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_translation')

# OpenAI API Configuration
# Replace with your actual API key
OPENAI_API_KEY = "your-api-key-here"
client = OpenAI(api_key=OPENAI_API_KEY)

# Fields to translate
SOURCE_FIELDS = {
    "sm_field_offre_description_poste": "tr_field_offre_description_poste",
    "sm_field_offre_profil": "tr_field_offre_profil"
}

# Batch size for OpenAI API calls
BATCH_SIZE = 2

# Directory for storing results
RESULTS_DIR = "translation_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

def translate_batch_with_openai(texts, model="gpt-3.5-turbo"):
    """
    Translate a batch of texts from French to English using OpenAI API.
    
    Args:
        texts (list): List of texts to translate
        model (str): OpenAI model to use
        
    Returns:
        list: List of translated texts
    """
    try:
        # Skip empty batch
        if not texts or all(not text for text in texts):
            return []
        
        # Prepare messages for batch processing
        messages = [
            {"role": "system", "content": "You are a professional translator that translates French job offers to English. Maintain the professional tone and terminology. Only return the translated text without any additional comments or explanations."}
        ]
        
        # Add all texts to translate in one request
        batch_text = "\n---ITEM---\n".join(texts)
        messages.append({"role": "user", "content": f"Translate the following French job offer texts to English. Each text is separated by '---ITEM---'. Return the translations in the same order, also separated by '---ITEM---':\n\n{batch_text}"})
        
        # Make the API call
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.3,  # Lower temperature for more consistent translations
        )
        
        # Extract and split the response
        translated_text = response.choices[0].message.content
        translated_items = translated_text.split("---ITEM---")
        
        # Clean up the results
        cleaned_translations = []
        for item in translated_items:
            item = item.strip()
            if item.startswith("\n"):
                item = item[1:]
            if item:
                cleaned_translations.append(item)
        
        # Ensure we have the same number of translations as input texts
        if len(cleaned_translations) != len(texts):
            logger.warning(f"Number of translations ({len(cleaned_translations)}) doesn't match number of input texts ({len(texts)})")
            # If there's a mismatch, we'll try to align them as best we can
            if len(cleaned_translations) < len(texts):
                # Pad with empty strings if we have fewer translations than inputs
                cleaned_translations.extend(["" for _ in range(len(texts) - len(cleaned_translations))])
            else:
                # Truncate if we have more translations than inputs
                cleaned_translations = cleaned_translations[:len(texts)]
        
        return cleaned_translations
    except Exception as e:
        logger.error(f"Error in translate_batch_with_openai: {e}")
        # Return empty strings for each input text in case of error
        return ["" for _ in range(len(texts))]

def translate_job_offers(job_offers):
    """
    Translate the specified fields in job offers.
    
    Args:
        job_offers (list): List of job offer dictionaries
        
    Returns:
        list: List of job offers with translated fields
    """
    try:
        if not job_offers:
            logger.warning("No job offers to translate")
            return []
        
        translated_offers = []
        
        # Process each source field separately
        for source_field, target_field in SOURCE_FIELDS.items():
            logger.info(f"Translating field {source_field} to {target_field}")
            
            # Collect texts to translate
            texts_to_translate = []
            offer_indices = []
            
            for i, offer in enumerate(job_offers):
                if source_field in offer and offer[source_field]:
                    # Some fields might be lists, take the first item if so
                    text = offer[source_field]
                    if isinstance(text, list):
                        text = text[0]
                    texts_to_translate.append(text)
                    offer_indices.append(i)
            
            logger.info(f"Found {len(texts_to_translate)} texts to translate for field {source_field}")
            
            # Process in batches to avoid API limits
            all_translations = []
            for i in range(0, len(texts_to_translate), BATCH_SIZE):
                batch = texts_to_translate[i:i+BATCH_SIZE]
                logger.info(f"Translating batch {i//BATCH_SIZE + 1}/{(len(texts_to_translate) + BATCH_SIZE - 1)//BATCH_SIZE}")
                translations = translate_batch_with_openai(batch)
                all_translations.extend(translations)
                # Add a small delay to avoid rate limits
                time.sleep(1)
            
            # Add translations to the offers
            for idx, translation in zip(offer_indices, all_translations):
                offer = job_offers[idx]
                
                # Initialize the offer in our results if it's not there yet
                offer_in_results = next((o for o in translated_offers if o['id'] == offer['id']), None)
                if not offer_in_results:
                    # Create a copy of the original offer
                    offer_in_results = offer.copy()
                    translated_offers.append(offer_in_results)
                
                # Add the translation
                offer_in_results[target_field] = translation
        
        logger.info(f"Translated {len(translated_offers)} job offers")
        return translated_offers
    except Exception as e:
        logger.error(f"Error in translate_job_offers: {e}")
        return []

def save_translations_to_json(translated_offers):
    """
    Save translated job offers to a JSON file.
    
    Args:
        translated_offers (list): List of job offers with translated fields
        
    Returns:
        str: Path to the saved file
    """
    try:
        if not translated_offers:
            logger.warning("No translated offers to save")
            return None
        
        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"sample_translated_offers_{timestamp}.json"
        json_path = os.path.join(RESULTS_DIR, json_filename)
        
        # Save to JSON
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(translated_offers, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved {len(translated_offers)} translated job offers to {json_path}")
        return json_path
    except Exception as e:
        logger.error(f"Error saving translations to JSON: {e}")
        return None

def main():
    """
    Main function to test the translation functionality.
    """
    print("JOB OFFER TRANSLATION TEST")
    print("==========================\n")
    
    # Load sample job offers
    print("Loading sample job offers...")
    try:
        with open('sample_job_offers.json', 'r', encoding='utf-8') as f:
            job_offers = json.load(f)
        print(f"Loaded {len(job_offers)} sample job offers\n")
    except Exception as e:
        print(f"Error loading sample job offers: {e}")
        return
    
    # Limit to a few offers for testing
    test_offers = job_offers[:2]
    print(f"Using {len(test_offers)} job offers for testing\n")
    
    # Set your OpenAI API key
    api_key = input("Enter your OpenAI API key (or press Enter to use the one in the script): ")
    if api_key:
        global client
        client = OpenAI(api_key=api_key)
    
    # Translate job offers
    print("\nTranslating job offers...")
    translated_offers = translate_job_offers(test_offers)
    
    if not translated_offers:
        print("Failed to translate job offers.")
        return
    
    print(f"Translated {len(translated_offers)} job offers\n")
    
    # Display sample translations
    print("Sample translations:")
    for offer in translated_offers:
        print(f"\nJob Offer ID: {offer['id']}")
        for source_field, target_field in SOURCE_FIELDS.items():
            if source_field in offer and target_field in offer:
                print(f"\nOriginal ({source_field}):")
                print(offer[source_field][:150] + "..." if len(offer[source_field]) > 150 else offer[source_field])
                print(f"\nTranslated ({target_field}):")
                print(offer[target_field][:150] + "..." if len(offer[target_field]) > 150 else offer[target_field])
    
    # Save translations to JSON
    print("\nSaving translations to JSON...")
    json_path = save_translations_to_json(translated_offers)
    
    if json_path:
        print(f"Saved translations to {json_path}")
    else:
        print("Failed to save translations to JSON.")
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
