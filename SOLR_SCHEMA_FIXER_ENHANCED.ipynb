{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhanced Solr Schema Fixer - Handle Field Addition Errors\n", "\n", "The previous schema fixer identified the problem but failed to add fields due to HTTP 400 errors.\n", "This enhanced version will:\n", "\n", "1. **Investigate available field types**\n", "2. **Try different field type approaches**\n", "3. **Handle permission and compatibility issues**\n", "4. **Provide alternative solutions**\n", "\n", "## Previous Results Analysis\n", "- ✅ Problem identified: Missing translation fields\n", "- ✅ Schema investigation: Found existing tr_* fields (good sign!)\n", "- ❌ Field addition failed: HTTP 400 errors\n", "- ❌ Likely causes: Wrong field type or permissions\n", "\n", "## Strategy\n", "1. Check available field types in this Solr instance\n", "2. Use field types that match existing tr_* fields\n", "3. Try multiple approaches if needed\n", "4. Provide manual schema modification instructions if automatic fails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import requests\n", "import logging\n", "import time\n", "from datetime import datetime\n", "import os\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('enhanced_schema_fixer')\n", "\n", "# Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "COUNTRY = \"maroc\"\n", "CORE_NAME = f\"core_jobsearch_{COUNTRY}\"\n", "SOLR_CORE_URL = f\"{SOLR_BASE_URL}{CORE_NAME}\"\n", "SCHEMA_URL = f\"{SOLR_CORE_URL}/schema\"\n", "UPDATE_URL = f\"{SOLR_CORE_URL}/update\"\n", "SELECT_URL = f\"{SOLR_CORE_URL}/select\"\n", "\n", "# Test document\n", "TEST_DOC_ID = \"c8xu1x/node/157221\"\n", "\n", "# Translation fields we need\n", "TRANSLATION_FIELDS = [\n", "    \"tr_field_offre_description_poste\",\n", "    \"tr_field_offre_profil\"\n", "]\n", "\n", "# Create results directory\n", "RESULTS_DIR = \"enhanced_schema_fix_results\"\n", "os.makedirs(RESULTS_DIR, exist_ok=True)\n", "\n", "print(\"🔧 Enhanced Solr Schema Fixer Initialized\")\n", "print(f\"Core: {CORE_NAME}\")\n", "print(f\"Schema URL: {SCHEMA_URL}\")\n", "print(f\"Translation fields to add: {TRANSLATION_FIELDS}\")\n", "print(f\"Test document: {TEST_DOC_ID}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Investigate Available Field Types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def investigate_field_types():\n", "    \"\"\"\n", "    Investigate available field types in this Solr instance.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 STEP 1: INVESTIGATING AVAILABLE FIELD TYPES\")\n", "    print(\"=\"*60)\n", "    \n", "    field_types_info = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'available_types': {},\n", "        'existing_tr_field_types': {},\n", "        'recommended_type': None\n", "    }\n", "    \n", "    try:\n", "        # Get field types\n", "        print(\"📋 Fetching available field types...\")\n", "        response = requests.get(f\"{SCHEMA_URL}/fieldtypes\", params={'wt': 'json'}, timeout=30)\n", "        \n", "        if response.status_code == 200:\n", "            types_data = response.json()\n", "            field_types = types_data.get('fieldTypes', [])\n", "            \n", "            print(f\"✅ Found {len(field_types)} field types\")\n", "            \n", "            # Store all field types\n", "            for ft in field_types:\n", "                type_name = ft.get('name')\n", "                field_types_info['available_types'][type_name] = ft\n", "                print(f\"  📄 {type_name}: {ft.get('class', 'unknown class')}\")\n", "            \n", "            # Look for text-related types\n", "            text_types = [ft for ft in field_types if 'text' in ft.get('name', '').lower()]\n", "            print(f\"\\n📝 Text-related field types ({len(text_types)}):\")\n", "            for ft in text_types:\n", "                print(f\"  ✅ {ft.get('name')}: {ft.get('class', 'unknown')}\")\n", "            \n", "        else:\n", "            print(f\"❌ Failed to get field types: {response.status_code}\")\n", "            field_types_info['error'] = f'HTTP {response.status_code}'\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error getting field types: {e}\")\n", "        field_types_info['error'] = str(e)\n", "    \n", "    # Analyze existing tr_* fields to understand what types work\n", "    print(\"\\n🔍 Analyzing existing tr_* fields for type patterns...\")\n", "    \n", "    try:\n", "        response = requests.get(f\"{SCHEMA_URL}/fields\", params={'wt': 'json'}, timeout=30)\n", "        \n", "        if response.status_code == 200:\n", "            schema_data = response.json()\n", "            fields = schema_data.get('fields', [])\n", "            \n", "            # Find existing tr_* fields\n", "            tr_fields = [f for f in fields if f['name'].startswith('tr_')]\n", "            print(f\"📋 Found {len(tr_fields)} existing tr_* fields:\")\n", "            \n", "            type_usage = {}\n", "            for field in tr_fields:\n", "                field_name = field['name']\n", "                field_type = field.get('type', 'unknown')\n", "                is_multivalued = field.get('multiValued', False)\n", "                \n", "                print(f\"  📄 {field_name}: type={field_type}, multiValued={is_multivalued}\")\n", "                \n", "                field_types_info['existing_tr_field_types'][field_name] = {\n", "                    'type': field_type,\n", "                    'multiValued': is_multivalued,\n", "                    'full_definition': field\n", "                }\n", "                \n", "                # Count type usage\n", "                if field_type not in type_usage:\n", "                    type_usage[field_type] = 0\n", "                type_usage[field_type] += 1\n", "            \n", "            # Recommend the most commonly used type\n", "            if type_usage:\n", "                most_common_type = max(type_usage, key=type_usage.get)\n", "                field_types_info['recommended_type'] = most_common_type\n", "                print(f\"\\n💡 Most common type for tr_* fields: {most_common_type} (used {type_usage[most_common_type]} times)\")\n", "                print(f\"💡 Recommended type for new fields: {most_common_type}\")\n", "            \n", "        else:\n", "            print(f\"❌ Failed to analyze existing fields: {response.status_code}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error analyzing existing fields: {e}\")\n", "    \n", "    return field_types_info\n", "\n", "# Run field type investigation\n", "field_types_info = investigate_field_types()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Enhanced Field Addition with Multiple Approaches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def enhanced_field_addition():\n", "    \"\"\"\n", "    Try multiple approaches to add the missing translation fields.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔧 STEP 2: ENHANCED FIELD ADDITION WITH MULTIPLE APPROACHES\")\n", "    print(\"=\"*60)\n", "    \n", "    addition_results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'approaches_tried': [],\n", "        'successful_fields': [],\n", "        'failed_attempts': [],\n", "        'success': <PERSON><PERSON><PERSON>\n", "    }\n", "    \n", "    # Get recommended type from investigation\n", "    recommended_type = field_types_info.get('recommended_type', 'text')\n", "    available_types = field_types_info.get('available_types', {})\n", "    \n", "    print(f\"🎯 Recommended field type: {recommended_type}\")\n", "    \n", "    # Define multiple approaches to try\n", "    field_approaches = [\n", "        {\n", "            'name': f'Recommended Type ({recommended_type})',\n", "            'definition': {\n", "                'type': recommended_type,\n", "                'multiValued': True,\n", "                'stored': True,\n", "                'indexed': True\n", "            }\n", "        },\n", "        {\n", "            'name': 'Text Type (Basic)',\n", "            'definition': {\n", "                'type': 'text',\n", "                'multiValued': True,\n", "                'stored': True,\n", "                'indexed': True\n", "            }\n", "        },\n", "        {\n", "            'name': 'String Type (Fallback)',\n", "            'definition': {\n", "                'type': 'string',\n", "                'multiValued': True,\n", "                'stored': True,\n", "                'indexed': True\n", "            }\n", "        }\n", "    ]\n", "    \n", "    # Filter approaches to only use available types\n", "    valid_approaches = []\n", "    for approach in field_approaches:\n", "        field_type = approach['definition']['type']\n", "        if field_type in available_types or field_type in ['text', 'string']:  # Basic types should always exist\n", "            valid_approaches.append(approach)\n", "            print(f\"✅ Will try: {approach['name']}\")\n", "        else:\n", "            print(f\"❌ Skipping: {approach['name']} (type '{field_type}' not available)\")\n", "    \n", "    # Try each approach for each field\n", "    for field_name in TRANSLATION_FIELDS:\n", "        print(f\"\\n📝 Adding field: {field_name}\")\n", "        field_added = False\n", "        \n", "        for approach in valid_approaches:\n", "            if field_added:\n", "                break  # Field already added successfully\n", "                \n", "            print(f\"\\n🧪 Trying approach: {approach['name']}\")\n", "            \n", "            try:\n", "                # Create field definition\n", "                field_definition = {'name': field_name}\n", "                field_definition.update(approach['definition'])\n", "                \n", "                print(f\"📄 Field definition: {json.dumps(field_definition, indent=2)}\")\n", "                \n", "                # Try to add the field\n", "                headers = {'Content-Type': 'application/json'}\n", "                response = requests.post(\n", "                    f\"{SCHEMA_URL}/fields\",\n", "                    headers=headers,\n", "                    data=json.dumps(field_definition),\n", "                    timeout=30\n", "                )\n", "                \n", "                print(f\"📡 Response status: {response.status_code}\")\n", "                print(f\"📡 Response text: {response.text}\")\n", "                \n", "                if response.status_code == 200:\n", "                    response_data = response.json()\n", "                    if response_data.get('responseHeader', {}).get('status') == 0:\n", "                        print(f\"✅ SUCCESS! Field {field_name} added with {approach['name']}\")\n", "                        addition_results['successful_fields'].append({\n", "                            'field': field_name,\n", "                            'approach': approach['name'],\n", "                            'definition': field_definition\n", "                        })\n", "                        field_added = True\n", "                    else:\n", "                        error_msg = f\"Solr error: {response_data}\"\n", "                        print(f\"❌ {error_msg}\")\n", "                        addition_results['failed_attempts'].append({\n", "                            'field': field_name,\n", "                            'approach': approach['name'],\n", "                            'error': error_msg\n", "                        })\n", "                else:\n", "                    error_msg = f\"HTTP {response.status_code}: {response.text}\"\n", "                    print(f\"❌ {error_msg}\")\n", "                    addition_results['failed_attempts'].append({\n", "                        'field': field_name,\n", "                        'approach': approach['name'],\n", "                        'error': error_msg\n", "                    })\n", "                    \n", "            except Exception as e:\n", "                error_msg = f\"Exception: {str(e)}\"\n", "                print(f\"❌ {error_msg}\")\n", "                addition_results['failed_attempts'].append({\n", "                    'field': field_name,\n", "                    'approach': approach['name'],\n", "                    'error': error_msg\n", "                })\n", "        \n", "        if not field_added:\n", "            print(f\"❌ Failed to add {field_name} with any approach\")\n", "    \n", "    # Summary\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"📊 ENHANCED FIELD ADDITION SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    if addition_results['successful_fields']:\n", "        print(f\"✅ Successfully added {len(addition_results['successful_fields'])} field(s):\")\n", "        for success in addition_results['successful_fields']:\n", "            print(f\"  ✅ {success['field']} using {success['approach']}\")\n", "        addition_results['success'] = True\n", "        \n", "        # Wait for schema changes\n", "        print(\"\\n⏳ Waiting for schema changes to take effect...\")\n", "        time.sleep(5)\n", "    else:\n", "        print(\"❌ No fields were successfully added\")\n", "        print(\"\\n🔍 All attempts failed. This suggests:\")\n", "        print(\"   1. Schema modification permissions are restricted\")\n", "        print(\"   2. Solr version doesn't support dynamic schema updates\")\n", "        print(\"   3. Core is in read-only mode\")\n", "        print(\"   4. Manual schema.xml modification may be required\")\n", "    \n", "    if addition_results['failed_attempts']:\n", "        print(f\"\\n❌ Failed attempts: {len(addition_results['failed_attempts'])}\")\n", "        for failure in addition_results['failed_attempts']:\n", "            print(f\"  ❌ {failure['field']} with {failure['approach']}: {failure['error']}\")\n", "    \n", "    return addition_results\n", "\n", "# Run enhanced field addition\n", "addition_results = enhanced_field_addition()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Alternative Solutions and Manual Instructions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def provide_alternative_solutions():\n", "    \"\"\"\n", "    Provide alternative solutions if automatic field addition fails.\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"🔧 STEP 3: ALTE<PERSON>TIVE SOLUTIONS AND MANUAL INSTRUCTIONS\")\n", "    print(\"=\"*80)\n", "    \n", "    if addition_results.get('success', False):\n", "        print(\"✅ Automatic field addition was successful!\")\n", "        print(\"✅ No manual intervention needed.\")\n", "        return\n", "    \n", "    print(\"❌ Automatic field addition failed. Here are alternative solutions:\")\n", "    \n", "    # Solution 1: Manual schema.xml modification\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🛠️ SOLUTION 1: MANUAL SCHEMA.XML MODIFICATION\")\n", "    print(\"=\"*60)\n", "    \n", "    # Get the recommended field type\n", "    recommended_type = field_types_info.get('recommended_type', 'text')\n", "    \n", "    print(\"📋 Steps to manually add fields to schema.xml:\")\n", "    print(\"\\n1. 🔍 Locate the schema.xml file for the Morocco core:\")\n", "    print(f\"   Path: /path/to/solr/cores/{CORE_NAME}/conf/schema.xml\")\n", "    print(\"   (or managed-schema if using managed schema)\")\n", "    \n", "    print(\"\\n2. 📝 Add these field definitions to the <fields> section:\")\n", "    print(\"\\n<!-- Translation fields for job offers -->\")\n", "    for field_name in TRANSLATION_FIELDS:\n", "        print(f'<field name=\"{field_name}\" type=\"{recommended_type}\" indexed=\"true\" stored=\"true\" multiValued=\"true\"/>')\n", "    \n", "    print(\"\\n3. 🔄 Reload the core:\")\n", "    reload_url = f\"{SOLR_BASE_URL}admin/cores?action=RELOAD&core={CORE_NAME}\"\n", "    print(f\"   URL: {reload_url}\")\n", "    print(\"   Or restart Solr if reload doesn't work\")\n", "    \n", "    print(\"\\n4. ✅ Verify fields were added:\")\n", "    verify_url = f\"{SCHEMA_URL}/fields?wt=json\"\n", "    print(f\"   URL: {verify_url}\")\n", "    \n", "    # Solution 2: Copy field approach\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔄 SOLUTION 2: COPY FIELD APPROACH (WORKAROUND)\")\n", "    print(\"=\"*60)\n", "    \n", "    print(\"📋 If you can't modify the schema, use existing fields as aliases:\")\n", "    print(\"\\n1. 🎯 Use existing tr_* fields that have similar characteristics\")\n", "    print(\"2. 🔄 Modify your pipeline to use these field names instead:\")\n", "    \n", "    # Find suitable existing tr_* fields\n", "    existing_tr_fields = field_types_info.get('existing_tr_field_types', {})\n", "    if existing_tr_fields:\n", "        print(\"\\n📋 Available tr_* fields you could repurpose:\")\n", "        for field_name, field_info in existing_tr_fields.items():\n", "            field_type = field_info.get('type', 'unknown')\n", "            is_multivalued = field_info.get('multiValued', False)\n", "            print(f\"   📄 {field_name}: type={field_type}, multiValued={is_multivalued}\")\n", "        \n", "        # Suggest specific mappings\n", "        text_fields = [name for name, info in existing_tr_fields.items() \n", "                      if info.get('type') == recommended_type and info.get('multiValued', False)]\n", "        \n", "        if len(text_fields) >= 2:\n", "            print(f\"\\n💡 Suggested mapping (using {recommended_type} fields):\")\n", "            print(f\"   tr_field_offre_description_poste → {text_fields[0]}\")\n", "            print(f\"   tr_field_offre_profil → {text_fields[1]}\")\n", "            \n", "            print(\"\\n🔧 Update your pipeline configuration:\")\n", "            print(\"   SOURCE_FIELDS = {\")\n", "            print(\"       'sm_field_offre_description_poste': '\" + text_fields[0] + \"',\")\n", "            print(\"       'sm_field_offre_profil': '\" + text_fields[1] + \"'\")\n", "            print(\"   }\")\n", "    \n", "    # Solution 3: Dynamic field approach\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🌟 SOLUTION 3: DYNAMIC FIELD APPROACH\")\n", "    print(\"=\"*60)\n", "    \n", "    print(\"📋 Use Solr's dynamic field feature:\")\n", "    print(\"\\n1. 🔍 Check if dynamic fields are configured:\")\n", "    print(f\"   URL: {SCHEMA_URL}/dynamicfields?wt=json\")\n", "    \n", "    print(\"\\n2. 🎯 Look for patterns like:\")\n", "    print(\"   <dynamicField name='*_txt' type='text' .../>\")\n", "    print(\"   <dynamicField name='tr_*' type='text' .../>\")\n", "    \n", "    print(\"\\n3. 🔄 If available, modify field names to match pattern:\")\n", "    print(\"   tr_field_offre_description_poste_txt\")\n", "    print(\"   tr_field_offre_profil_txt\")\n", "    \n", "    # Solution 4: Contact administrator\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"👥 SOLUTION 4: CONTACT SOLR ADMINISTRATOR\")\n", "    print(\"=\"*60)\n", "    \n", "    print(\"📋 If all else fails, contact your Solr administrator with:\")\n", "    print(\"\\n📄 Required field definitions:\")\n", "    for field_name in TRANSLATION_FIELDS:\n", "        print(f\"   Field: {field_name}\")\n", "        print(f\"   Type: {recommended_type}\")\n", "        print(f\"   Properties: indexed=true, stored=true, multiValued=true\")\n", "        print()\n", "    \n", "    print(\"📋 Business justification:\")\n", "    print(\"   - Required for multilingual job offer translation feature\")\n", "    print(\"   - Will store English translations of French job descriptions\")\n", "    print(\"   - Critical for expanding to international markets\")\n", "    \n", "    # Generate comprehensive report\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    report = {\n", "        'timestamp': timestamp,\n", "        'automatic_addition_failed': True,\n", "        'field_types_investigation': field_types_info,\n", "        'addition_attempts': addition_results,\n", "        'recommended_type': recommended_type,\n", "        'required_fields': TRANSLATION_FIELDS,\n", "        'manual_schema_xml': {\n", "            'field_definitions': [f'<field name=\"{field}\" type=\"{recommended_type}\" indexed=\"true\" stored=\"true\" multiValued=\"true\"/>' \n", "                                for field in TRANSLATION_FIELDS],\n", "            'reload_url': f\"{SOLR_BASE_URL}admin/cores?action=RELOAD&core={CORE_NAME}\"\n", "        },\n", "        'alternative_approaches': {\n", "            'existing_tr_fields': list(existing_tr_fields.keys()) if existing_tr_fields else [],\n", "            'dynamic_fields_check_url': f\"{SCHEMA_URL}/dynamicfields?wt=json\"\n", "        }\n", "    }\n", "    \n", "    # Save report\n", "    report_filename = f\"schema_fix_alternatives_{timestamp}.json\"\n", "    report_filepath = os.path.join(RESULTS_DIR, report_filename)\n", "    \n", "    with open(report_filepath, 'w', encoding='utf-8') as f:\n", "        json.dump(report, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"\\n📁 Detailed alternative solutions saved to: {report_filepath}\")\n", "    \n", "    return report\n", "\n", "# Provide alternative solutions\n", "alternatives_report = provide_alternative_solutions()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Final Summary and Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*80)\n", "print(\"🎯 ENHANCED SCHEMA FIXER - FINAL SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "# Determine overall status\n", "if addition_results.get('success', False):\n", "    print(\"\\n🎉 SUCCESS! Schema has been automatically fixed!\")\n", "    print(\"✅ Translation fields have been added to the schema\")\n", "    print(\"✅ Your translation pipeline should now work correctly\")\n", "    \n", "    # Test the fix\n", "    print(\"\\n🧪 Quick test - try updating a document now:\")\n", "    test_url = f\"{UPDATE_URL}?commit=true\"\n", "    print(f\"Update URL: {test_url}\")\n", "    \n", "    # Verification URL\n", "    verify_url = f\"{SELECT_URL}?q=id:\\\"{TEST_DOC_ID}\\\"&fl=id,tr_field_offre_description_poste,tr_field_offre_profil&wt=json&indent=true\"\n", "    print(f\"\\n🔗 Verify fields exist:\")\n", "    print(verify_url)\n", "    \n", "else:\n", "    print(\"\\n⚠️ Automatic schema fix failed - Manual intervention required\")\n", "    print(\"❌ Could not automatically add translation fields\")\n", "    print(\"🔧 Please follow the alternative solutions provided above\")\n", "    \n", "    recommended_type = field_types_info.get('recommended_type', 'text')\n", "    print(f\"\\n📋 Quick Manual Fix:\")\n", "    print(f\"1. Add these lines to your schema.xml:\")\n", "    for field_name in TRANSLATION_FIELDS:\n", "        print(f'   <field name=\"{field_name}\" type=\"{recommended_type}\" indexed=\"true\" stored=\"true\" multiValued=\"true\"/>')\n", "    print(f\"2. Reload core: {SOLR_BASE_URL}admin/cores?action=RELOAD&core={CORE_NAME}\")\n", "    print(f\"3. Test your pipeline\")\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"🔧 ENHANCED SCHEMA FIXER COMPLETE\")\n", "print(\"=\"*80)\n", "print(\"\\nNext steps:\")\n", "if addition_results.get('success', False):\n", "    print(\"1. ✅ Test your translation pipeline - it should work now!\")\n", "    print(\"2. ✅ Apply the same fix to other country cores\")\n", "    print(\"3. ✅ Monitor the first few translation batches\")\nelse:\n", "    print(\"1. 🔧 Follow the manual schema modification instructions\")\n", "    print(\"2. 🔧 Or contact your Solr administrator\")\n", "    print(\"3. 🔧 Consider the alternative workaround approaches\")\n", "    print(\"4. ✅ Test the pipeline after schema changes\")\n", "\n", "print(\"\\n🚀 Good luck with your translation pipeline!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}