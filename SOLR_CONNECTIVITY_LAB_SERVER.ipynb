{"cells": [{"cell_type": "markdown", "id": "title-section", "metadata": {}, "source": ["# Solr Connectivity Lab - Server Version\n", "\n", "This notebook is designed to test Solr connectivity on the server via X2GO. It focuses solely on testing connectivity to Solr and basic query functionality."]}, {"cell_type": "code", "execution_count": null, "id": "imports-section", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import logging\n", "import os\n", "import pysolr\n", "import requests\n", "import time\n", "import pandas as pd\n", "from datetime import datetime\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('solr_lab')"]}, {"cell_type": "code", "execution_count": null, "id": "config-section", "metadata": {}, "outputs": [], "source": ["# Solr Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "LOCAL_SOLR_URL = \"http://localhost:8983/solr/\"\n", "\n", "# Fields we're interested in for job offers\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "# Countries to test with\n", "TEST_COUNTRIES = ['algerie', 'centrafrique', 'benin', 'burkina']\n", "\n", "# Test parameters\n", "MAX_TEST_DOCS = 10  # Maximum number of documents to retrieve for testing"]}, {"cell_type": "markdown", "id": "direct-http-section", "metadata": {}, "source": ["## 1. Direct HTTP Request Tests\n", "\n", "First, let's test connectivity using direct HTTP requests to the Solr server."]}, {"cell_type": "code", "execution_count": null, "id": "direct-http-functions", "metadata": {}, "outputs": [], "source": ["def test_direct_http_request(country):\n", "    \"\"\"\n", "    Test connectivity to Solr using a direct HTTP request.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        dict: Information about the request\n", "    \"\"\"\n", "    try:\n", "        # Construct the URL\n", "        url = f\"{SOLR_BASE_URL}core_cvsearch_{country}/select?q=*%3A*&rows=1&wt=json&indent=true\"\n", "        \n", "        logger.info(f\"Testing direct HTTP request to {url}\")\n", "        \n", "        # Make the request\n", "        start_time = time.time()\n", "        response = requests.get(url)\n", "        end_time = time.time()\n", "        \n", "        # Calculate request time\n", "        request_time = (end_time - start_time) * 1000  # Convert to ms\n", "        \n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            # Parse the response\n", "            data = json.loads(response.text)\n", "            num_found = data['response']['numFound']\n", "            \n", "            return {\n", "                'country': country,\n", "                'status': 'OK',\n", "                'status_code': response.status_code,\n", "                'num_found': num_found,\n", "                'request_time_ms': request_time,\n", "                'url': url\n", "            }\n", "        else:\n", "            return {\n", "                'country': country,\n", "                'status': 'Error',\n", "                'status_code': response.status_code,\n", "                'error': f\"HTTP status code: {response.status_code}\",\n", "                'request_time_ms': request_time,\n", "                'url': url\n", "            }\n", "    except Exception as e:\n", "        logger.error(f\"Error in direct HTTP request for {country}: {e}\")\n", "        return {\n", "            'country': country,\n", "            'status': 'Error',\n", "            'error': str(e),\n", "            'url': url if 'url' in locals() else f\"{SOLR_BASE_URL}core_cvsearch_{country}\"\n", "        }"]}, {"cell_type": "code", "execution_count": null, "id": "run-direct-http-tests", "metadata": {}, "outputs": [], "source": ["# Run direct HTTP request tests for all test countries\n", "print(\"Testing Solr connectivity using direct HTTP requests...\\n\")\n", "\n", "direct_http_results = []\n", "for country in TEST_COUNTRIES:\n", "    print(f\"Testing direct HTTP request for {country}...\")\n", "    result = test_direct_http_request(country)\n", "    direct_http_results.append(result)\n", "    \n", "    if result['status'] == 'OK':\n", "        print(f\"  ✅ Success! Found {result['num_found']} documents in {result['request_time_ms']:.2f} ms\")\n", "    else:\n", "        print(f\"  ❌ Failed: {result.get('error', 'Unknown error')}\")\n", "    \n", "    print(f\"  URL: {result['url']}\")\n", "    print()\n", "\n", "# Summarize results\n", "successful_countries = [r['country'] for r in direct_http_results if r['status'] == 'OK']\n", "failed_countries = [r['country'] for r in direct_http_results if r['status'] != 'OK']\n", "\n", "print(\"\\nDirect HTTP Request Summary:\")\n", "print(f\"  Successful: {len(successful_countries)} countries - {', '.join(successful_countries) if successful_countries else 'None'}\")\n", "print(f\"  Failed: {len(failed_countries)} countries - {', '.join(failed_countries) if failed_countries else 'None'}\")"]}, {"cell_type": "markdown", "id": "pysolr-section", "metadata": {}, "source": ["## 2. PySOLR Tests\n", "\n", "Now let's test connectivity using the PySOLR library."]}, {"cell_type": "code", "execution_count": null, "id": "pysolr-functions", "metadata": {}, "outputs": [], "source": ["def test_pysolr_connection(country):\n", "    \"\"\"\n", "    Test connectivity to Solr using PySOLR.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        dict: Information about the connection test\n", "    \"\"\"\n", "    try:\n", "        # Construct the Solr URL\n", "        solr_url = f\"{SOLR_BASE_URL}core_cvsearch_{country}\"\n", "        \n", "        logger.info(f\"Testing PySOLR connection to {solr_url}\")\n", "        \n", "        # Create PySOLR connection\n", "        start_time = time.time()\n", "        solr = pysolr.Solr(solr_url)\n", "        \n", "        # Try a simple query\n", "        results = solr.search('*:*', rows=1)\n", "        end_time = time.time()\n", "        \n", "        # Calculate query time\n", "        query_time = (end_time - start_time) * 1000  # Convert to ms\n", "        \n", "        # Get number of documents\n", "        num_found = len(results)\n", "        \n", "        return {\n", "            'country': country,\n", "            'status': 'OK',\n", "            'num_found': num_found,\n", "            'query_time_ms': query_time,\n", "            'solr_url': solr_url\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error in PySOLR connection for {country}: {e}\")\n", "        return {\n", "            'country': country,\n", "            'status': 'Error',\n", "            'error': str(e),\n", "            'solr_url': solr_url if 'solr_url' in locals() else f\"{SOLR_BASE_URL}core_cvsearch_{country}\"\n", "        }"]}, {"cell_type": "code", "execution_count": null, "id": "run-pysolr-tests", "metadata": {}, "outputs": [], "source": ["# Run PySOLR tests for all test countries\n", "print(\"Testing Solr connectivity using PySOLR...\\n\")\n", "\n", "pysolr_results = []\n", "for country in TEST_COUNTRIES:\n", "    print(f\"Testing PySOLR connection for {country}...\")\n", "    result = test_pysolr_connection(country)\n", "    pysolr_results.append(result)\n", "    \n", "    if result['status'] == 'OK':\n", "        print(f\"  ✅ Success! Found {result['num_found']} documents in {result['query_time_ms']:.2f} ms\")\n", "    else:\n", "        print(f\"  ❌ Failed: {result.get('error', 'Unknown error')}\")\n", "    \n", "    print(f\"  URL: {result['solr_url']}\")\n", "    print()\n", "\n", "# Summarize results\n", "successful_countries = [r['country'] for r in pysolr_results if r['status'] == 'OK']\n", "failed_countries = [r['country'] for r in pysolr_results if r['status'] != 'OK']\n", "\n", "print(\"\\nPySOLR Summary:\")\n", "print(f\"  Successful: {len(successful_countries)} countries - {', '.join(successful_countries) if successful_countries else 'None'}\")\n", "print(f\"  Failed: {len(failed_countries)} countries - {', '.join(failed_countries) if failed_countries else 'None'}\")"]}, {"cell_type": "markdown", "id": "boss-code-section", "metadata": {}, "source": ["## 3. Boss's <PERSON> Pattern Test\n", "\n", "Let's test the pattern from your boss's code."]}, {"cell_type": "code", "execution_count": null, "id": "boss-code-functions", "metadata": {}, "outputs": [], "source": ["def test_boss_code_pattern(country):\n", "    \"\"\"\n", "    Test the pattern from the boss's code.\n", "    \n", "    Args:\n", "        country (str): Country code or 'dev' for local Solr\n", "        \n", "    Returns:\n", "        dict: Information about the connection test\n", "    \"\"\"\n", "    try:\n", "        # Use the pattern from the boss's code\n", "        if country != \"dev\":\n", "            solr_url = f\"{SOLR_BASE_URL}core_cvsearch_{country}\"\n", "            core_name = f\"core_cvsearch_{country}\"\n", "        else:\n", "            solr_url = f\"{LOCAL_SOLR_URL}core_outman_cvsearch\"\n", "            core_name = \"core_outman_cvsearch\"\n", "        \n", "        logger.info(f\"Testing boss's code pattern for {country} at {solr_url}\")\n", "        \n", "        # Create PySOLR connection\n", "        start_time = time.time()\n", "        solr = pysolr.Solr(solr_url)\n", "        \n", "        # Try a simple query\n", "        results = solr.search('*:*', rows=1)\n", "        end_time = time.time()\n", "        \n", "        # Calculate query time\n", "        query_time = (end_time - start_time) * 1000  # Convert to ms\n", "        \n", "        # Get number of documents\n", "        num_found = len(results)\n", "        \n", "        return {\n", "            'country': country,\n", "            'core_name': core_name,\n", "            'status': 'OK',\n", "            'num_found': num_found,\n", "            'query_time_ms': query_time,\n", "            'solr_url': solr_url\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error in boss's code pattern for {country}: {e}\")\n", "        return {\n", "            'country': country,\n", "            'core_name': core_name if 'core_name' in locals() else f\"Unknown for {country}\",\n", "            'status': 'Error',\n", "            'error': str(e),\n", "            'solr_url': solr_url if 'solr_url' in locals() else \"Unknown\"\n", "        }"]}, {"cell_type": "code", "execution_count": null, "id": "run-boss-code-tests", "metadata": {}, "outputs": [], "source": ["# Run boss's code pattern tests\n", "print(\"Testing Solr connectivity using boss's code pattern...\\n\")\n", "\n", "# Add 'dev' to the test countries\n", "boss_test_countries = TEST_COUNTRIES + ['dev']\n", "\n", "boss_code_results = []\n", "for country in boss_test_countries:\n", "    print(f\"Testing boss's code pattern for {country}...\")\n", "    result = test_boss_code_pattern(country)\n", "    boss_code_results.append(result)\n", "    \n", "    if result['status'] == 'OK':\n", "        print(f\"  ✅ Success! Found {result['num_found']} documents in {result['query_time_ms']:.2f} ms\")\n", "    else:\n", "        print(f\"  ❌ Failed: {result.get('error', 'Unknown error')}\")\n", "    \n", "    print(f\"  Core: {result['core_name']}\")\n", "    print(f\"  URL: {result['solr_url']}\")\n", "    print()\n", "\n", "# Summarize results\n", "successful_countries = [r['country'] for r in boss_code_results if r['status'] == 'OK']\n", "failed_countries = [r['country'] for r in boss_code_results if r['status'] != 'OK']\n", "\n", "print(\"\\nBoss's Code Pattern Summary:\")\n", "print(f\"  Successful: {len(successful_countries)} countries - {', '.join(successful_countries) if successful_countries else 'None'}\")\n", "print(f\"  Failed: {len(failed_countries)} countries - {', '.join(failed_countries) if failed_countries else 'None'}\")"]}, {"cell_type": "markdown", "id": "field-existence-section", "metadata": {}, "source": ["## 4. Field Existence Test\n", "\n", "Let's test if the fields we need to translate exist in the documents."]}, {"cell_type": "code", "execution_count": null, "id": "field-existence-functions", "metadata": {}, "outputs": [], "source": ["def test_field_existence(country, num_docs=MAX_TEST_DOCS):\n", "    \"\"\"\n", "    Test if the fields we need to translate exist in the documents.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        num_docs (int): Number of documents to check\n", "        \n", "    Returns:\n", "        dict: Field existence statistics\n", "    \"\"\"\n", "    try:\n", "        # Use the pattern from the boss's code\n", "        if country != \"dev\":\n", "            solr_url = f\"{SOLR_BASE_URL}core_cvsearch_{country}\"\n", "        else:\n", "            solr_url = f\"{LOCAL_SOLR_URL}core_outman_cvsearch\"\n", "        \n", "        logger.info(f\"Testing field existence for {country} at {solr_url}\")\n", "        \n", "        # Create PySOLR connection\n", "        solr = pysolr.Solr(solr_url)\n", "        \n", "        # Fields to check\n", "        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Try a query to get documents\n", "        results = solr.search('*:*', **{'fl': ','.join(fields), 'rows': num_docs})\n", "        \n", "        # Convert to list of dictionaries\n", "        docs = [dict(doc) for doc in results]\n", "        \n", "        # Check field existence\n", "        field_stats = {}\n", "        for field in fields:\n", "            # Count documents with this field\n", "            docs_with_field = [doc for doc in docs if field in doc]\n", "            \n", "            # Calculate statistics\n", "            field_stats[field] = {\n", "                'exists_count': len(docs_with_field),\n", "                'exists_percent': len(docs_with_field) / len(docs) * 100 if docs else 0,\n", "                'missing_count': len(docs) - len(docs_with_field),\n", "                'missing_percent': (len(docs) - len(docs_with_field)) / len(docs) * 100 if docs else 0\n", "            }\n", "        \n", "        return {\n", "            'country': country,\n", "            'status': 'OK',\n", "            'total_docs': len(docs),\n", "            'field_stats': field_stats,\n", "            'solr_url': solr_url\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error in field existence test for {country}: {e}\")\n", "        return {\n", "            'country': country,\n", "            'status': 'Error',\n", "            'error': str(e),\n", "            'solr_url': solr_url if 'solr_url' in locals() else \"Unknown\"\n", "        }"]}, {"cell_type": "code", "execution_count": null, "id": "run-field-existence-tests", "metadata": {}, "outputs": [], "source": ["# Run field existence tests for successful countries\n", "print(\"Testing field existence...\\n\")\n", "\n", "# Use countries that were successful in the boss's code pattern test\n", "field_test_countries = [r['country'] for r in boss_code_results if r['status'] == 'OK']\n", "\n", "if not field_test_countries:\n", "    print(\"No successful countries to test field existence.\")\n", "else:\n", "    field_existence_results = []\n", "    for country in field_test_countries:\n", "        print(f\"Testing field existence for {country}...\")\n", "        result = test_field_existence(country)\n", "        field_existence_results.append(result)\n", "        \n", "        if result['status'] == 'OK':\n", "            print(f\"  ✅ Success! Checked {result['total_docs']} documents\")\n", "            \n", "            # Print field statistics\n", "            print(\"\\n  Field existence statistics:\")\n", "            for field, stats in result['field_stats'].items():\n", "                print(f\"    {field}: {stats['exists_count']}/{result['total_docs']} ({stats['exists_percent']:.1f}%)\")\n", "        else:\n", "            print(f\"  ❌ Failed: {result.get('error', 'Unknown error')}\")\n", "        \n", "        print(f\"\\n  URL: {result['solr_url']}\")\n", "        print()"]}, {"cell_type": "markdown", "id": "sample-data-section", "metadata": {}, "source": ["## 5. Sample Data Retrieval\n", "\n", "Let's retrieve a sample document to see the actual data."]}, {"cell_type": "code", "execution_count": null, "id": "sample-data-functions", "metadata": {}, "outputs": [], "source": ["def get_sample_document(country):\n", "    \"\"\"\n", "    Get a sample document from Solr.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        \n", "    Returns:\n", "        dict: Sam<PERSON> document\n", "    \"\"\"\n", "    try:\n", "        # Use the pattern from the boss's code\n", "        if country != \"dev\":\n", "            solr_url = f\"{SOLR_BASE_URL}core_cvsearch_{country}\"\n", "        else:\n", "            solr_url = f\"{LOCAL_SOLR_URL}core_outman_cvsearch\"\n", "        \n", "        logger.info(f\"Getting sample document for {country} from {solr_url}\")\n", "        \n", "        # Create PySOLR connection\n", "        solr = pysolr.Solr(solr_url)\n", "        \n", "        # Try a query to get a document\n", "        results = solr.search('*:*', rows=1)\n", "        \n", "        # Convert to dictionary\n", "        if len(results) > 0:\n", "            doc = dict(results[0])\n", "            return {\n", "                'country': country,\n", "                'status': 'OK',\n", "                'document': doc,\n", "                'solr_url': solr_url\n", "            }\n", "        else:\n", "            return {\n", "                'country': country,\n", "                'status': 'Error',\n", "                'error': 'No documents found',\n", "                'solr_url': solr_url\n", "            }\n", "    except Exception as e:\n", "        logger.error(f\"Error getting sample document for {country}: {e}\")\n", "        return {\n", "            'country': country,\n", "            'status': 'Error',\n", "            'error': str(e),\n", "            'solr_url': solr_url if 'solr_url' in locals() else \"Unknown\"\n", "        }"]}, {"cell_type": "code", "execution_count": null, "id": "run-sample-data-retrieval", "metadata": {}, "outputs": [], "source": ["# Get sample documents for successful countries\n", "print(\"Retrieving sample documents...\\n\")\n", "\n", "# Use countries that were successful in the boss's code pattern test\n", "sample_countries = [r['country'] for r in boss_code_results if r['status'] == 'OK']\n", "\n", "if not sample_countries:\n", "    print(\"No successful countries to retrieve sample documents.\")\n", "else:\n", "    for country in sample_countries:\n", "        print(f\"Retrieving sample document for {country}...\")\n", "        result = get_sample_document(country)\n", "        \n", "        if result['status'] == 'OK':\n", "            print(f\"  ✅ Success! Retrieved sample document\")\n", "            \n", "            # Print document fields\n", "            doc = result['document']\n", "            print(\"\\n  Document fields:\")\n", "            for field in sorted(doc.keys()):\n", "                value = doc[field]\n", "                # Truncate long values\n", "                if isinstance(value, str) and len(value) > 100:\n", "                    value = value[:100] + \"...\"\n", "                elif isinstance(value, list) and len(str(value)) > 100:\n", "                    value = str(value)[:100] + \"...\"\n", "                print(f\"    {field}: {value}\")\n", "            \n", "            # Check for our specific fields\n", "            print(\"\\n  Source fields for translation:\")\n", "            for field in SOURCE_FIELDS.keys():\n", "                if field in doc:\n", "                    value = doc[field]\n", "                    if isinstance(value, list) and value:\n", "                        value = value[0]\n", "                    # Truncate long values\n", "                    if isinstance(value, str) and len(value) > 100:\n", "                        value = value[:100] + \"...\"\n", "                    print(f\"    {field}: {value}\")\n", "                else:\n", "                    print(f\"    {field}: Not found in document\")\n", "        else:\n", "            print(f\"  ❌ Failed: {result.get('error', 'Unknown error')}\")\n", "        \n", "        print(f\"\\n  URL: {result['solr_url']}\")\n", "        print()"]}, {"cell_type": "markdown", "id": "conclusion-section", "metadata": {}, "source": ["## 6. Summary and Conclusion\n", "\n", "Let's summarize the results of our connectivity tests."]}, {"cell_type": "code", "execution_count": null, "id": "summary", "metadata": {}, "outputs": [], "source": ["# Summarize all test results\n", "print(\"SOLR CONNECTIVITY TEST SUMMARY\")\n", "print(\"============================\\n\")\n", "\n", "# Direct HTTP Request Summary\n", "direct_http_success = [r['country'] for r in direct_http_results if r['status'] == 'OK']\n", "print(\"1. Direct HTTP Request Tests:\")\n", "print(f\"   - Success: {len(direct_http_success)}/{len(direct_http_results)} countries\")\n", "print(f\"   - Successful countries: {', '.join(direct_http_success) if direct_http_success else 'None'}\")\n", "print()\n", "\n", "# PySOLR Summary\n", "pysolr_success = [r['country'] for r in pysolr_results if r['status'] == 'OK']\n", "print(\"2. PySOLR Tests:\")\n", "print(f\"   - Success: {len(pysolr_success)}/{len(pysolr_results)} countries\")\n", "print(f\"   - Successful countries: {', '.join(pysolr_success) if pysolr_success else 'None'}\")\n", "print()\n", "\n", "# Boss's Code Pattern Summary\n", "boss_code_success = [r['country'] for r in boss_code_results if r['status'] == 'OK']\n", "print(\"3. <PERSON>'s Code Pattern Tests:\")\n", "print(f\"   - Success: {len(boss_code_success)}/{len(boss_code_results)} countries\")\n", "print(f\"   - Successful countries: {', '.join(boss_code_success) if boss_code_success else 'None'}\")\n", "print()\n", "\n", "# Overall Conclusion\n", "print(\"CONCLUSION:\")\n", "if direct_http_success or pysolr_success or boss_code_success:\n", "    print(\"✅ Solr connectivity is working for some countries!\")\n", "    \n", "    # Determine the best approach\n", "    approaches = [\n", "        (\"Direct HTTP\", len(direct_http_success)),\n", "        (\"PySOLR\", len(pysolr_success)),\n", "        (\"Boss's Code Pattern\", len(boss_code_success))\n", "    ]\n", "    best_approach = max(approaches, key=lambda x: x[1])\n", "    \n", "    print(f\"The most successful approach is: {best_approach[0]} with {best_approach[1]} successful countries\")\n", "    \n", "    # Check if source fields exist\n", "    if 'field_existence_results' in locals() and field_existence_results:\n", "        fields_exist = True\n", "        for result in field_existence_results:\n", "            if result['status'] == 'OK':\n", "                for field in SOURCE_FIELDS.keys():\n", "                    if field in result['field_stats'] and result['field_stats'][field]['exists_percent'] < 50:\n", "                        fields_exist = False\n", "                        break\n", "        \n", "        if fields_exist:\n", "            print(\"✅ The source fields for translation exist in the documents\")\n", "        else:\n", "            print(\"⚠️ Some source fields for translation are missing in many documents\")\n", "else:\n", "    print(\"❌ Solr connectivity is not working for any country!\")\n", "    print(\"Please check your network connection and Solr server configuration.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}