{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solr Update Testing Notebook\n", "\n", "This notebook is dedicated to testing Solr update operations for the job offer translation pipeline. It provides a series of tests with specific URLs that can be executed to verify Solr update functionality.\n", "\n", "## Purpose\n", "- Test Solr connectivity\n", "- Test document retrieval\n", "- Test document updates with translated fields\n", "- Verify update results\n", "\n", "Each test will provide a URL that can be executed, and the results will be used to refine the update functionality."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import logging\n", "import os\n", "import time\n", "import requests\n", "from datetime import datetime\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(levelname)s - %(message)s',\n", "    datefmt='%Y-%m-%d %H:%M:%S'\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"✅ Basic libraries successfully imported\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration\n", "\n", "Define the Solr server configuration and test parameters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Solr configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "\n", "# Test countries\n", "TEST_COUNTRIES = [\n", "    \"algerie\",\n", "    \"benin\",\n", "    \"burkina\",\n", "    \"centrafrique\"\n", "]\n", "\n", "# Field mapping\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "print(\"✅ Configuration successfully loaded\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 1: Solr Server Information\n", "\n", "First, let's test basic connectivity to the Solr server by retrieving server information."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_solr_info():\n", "    \"\"\"\n", "    Get information about the Solr server.\n", "    \n", "    Returns:\n", "        dict: Solr server information\n", "    \"\"\"\n", "    try:\n", "        url = f\"{SOLR_BASE_URL}admin/info/system?wt=json\"\n", "        print(f\"🔍 Testing URL: {url}\")\n", "        \n", "        response = requests.get(url, timeout=30)\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            logger.error(f\"Failed to get Solr info: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error getting Solr info: {e}\")\n", "        return None\n", "\n", "# Get Solr information\n", "print(\"Testing Solr server information...\\n\")\n", "solr_info = get_solr_info()\n", "\n", "if solr_info:\n", "    print(\"\\n✅ Solr server is accessible\")\n", "    print(f\"  - Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}\")\n", "    print(f\"  - Solr Implementation: {solr_info.get('lucene', {}).get('solr-impl-version', 'Unknown')}\")\n", "    print(f\"  - JVM: {solr_info.get('jvm', {}).get('name', 'Unknown')} {solr_info.get('jvm', {}).get('version', 'Unknown')}\")\n", "else:\n", "    print(\"\\n❌ Could not connect to Solr server. Check connectivity.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 2: List Available Cores\n", "\n", "Next, let's list all available cores on the Solr server to verify that our country-specific cores exist."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def list_solr_cores():\n", "    \"\"\"\n", "    List all available cores on the Solr server.\n", "    \n", "    Returns:\n", "        dict: Information about available cores\n", "    \"\"\"\n", "    try:\n", "        url = f\"{SOLR_BASE_URL}admin/cores?action=STATUS&wt=json\"\n", "        print(f\"🔍 Testing URL: {url}\")\n", "        \n", "        response = requests.get(url, timeout=30)\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            logger.error(f\"Failed to list Solr cores: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error listing Solr cores: {e}\")\n", "        return None\n", "\n", "# List Solr cores\n", "print(\"Listing Solr cores...\\n\")\n", "cores_info = list_solr_cores()\n", "\n", "if cores_info and 'status' in cores_info:\n", "    cores = cores_info['status']\n", "    print(f\"\\n✅ Found {len(cores)} cores:\")\n", "    \n", "    # Check for our test country cores\n", "    job_search_cores = []\n", "    for core_name in cores.keys():\n", "        if core_name.startswith('core_jobsearch_'):\n", "            job_search_cores.append(core_name)\n", "            country = core_name.replace('core_jobsearch_', '')\n", "            print(f\"  - {core_name} (Country: {country})\")\n", "    \n", "    # Check if all test countries have cores\n", "    missing_countries = []\n", "    for country in TEST_COUNTRIES:\n", "        core_name = f\"core_jobsearch_{country}\"\n", "        if core_name not in cores:\n", "            missing_countries.append(country)\n", "    \n", "    if missing_countries:\n", "        print(f\"\\n⚠️ Missing cores for countries: {', '.join(missing_countries)}\")\n", "    else:\n", "        print(f\"\\n✅ All test countries have cores\")\n", "else:\n", "    print(\"\\n❌ Could not list Solr cores. Check connectivity.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 3: Query Documents from a Core\n", "\n", "Now, let's query documents from a specific country's core to verify that we can retrieve job offers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def query_solr_documents(country, query=\"*:*\", fields=\"id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil\", rows=5):\n", "    \"\"\"\n", "    Query documents from a specific country's core.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        query (str): Solr query\n", "        fields (str): Comma-separated list of fields to retrieve\n", "        rows (int): Number of rows to retrieve\n", "        \n", "    Returns:\n", "        dict: Query results\n", "    \"\"\"\n", "    try:\n", "        # Construct the URL\n", "        core_name = f\"core_jobsearch_{country}\"\n", "        url = f\"{SOLR_BASE_URL}{core_name}/select\"\n", "        \n", "        # Prepare parameters\n", "        params = {\n", "            'q': query,\n", "            'fl': fields,\n", "            'rows': rows,\n", "            'wt': 'json'\n", "        }\n", "        \n", "        # Log the URL with parameters\n", "        param_str = \"&\".join([f\"{k}={v}\" for k, v in params.items()])\n", "        full_url = f\"{url}?{param_str}\"\n", "        print(f\"🔍 Testing URL: {full_url}\")\n", "        \n", "        # Make the request\n", "        response = requests.get(url, params=params, timeout=30)\n", "        \n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            logger.error(f\"Failed to query Solr: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error querying Solr: {e}\")\n", "        return None\n", "\n", "# Select a country to test\n", "test_country = TEST_COUNTRIES[0]  # Using the first test country (algerie)\n", "\n", "print(f\"Querying documents from {test_country}...\\n\")\n", "query_results = query_solr_documents(test_country)\n", "\n", "if query_results and 'response' in query_results:\n", "    num_found = query_results['response']['numFound']\n", "    docs = query_results['response']['docs']\n", "    \n", "    print(f\"\\n✅ Found {num_found} documents in {test_country}\")\n", "    \n", "    if docs:\n", "        print(f\"\\nSample document:\")\n", "        sample_doc = docs[0]\n", "        print(f\"  - ID: {sample_doc.get('id', 'N/A')}\")\n", "        print(f\"  - Entity ID: {sample_doc.get('entity_id', 'N/A')}\")\n", "        \n", "        # Check if the document has the source fields\n", "        for field in SOURCE_FIELDS.keys():\n", "            if field in sample_doc:\n", "                value = sample_doc[field]\n", "                print(f\"  - {field}: {value[:100]}...\" if isinstance(value, list) and value and len(value[0]) > 100 else f\"  - {field}: {value}\")\n", "            else:\n", "                print(f\"  - {field}: Not present\")\n", "        \n", "        # Check if the document already has translated fields\n", "        for field in SOURCE_FIELDS.values():\n", "            if field in sample_doc:\n", "                value = sample_doc[field]\n", "                print(f\"  - {field}: {value[:100]}...\" if isinstance(value, list) and value and len(value[0]) > 100 else f\"  - {field}: {value}\")\n", "            else:\n", "                print(f\"  - {field}: Not present\")\n", "    else:\n", "        print(\"No documents found in the response.\")\n", "else:\n", "    print(f\"\\n❌ Could not query documents from {test_country}. Check connectivity.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 4: Find Documents Without Translations\n", "\n", "Let's find documents that don't have translations yet. These are the documents we'll want to update."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_untranslated_documents(country, rows=5):\n", "    \"\"\"\n", "    Find documents that don't have translations yet.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        rows (int): Number of rows to retrieve\n", "        \n", "    Returns:\n", "        dict: Query results\n", "    \"\"\"\n", "    try:\n", "        # Construct the URL\n", "        core_name = f\"core_jobsearch_{country}\"\n", "        url = f\"{SOLR_BASE_URL}{core_name}/select\"\n", "        \n", "        # Prepare parameters\n", "        # Use filter query to exclude documents with translations\n", "        params = {\n", "            'q': '*:*',\n", "            'fq': '-tr_field_offre_description_poste:[* TO *] AND -tr_field_offre_profil:[* TO *]',\n", "            'fl': 'id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil',\n", "            'rows': rows,\n", "            'wt': 'json'\n", "        }\n", "        \n", "        # Log the URL with parameters\n", "        param_str = \"&\".join([f\"{k}={v}\" for k, v in params.items()])\n", "        full_url = f\"{url}?{param_str}\"\n", "        print(f\"🔍 Testing URL: {full_url}\")\n", "        \n", "        # Make the request\n", "        response = requests.get(url, params=params, timeout=30)\n", "        \n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            logger.error(f\"Failed to query Solr: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error querying Solr: {e}\")\n", "        return None\n", "\n", "# Select a country to test\n", "test_country = TEST_COUNTRIES[0]  # Using the first test country (algerie)\n", "\n", "print(f\"Finding untranslated documents from {test_country}...\\n\")\n", "query_results = find_untranslated_documents(test_country)\n", "\n", "if query_results and 'response' in query_results:\n", "    num_found = query_results['response']['numFound']\n", "    docs = query_results['response']['docs']\n", "    \n", "    print(f\"\\n✅ Found {num_found} untranslated documents in {test_country}\")\n", "    \n", "    if docs:\n", "        print(f\"\\nSample untranslated document:\")\n", "        sample_doc = docs[0]\n", "        print(f\"  - ID: {sample_doc.get('id', 'N/A')}\")\n", "        print(f\"  - Entity ID: {sample_doc.get('entity_id', 'N/A')}\")\n", "        \n", "        # Check source fields\n", "        for field in SOURCE_FIELDS.keys():\n", "            if field in sample_doc:\n", "                value = sample_doc[field]\n", "                print(f\"  - {field}: {value[:100]}...\" if isinstance(value, list) and value and len(value[0]) > 100 else f\"  - {field}: {value}\")\n", "            else:\n", "                print(f\"  - {field}: Not present\")\n", "    else:\n", "        print(\"No untranslated documents found.\")\n", "else:\n", "    print(f\"\\n❌ Could not query untranslated documents from {test_country}. Check connectivity.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 5: Prepare Update Document\n", "\n", "Now, let's prepare an update document for a specific job offer. We'll create a document with translated fields that we can send to Solr."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_update_document(doc_id, translations):\n", "    \"\"\"\n", "    Prepare an update document for Solr using atomic updates.\n", "    \n", "    Args:\n", "        doc_id (str): Document ID\n", "        translations (dict): Dictionary of translated fields\n", "        \n", "    Returns:\n", "        dict: Update document with atomic update syntax\n", "    \"\"\"\n", "    # Create update document with ID field\n", "    update_doc = {\n", "        \"id\": doc_id\n", "    }\n", "    \n", "    # Add translated fields using atomic update syntax\n", "    for field, value in translations.items():\n", "        # Ensure the value is an array to match the original format\n", "        if isinstance(value, list):\n", "            update_doc[field] = {\"set\": value}\n", "        else:\n", "            update_doc[field] = {\"set\": [value]}\n", "    \n", "    return update_doc\n", "\n", "# Get a document ID from the previous query\n", "doc_id = None\n", "if 'query_results' in locals() and query_results and 'response' in query_results and query_results['response']['docs']:\n", "    doc_id = query_results['response']['docs'][0].get('id')\n", "\n", "if not doc_id:\n", "    print(\"No document ID available. Please run the previous cell first.\")\n", "else:\n", "    # Create sample translations\n", "    translations = {\n", "        \"tr_field_offre_description_poste\": [\"This is a test translation of the job description.\"],\n", "        \"tr_field_offre_profil\": [\"This is a test translation of the job profile.\"]\n", "    }\n", "    \n", "    # Prepare update document\n", "    update_doc = prepare_update_document(doc_id, translations)\n", "    \n", "    print(f\"Prepared update document for ID: {doc_id}\\n\")\n", "    print(json.dumps(update_doc, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 6: Test Update Document (Dry Run)\n", "\n", "Let's test updating a document in Solr, but in dry run mode (no actual updates)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_update_document(country, update_doc, dry_run=True):\n", "    \"\"\"\n", "    Test updating a document in Solr.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        update_doc (dict): Update document\n", "        dry_run (bool): If True, don't actually update Solr\n", "        \n", "    Returns:\n", "        dict: Response from Solr\n", "    \"\"\"\n", "    try:\n", "        # Construct the URL\n", "        core_name = f\"core_jobsearch_{country}\"\n", "        url = f\"{SOLR_BASE_URL}{core_name}/update\"\n", "        \n", "        # Prepare parameters\n", "        params = {\n", "            'commit': 'true',\n", "            'wt': 'json'\n", "        }\n", "        \n", "        # Prepare headers\n", "        headers = {\n", "            'Content-Type': 'application/json'\n", "        }\n", "        \n", "        # Log the URL\n", "        param_str = \"&\".join([f\"{k}={v}\" for k, v in params.items()])\n", "        print(f\"🔍 Testing URL: {url}?{param_str}\")\n", "        \n", "        # Log the update document\n", "        print(f\"\\nUpdate document:\")\n", "        print(json.dumps(update_doc, indent=2))\n", "        \n", "        if dry_run:\n", "            print(\"\\n⚠️ DRY RUN MODE - No actual update will be performed\")\n", "            return {\n", "                \"responseHeader\": {\n", "                    \"status\": 0,\n", "                    \"QTime\": 0\n", "                },\n", "                \"dry_run\": True\n", "            }\n", "        \n", "        # Make the request\n", "        response = requests.post(\n", "            url,\n", "            params=params,\n", "            headers=headers,\n", "            data=json.dumps([update_doc]),\n", "            timeout=30\n", "        )\n", "        \n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            logger.error(f\"Failed to update Solr: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error updating Solr: {e}\")\n", "        return None\n", "\n", "# Test updating a document\n", "if 'update_doc' not in locals() or not update_doc:\n", "    print(\"No update document available. Please run the previous cell first.\")\n", "else:\n", "    print(f\"Testing document update for {test_country} (DRY RUN)...\\n\")\n", "    response = test_update_document(test_country, update_doc, dry_run=True)\n", "    \n", "    if response:\n", "        print(f\"\\n✅ Dry run successful\")\n", "        print(f\"Response: {json.dumps(response, indent=2)}\")\n", "    else:\n", "        print(f\"\\n❌ Dry run failed. Check logs for details.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 7: Update Document (Real Update)\n", "\n", "Now, let's perform a real update to Solr. **CAUTION: This will actually update the document in Solr.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CAUTION: This will actually update the document in Solr\n", "# Set perform_update to True to execute the update\n", "perform_update = False  # Change to True to perform the update\n", "\n", "if not perform_update:\n", "    print(\"⚠️ Update not performed. Set perform_update = True to execute the update.\")\n", "elif 'update_doc' not in locals() or not update_doc:\n", "    print(\"No update document available. Please run the previous cell first.\")\n", "else:\n", "    print(f\"Updating document in {test_country}...\\n\")\n", "    response = test_update_document(test_country, update_doc, dry_run=False)\n", "    \n", "    if response and response.get('responseHeader', {}).get('status') == 0:\n", "        print(f\"\\n✅ Update successful\")\n", "        print(f\"Response: {json.dumps(response, indent=2)}\")\n", "    else:\n", "        print(f\"\\n❌ Update failed. Check logs for details.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 8: Verify Document Update\n", "\n", "Finally, let's verify that the document was updated correctly by retrieving it from Solr."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def verify_document_update(country, doc_id):\n", "    \"\"\"\n", "    Verify that a document was updated correctly.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        doc_id (str): Document ID\n", "        \n", "    Returns:\n", "        dict: Document from Solr\n", "    \"\"\"\n", "    try:\n", "        # Construct the URL\n", "        core_name = f\"core_jobsearch_{country}\"\n", "        url = f\"{SOLR_BASE_URL}{core_name}/select\"\n", "        \n", "        # Prepare parameters\n", "        params = {\n", "            'q': f'id:\"{doc_id}\"',\n", "            'fl': '*',\n", "            'wt': 'json'\n", "        }\n", "        \n", "        # Log the URL with parameters\n", "        param_str = \"&\".join([f\"{k}={v}\" for k, v in params.items()])\n", "        full_url = f\"{url}?{param_str}\"\n", "        print(f\"🔍 Testing URL: {full_url}\")\n", "        \n", "        # Make the request\n", "        response = requests.get(url, params=params, timeout=30)\n", "        \n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            result = response.json()\n", "            if result.get('response', {}).get('numFound', 0) > 0:\n", "                return result['response']['docs'][0]\n", "            else:\n", "                logger.warning(f\"Document not found: {doc_id}\")\n", "                return None\n", "        else:\n", "            logger.error(f\"Failed to query Solr: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error querying Solr: {e}\")\n", "        return None\n", "\n", "# Verify document update\n", "if 'doc_id' not in locals() or not doc_id:\n", "    print(\"No document ID available. Please run the previous cells first.\")\n", "else:\n", "    print(f\"Verifying document update for ID: {doc_id}\\n\")\n", "    doc = verify_document_update(test_country, doc_id)\n", "    \n", "    if doc:\n", "        print(f\"\\n✅ Document found in Solr\")\n", "        print(f\"  - ID: {doc.get('id', 'N/A')}\")\n", "        print(f\"  - Entity ID: {doc.get('entity_id', 'N/A')}\")\n", "        \n", "        # Check source fields\n", "        for field in SOURCE_FIELDS.keys():\n", "            if field in doc:\n", "                value = doc[field]\n", "                print(f\"  - {field}: {value[:100]}...\" if isinstance(value, list) and value and len(value[0]) > 100 else f\"  - {field}: {value}\")\n", "            else:\n", "                print(f\"  - {field}: Not present\")\n", "        \n", "        # Check translated fields\n", "        for field in SOURCE_FIELDS.values():\n", "            if field in doc:\n", "                value = doc[field]\n", "                print(f\"  - {field}: {value[:100]}...\" if isinstance(value, list) and value and len(value[0]) > 100 else f\"  - {field}: {value}\")\n", "            else:\n", "                print(f\"  - {field}: Not present\")\n", "    else:\n", "        print(f\"\\n❌ Document not found in Solr. The update may have failed or the document ID is incorrect.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 9: Bat<PERSON> Update Documents\n", "\n", "Let's test updating multiple documents in a single batch."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_batch_update(country, num_docs=3):\n", "    \"\"\"\n", "    Prepare a batch update for multiple documents using atomic updates.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        num_docs (int): Number of documents to update\n", "        \n", "    Returns:\n", "        list: List of update documents with atomic update syntax\n", "    \"\"\"\n", "    # Find untranslated documents\n", "    query_results = find_untranslated_documents(country, rows=num_docs)\n", "    \n", "    if not query_results or 'response' not in query_results or not query_results['response']['docs']:\n", "        print(f\"No untranslated documents found in {country}\")\n", "        return []\n", "    \n", "    docs = query_results['response']['docs']\n", "    update_docs = []\n", "    \n", "    for doc in docs:\n", "        doc_id = doc.get('id')\n", "        if not doc_id:\n", "            continue\n", "        \n", "        # Create sample translations\n", "        translations = {\n", "            \"tr_field_offre_description_poste\": [f\"Batch test translation of job description for {doc_id}.\"],\n", "            \"tr_field_offre_profil\": [f\"Batch test translation of job profile for {doc_id}.\"]\n", "        }\n", "        \n", "        # Prepare update document with atomic update syntax\n", "        update_doc = prepare_update_document(doc_id, translations)\n", "        update_docs.append(update_doc)\n", "    \n", "    return update_docs\n", "\n", "def test_batch_update(country, update_docs, dry_run=True):\n", "    \"\"\"\n", "    Test updating multiple documents in Solr.\n", "    \n", "    Args:\n", "        country (str): Country code\n", "        update_docs (list): List of update documents\n", "        dry_run (bool): If True, don't actually update Solr\n", "        \n", "    Returns:\n", "        dict: Response from Solr\n", "    \"\"\"\n", "    try:\n", "        # Construct the URL\n", "        core_name = f\"core_jobsearch_{country}\"\n", "        url = f\"{SOLR_BASE_URL}{core_name}/update\"\n", "        \n", "        # Prepare parameters\n", "        params = {\n", "            'commit': 'true',\n", "            'wt': 'json'\n", "        }\n", "        \n", "        # Prepare headers\n", "        headers = {\n", "            'Content-Type': 'application/json'\n", "        }\n", "        \n", "        # Log the URL\n", "        param_str = \"&\".join([f\"{k}={v}\" for k, v in params.items()])\n", "        print(f\"🔍 Testing URL: {url}?{param_str}\")\n", "        \n", "        # Log the update documents\n", "        print(f\"\\nPrepared {len(update_docs)} documents for batch update\")\n", "        if update_docs:\n", "            print(f\"Sample update document:\")\n", "            print(json.dumps(update_docs[0], indent=2))\n", "        \n", "        if dry_run:\n", "            print(\"\\n⚠️ DRY RUN MODE - No actual update will be performed\")\n", "            return {\n", "                \"responseHeader\": {\n", "                    \"status\": 0,\n", "                    \"QTime\": 0\n", "                },\n", "                \"dry_run\": True,\n", "                \"batch_size\": len(update_docs)\n", "            }\n", "        \n", "        # Make the request\n", "        response = requests.post(\n", "            url,\n", "            params=params,\n", "            headers=headers,\n", "            data=json.dumps(update_docs),\n", "            timeout=30\n", "        )\n", "        \n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            logger.error(f\"Failed to update Solr: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error updating Solr: {e}\")\n", "        return None\n", "\n", "# Test batch update\n", "print(f\"Preparing batch update for {test_country}...\\n\")\n", "update_docs = prepare_batch_update(test_country, num_docs=3)\n", "\n", "if not update_docs:\n", "    print(\"No documents prepared for batch update.\")\n", "else:\n", "    print(f\"\\nTesting batch update for {test_country} (DRY RUN)...\\n\")\n", "    response = test_batch_update(test_country, update_docs, dry_run=True)\n", "    \n", "    if response:\n", "        print(f\"\\n✅ Batch update dry run successful\")\n", "        print(f\"Response: {json.dumps(response, indent=2)}\")\n", "    else:\n", "        print(f\"\\n❌ Batch update dry run failed. Check logs for details.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 10: <PERSON> Batch Update\n", "\n", "Finally, let's perform a real batch update to Solr. **CAUTION: This will actually update the documents in Solr.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CAUTION: This will actually update the documents in Solr\n", "# Set perform_batch_update to True to execute the update\n", "perform_batch_update = False  # Change to True to perform the batch update\n", "\n", "if not perform_batch_update:\n", "    print(\"⚠️ Batch update not performed. Set perform_batch_update = True to execute the update.\")\n", "elif 'update_docs' not in locals() or not update_docs:\n", "    print(\"No update documents available. Please run the previous cell first.\")\n", "else:\n", "    print(f\"Performing batch update for {test_country}...\\n\")\n", "    response = test_batch_update(test_country, update_docs, dry_run=False)\n", "    \n", "    if response and response.get('responseHeader', {}).get('status') == 0:\n", "        print(f\"\\n✅ Batch update successful\")\n", "        print(f\"Response: {json.dumps(response, indent=2)}\")\n", "        \n", "        # Verify one of the updated documents\n", "        if update_docs:\n", "            doc_id = update_docs[0]['id']\n", "            print(f\"\\nVerifying update for document: {doc_id}\")\n", "            doc = verify_document_update(test_country, doc_id)\n", "            \n", "            if doc:\n", "                print(f\"\\n✅ Document found in Solr with translations\")\n", "                for field in SOURCE_FIELDS.values():\n", "                    if field in doc:\n", "                        value = doc[field]\n", "                        print(f\"  - {field}: {value}\")\n", "                    else:\n", "                        print(f\"  - {field}: Not present\")\n", "            else:\n", "                print(f\"\\n❌ Document not found in Solr after update.\")\n", "    else:\n", "        print(f\"\\n❌ Batch update failed. Check logs for details.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary of Test URLs\n", "\n", "Here's a summary of all the test URLs used in this notebook. You can use these URLs to test Solr connectivity and updates on your server.\n", "\n", "### Basic Connectivity\n", "1. **Solr Server Info**: `http://************:8983/solr/admin/info/system?wt=json`\n", "2. **List Cores**: `http://************:8983/solr/admin/cores?action=STATUS&wt=json`\n", "\n", "### Document Queries\n", "3. **Query Documents**: `http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=5&wt=json`\n", "4. **Find Untranslated Documents**: `http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=-tr_field_offre_description_poste:[* TO *] AND -tr_field_offre_profil:[* TO *]&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=5&wt=json`\n", "5. **Verify Document by ID**: `http://************:8983/solr/core_jobsearch_algerie/select?q=id:\"DOCUMENT_ID\"&fl=*&wt=json`\n", "\n", "### Document Updates (Using Atomic Updates)\n", "6. **Update Single Document**: `http://************:8983/solr/core_jobsearch_algerie/update?commit=true&wt=json`\n", "   - With POST body: `[{\"id\":\"DOCUMENT_ID\",\"tr_field_offre_description_poste\":{\"set\":[\"Translation text\"]},\"tr_field_offre_profil\":{\"set\":[\"Translation text\"]}}]`\n", "   - Content-Type: `application/json`\n", "\n", "7. **Batch Update Documents**: `http://************:8983/solr/core_jobsearch_algerie/update?commit=true&wt=json`\n", "   - With POST body containing an array of documents: `[{\"id\":\"DOC1_ID\",\"tr_field_offre_description_poste\":{\"set\":[\"Translation\"]}},{\"id\":\"DOC2_ID\",\"tr_field_offre_description_poste\":{\"set\":[\"Translation\"]}}]`\n", "   - Content-Type: `application/json`\n", "\n", "## How to Use These Test Results\n", "\n", "1. **Run the tests** in this notebook to generate the specific URLs for your environment\n", "2. **Copy the URLs** from the output of each test\n", "3. **Test the URLs** on your server to verify connectivity and functionality\n", "4. **Examine the responses** to understand the structure of the data and the update process\n", "5. **Use the dry run tests** to verify your update documents without making actual changes\n", "6. **When ready, perform real updates** by setting the appropriate flags to `True`\n", "\n", "The key insights from these tests should be used to implement a robust Solr update function in the main translation pipeline."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}