{"cells": [{"cell_type": "markdown", "id": "title-section", "metadata": {}, "source": ["# Solr Connectivity - Working Approach\n", "\n", "This notebook implements the working approach for Solr connectivity based on our lab experimentation and the code snippet provided by the boss."]}, {"cell_type": "code", "execution_count": 1, "id": "imports-section", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import json\n", "import logging\n", "import os\n", "import pysolr\n", "import requests\n", "import time\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "from datetime import datetime\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger('solr_lab')"]}, {"cell_type": "code", "execution_count": 2, "id": "config-section", "metadata": {}, "outputs": [], "source": ["# Solr Configuration\n", "SOLR_BASE_URL = \"http://************:8983/solr/\"\n", "LOCAL_SOLR_URL = \"http://localhost:8983/solr/\"\n", "\n", "# Default timeout for requests (in seconds)\n", "DEFAULT_TIMEOUT = 30\n", "\n", "# Fields we're interested in for job offers\n", "SOURCE_FIELDS = {\n", "    \"sm_field_offre_description_poste\": \"tr_field_offre_description_poste\",\n", "    \"sm_field_offre_profil\": \"tr_field_offre_profil\"\n", "}\n", "\n", "# Countries to test with\n", "TEST_COUNTRIES = ['algerie', 'dev']\n", "\n", "# Test parameters\n", "MAX_TEST_DOCS = 100  # Maximum number of documents to retrieve for testing"]}, {"cell_type": "markdown", "id": "working-approach-section", "metadata": {}, "source": ["## Working Approach for Solr Connectivity\n", "\n", "Based on our lab experimentation and the code snippet provided by the boss, we've identified a working approach for Solr connectivity that uses a development mode pattern."]}, {"cell_type": "code", "execution_count": 3, "id": "working-approach-function", "metadata": {}, "outputs": [], "source": ["def get_solr_connection(country, timeout=DEFAULT_TIMEOUT):\n", "    \"\"\"\n", "    Get a connection to the Solr core for the specified country.\n", "    Uses the pattern from the boss's code.\n", "    \n", "    Args:\n", "        country (str): Country code or 'dev' for local Solr\n", "        timeout (int): Timeout in seconds for the request\n", "        \n", "    Returns:\n", "        pysolr.Solr: Solr connection\n", "    \"\"\"\n", "    try:\n", "        # Use the pattern from the boss's code\n", "        if country != \"dev\":\n", "            solr_url = f\"{SOLR_BASE_URL}core_cvsearch_{country}\"\n", "            core_name = f\"core_cvsearch_{country}\"\n", "        else:\n", "            solr_url = f\"{LOCAL_SOLR_URL}core_outman_cvsearch\"\n", "            core_name = \"core_outman_cvsearch\"\n", "        \n", "        logger.info(f\"Connecting to Solr core {core_name} at {solr_url}\")\n", "        \n", "        # Create PySOLR connection with timeout\n", "        solr = pysolr.Solr(solr_url, timeout=timeout)\n", "        \n", "        return solr\n", "    except Exception as e:\n", "        logger.error(f\"Error connecting to Solr for {country}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 4, "id": "test-solr-connection", "metadata": {}, "outputs": [], "source": ["def test_solr_connection(country, timeout=DEFAULT_TIMEOUT):\n", "    \"\"\"\n", "    Test connectivity to Solr using PySOLR.\n", "    \n", "    Args:\n", "        country (str): Country code or 'dev' for local Solr\n", "        timeout (int): Timeout in seconds for the request\n", "        \n", "    Returns:\n", "        dict: Information about the connection test\n", "    \"\"\"\n", "    try:\n", "        start_time = time.time()\n", "        \n", "        # Get Solr connection\n", "        solr = get_solr_connection(country, timeout)\n", "        if not solr:\n", "            return {\n", "                'country': country,\n", "                'status': 'Error',\n", "                'error': 'Failed to get Solr connection'\n", "            }\n", "        \n", "        # Try a simple query\n", "        results = solr.search('*:*', rows=10)\n", "        \n", "        end_time = time.time()\n", "        query_time = (end_time - start_time) * 1000  # Convert to ms\n", "        \n", "        # Get number of documents\n", "        num_found = len(results)\n", "        \n", "        return {\n", "            'country': country,\n", "            'status': 'OK',\n", "            'num_found': num_found,\n", "            'query_time_ms': query_time\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error testing Solr connection for {country}: {e}\")\n", "        return {\n", "            'country': country,\n", "            'status': 'Error',\n", "            'error': str(e)\n", "        }"]}, {"cell_type": "code", "execution_count": 5, "id": "run-connection-test", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:47:18,033 - INFO - Connecting to Solr core core_cvsearch_algerie at http://************:8983/solr/core_cvsearch_algerie\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Testing Solr connection using the working approach...\n", "\n", "Testing connection to algerie...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:47:39,089 - ERROR - Connection to server 'http://************:8983/solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json' timed out: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001A0A0628AD0>, 'Connection to ************ timed out. (connect timeout=30)'))\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py\", line 198, in _new_conn\n", "    sock = connection.create_connection(\n", "        (self._dns_host, self.port),\n", "    ...<2 lines>...\n", "        socket_options=self.socket_options,\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\connection.py\", line 85, in create_connection\n", "    raise err\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\connection.py\", line 73, in create_connection\n", "    sock.connect(sa)\n", "    ~~~~~~~~~~~~^^^^\n", "TimeoutError: [WinError 10060] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 787, in urlopen\n", "    response = self._make_request(\n", "        conn,\n", "    ...<10 lines>...\n", "        **response_kw,\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 493, in _make_request\n", "    conn.request(\n", "    ~~~~~~~~~~~~^\n", "        method,\n", "        ^^^^^^^\n", "    ...<6 lines>...\n", "        enforce_content_length=enforce_content_length,\n", "        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "    )\n", "    ^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py\", line 445, in request\n", "    self.endheaders()\n", "    ~~~~~~~~~~~~~~~^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py\", line 1333, in endheaders\n", "    self._send_output(message_body, encode_chunked=encode_chunked)\n", "    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py\", line 1093, in _send_output\n", "    self.send(msg)\n", "    ~~~~~~~~~^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py\", line 1037, in send\n", "    self.connect()\n", "    ~~~~~~~~~~~~^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py\", line 276, in connect\n", "    self.sock = self._new_conn()\n", "                ~~~~~~~~~~~~~~^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py\", line 207, in _new_conn\n", "    raise ConnectTimeoutError(\n", "    ...<2 lines>...\n", "    ) from e\n", "urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPConnection object at 0x000001A0A0628AD0>, 'Connection to ************ timed out. (connect timeout=30)')\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\adapters.py\", line 667, in send\n", "    resp = conn.urlopen(\n", "        method=request.method,\n", "    ...<9 lines>...\n", "        chunked=chunked,\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 841, in urlopen\n", "    retries = retries.increment(\n", "        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\retry.py\", line 519, in increment\n", "    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]\n", "    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001A0A0628AD0>, 'Connection to ************ timed out. (connect timeout=30)'))\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pysolr.py\", line 414, in _send_request\n", "    resp = requests_method(\n", "        url,\n", "    ...<4 lines>...\n", "        auth=self.auth,\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\sessions.py\", line 602, in get\n", "    return self.request(\"GET\", url, **kwargs)\n", "           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\sessions.py\", line 589, in request\n", "    resp = self.send(prep, **send_kwargs)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\sessions.py\", line 703, in send\n", "    r = adapter.send(request, **kwargs)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\adapters.py\", line 688, in send\n", "    raise ConnectTimeout(e, request=request)\n", "requests.exceptions.ConnectTimeout: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001A0A0628AD0>, 'Connection to ************ timed out. (connect timeout=30)'))\n", "2025-05-13 11:47:39,118 - ERROR - Error testing Solr connection for algerie: Connection to server 'http://************:8983/solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json' timed out: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001A0A0628AD0>, 'Connection to ************ timed out. (connect timeout=30)'))\n", "2025-05-13 11:47:39,118 - INFO - Connecting to Solr core core_outman_cvsearch at http://localhost:8983/solr/core_outman_cvsearch\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  - algerie: FAILED - Connection to server 'http://************:8983/solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json' timed out: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/core_cvsearch_algerie/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001A0A0628AD0>, 'Connection to ************ timed out. (connect timeout=30)'))\n", "\n", "Testing connection to dev...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 11:47:43,218 - ERROR - Failed to connect to server at http://localhost:8983/solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json: HTTPConnectionPool(host='localhost', port=8983): Max retries exceeded with url: /solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A0A06C47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py\", line 198, in _new_conn\n", "    sock = connection.create_connection(\n", "        (self._dns_host, self.port),\n", "    ...<2 lines>...\n", "        socket_options=self.socket_options,\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\connection.py\", line 85, in create_connection\n", "    raise err\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\connection.py\", line 73, in create_connection\n", "    sock.connect(sa)\n", "    ~~~~~~~~~~~~^^^^\n", "ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 787, in urlopen\n", "    response = self._make_request(\n", "        conn,\n", "    ...<10 lines>...\n", "        **response_kw,\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 493, in _make_request\n", "    conn.request(\n", "    ~~~~~~~~~~~~^\n", "        method,\n", "        ^^^^^^^\n", "    ...<6 lines>...\n", "        enforce_content_length=enforce_content_length,\n", "        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "    )\n", "    ^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py\", line 445, in request\n", "    self.endheaders()\n", "    ~~~~~~~~~~~~~~~^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py\", line 1333, in endheaders\n", "    self._send_output(message_body, encode_chunked=encode_chunked)\n", "    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py\", line 1093, in _send_output\n", "    self.send(msg)\n", "    ~~~~~~~~~^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py\", line 1037, in send\n", "    self.connect()\n", "    ~~~~~~~~~~~~^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py\", line 276, in connect\n", "    self.sock = self._new_conn()\n", "                ~~~~~~~~~~~~~~^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py\", line 213, in _new_conn\n", "    raise NewConnectionError(\n", "        self, f\"Failed to establish a new connection: {e}\"\n", "    ) from e\n", "urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001A0A06C47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\adapters.py\", line 667, in send\n", "    resp = conn.urlopen(\n", "        method=request.method,\n", "    ...<9 lines>...\n", "        chunked=chunked,\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 841, in urlopen\n", "    retries = retries.increment(\n", "        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\retry.py\", line 519, in increment\n", "    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]\n", "    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=8983): Max retries exceeded with url: /solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A0A06C47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pysolr.py\", line 414, in _send_request\n", "    resp = requests_method(\n", "        url,\n", "    ...<4 lines>...\n", "        auth=self.auth,\n", "    )\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\sessions.py\", line 602, in get\n", "    return self.request(\"GET\", url, **kwargs)\n", "           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\sessions.py\", line 589, in request\n", "    resp = self.send(prep, **send_kwargs)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\sessions.py\", line 703, in send\n", "    r = adapter.send(request, **kwargs)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\adapters.py\", line 700, in send\n", "    raise ConnectionError(e, request=request)\n", "requests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=8983): Max retries exceeded with url: /solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A0A06C47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\n", "2025-05-13 11:47:43,222 - ERROR - Error testing Solr connection for dev: Failed to connect to server at http://localhost:8983/solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json: HTTPConnectionPool(host='localhost', port=8983): Max retries exceeded with url: /solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A0A06C47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  - dev: FAILED - Failed to connect to server at http://localhost:8983/solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json: HTTPConnectionPool(host='localhost', port=8983): Max retries exceeded with url: /solr/core_outman_cvsearch/select/?q=%2A%3A%2A&rows=10&wt=json (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A0A06C47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\n", "\n"]}], "source": ["# Test connection to each country/environment\n", "print(\"Testing Solr connection using the working approach...\\n\")\n", "\n", "for country in TEST_COUNTRIES:\n", "    print(f\"Testing connection to {country}...\")\n", "    result = test_solr_connection(country)\n", "    \n", "    if result['status'] == 'OK':\n", "        print(f\"  - {country}: SUCCESS\")\n", "        print(f\"    Documents found: {result['num_found']}\")\n", "        print(f\"    Query time: {result['query_time_ms']:.2f} ms\")\n", "    else:\n", "        print(f\"  - {country}: FAILED - {result.get('error', 'Unknown error')}\")\n", "    \n", "    print()"]}, {"cell_type": "code", "execution_count": null, "id": "9cd502e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing Solr connectivity...\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 10:29:31,322 - ERROR - <PERSON><PERSON><PERSON> getting Solr info: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/admin/info/system?wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001E51AC6CEC0>, 'Connection to ************ timed out. (connect timeout=None)'))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Could not retrieve Solr information. Check connectivity.\n", "\n", "Listing Solr cores...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 10:29:52,368 - ERROR - Error listing Solr cores: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/admin/cores?action=STATUS&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001E51ACA4410>, 'Connection to ************ timed out. (connect timeout=None)'))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["No cores found or could not retrieve core list.\n", "\n", "Testing connectivity to relevant cores...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 10:30:13,405 - ERROR - Error testing core core_cvsearch_algerie: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/core_cvsearch_algerie/select?q=%2A%3A%2A&rows=1&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001E51ACA4CD0>, 'Connection to ************ timed out. (connect timeout=None)'))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  - core_cvsearch_algerie: Error (Documents: Unknown, Query Time: Unknownms)\n"]}], "source": ["# Run basic connectivity tests\n", "print(\"Testing Solr connectivity...\\n\")\n", "\n", "# Get Solr information\n", "solr_info = get_solr_info()\n", "if solr_info:\n", "    print(f\"Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}\")\n", "    print(f\"Solr Implementation: {solr_info.get('lucene', {}).get('solr-impl-version', 'Unknown')}\")\n", "    print(f\"JVM: {solr_info.get('jvm', {}).get('name', 'Unknown')} {solr_info.get('jvm', {}).get('version', 'Unknown')}\")\n", "    print(f\"System Memory: {solr_info.get('system', {}).get('totalPhysicalMemorySize', 'Unknown')} bytes\")\n", "else:\n", "    print(\"Could not retrieve Solr information. Check connectivity.\")\n", "\n", "# List cores\n", "print(\"\\nListing Solr cores...\")\n", "cores = list_solr_cores()\n", "if cores:\n", "    print(f\"Found {len(cores)} cores:\")\n", "    for core in cores:\n", "        print(f\"  - {core}\")\n", "else:\n", "    print(\"No cores found or could not retrieve core list.\")\n", "\n", "# Test connectivity to relevant cores\n", "print(\"\\nTesting connectivity to relevant cores...\")\n", "core_results = []\n", "for country in TEST_COUNTRIES:\n", "    core_name = f\"core_cvsearch_{country}\"\n", "    result = test_core_connectivity(core_name)\n", "    core_results.append(result)\n", "    if result:\n", "        print(f\"  - {core_name}: {result['status']} (Documents: {result.get('num_found', 'Unknown')}, Query Time: {result.get('query_time_ms', 'Unknown')}ms)\")\n", "    else:\n", "        print(f\"  - {core_name}: Connection failed\")"]}, {"cell_type": "code", "execution_count": null, "id": "c7b9c62d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing Solr connectivity...\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 10:29:31,322 - ERROR - <PERSON><PERSON><PERSON> getting Solr info: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/admin/info/system?wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001E51AC6CEC0>, 'Connection to ************ timed out. (connect timeout=None)'))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Could not retrieve Solr information. Check connectivity.\n", "\n", "Listing Solr cores...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 10:29:52,368 - ERROR - Error listing Solr cores: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/admin/cores?action=STATUS&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001E51ACA4410>, 'Connection to ************ timed out. (connect timeout=None)'))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["No cores found or could not retrieve core list.\n", "\n", "Testing connectivity to relevant cores...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-13 10:30:13,405 - ERROR - Error testing core core_cvsearch_algerie: HTTPConnectionPool(host='************', port=8983): Max retries exceeded with url: /solr/core_cvsearch_algerie/select?q=%2A%3A%2A&rows=1&wt=json (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001E51ACA4CD0>, 'Connection to ************ timed out. (connect timeout=None)'))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  - core_cvsearch_algerie: Error (Documents: Unknown, Query Time: Unknownms)\n"]}], "source": ["# Run basic connectivity tests\n", "print(\"Testing Solr connectivity...\\n\")\n", "\n", "# Get Solr information\n", "solr_info = get_solr_info()\n", "if solr_info:\n", "    print(f\"Solr Version: {solr_info.get('lucene', {}).get('solr-spec-version', 'Unknown')}\")\n", "    print(f\"Solr Implementation: {solr_info.get('lucene', {}).get('solr-impl-version', 'Unknown')}\")\n", "    print(f\"JVM: {solr_info.get('jvm', {}).get('name', 'Unknown')} {solr_info.get('jvm', {}).get('version', 'Unknown')}\")\n", "    print(f\"System Memory: {solr_info.get('system', {}).get('totalPhysicalMemorySize', 'Unknown')} bytes\")\n", "else:\n", "    print(\"Could not retrieve Solr information. Check connectivity.\")\n", "\n", "# List cores\n", "print(\"\\nListing Solr cores...\")\n", "cores = list_solr_cores()\n", "if cores:\n", "    print(f\"Found {len(cores)} cores:\")\n", "    for core in cores:\n", "        print(f\"  - {core}\")\n", "else:\n", "    print(\"No cores found or could not retrieve core list.\")\n", "\n", "# Test connectivity to relevant cores\n", "print(\"\\nTesting connectivity to relevant cores...\")\n", "core_results = []\n", "for country in TEST_COUNTRIES:\n", "    core_name = f\"core_cvsearch_{country}\"\n", "    result = test_core_connectivity(core_name)\n", "    core_results.append(result)\n", "    if result:\n", "        print(f\"  - {core_name}: {result['status']} (Documents: {result.get('num_found', 'Unknown')}, Query Time: {result.get('query_time_ms', 'Unknown')}ms)\")\n", "    else:\n", "        print(f\"  - {core_name}: Connection failed\")"]}, {"cell_type": "markdown", "id": "query-functions-section", "metadata": {}, "source": ["## Query Functions\n", "\n", "Now let's implement functions to query <PERSON><PERSON> for job offers and their fields."]}, {"cell_type": "code", "execution_count": null, "id": "query-functions", "metadata": {}, "outputs": [], "source": ["def get_job_offers(country, num_offers=100, timeout=DEFAULT_TIMEOUT):\n", "    \"\"\"\n", "    Get job offers from Solr.\n", "    \n", "    Args:\n", "        country (str): Country code or 'dev' for local Solr\n", "        num_offers (int): Number of job offers to retrieve\n", "        timeout (int): Timeout in seconds for the request\n", "        \n", "    Returns:\n", "        list: List of job offer documents\n", "    \"\"\"\n", "    try:\n", "        solr = get_solr_connection(country, timeout)\n", "        if not solr:\n", "            logger.error(f\"Failed to get Solr connection for {country}\")\n", "            return []\n", "        \n", "        # Fields to retrieve\n", "        fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())\n", "        \n", "        # Execute the query\n", "        logger.info(f\"Retrieving {num_offers} job offers from {country}\")\n", "        results = solr.search('*:*', **{'fl': ','.join(fields), 'rows': num_offers})\n", "        \n", "        # Convert to list of dictionaries\n", "        job_offers = [dict(doc) for doc in results]\n", "        \n", "        logger.info(f\"Retrieved {len(job_offers)} job offers from {country}\")\n", "        return job_offers\n", "    except Exception as e:\n", "        logger.error(f\"Error retrieving job offers from {country}: {e}\")\n", "        return []\n", "\n", "def update_solr_with_translations(translated_offers, country, timeout=DEFAULT_TIMEOUT):\n", "    \"\"\"\n", "    Update Solr with translated job offer fields.\n", "    \n", "    Args:\n", "        translated_offers (list): List of job offers with translated fields\n", "        country (str): Country code or 'dev' for local Solr\n", "        timeout (int): Timeout in seconds for the request\n", "        \n", "    Returns:\n", "        bool or list: True if successful, list of failed offer IDs otherwise\n", "    \"\"\"\n", "    try:\n", "        solr = get_solr_connection(country, timeout)\n", "        if not solr:\n", "            logger.error(f\"Failed to get Solr connection for {country}\")\n", "            return False\n", "        \n", "        logger.info(f\"Updating Solr with {len(translated_offers)} translated job offers\")\n", "        \n", "        offers_with_error = []\n", "        for offer in translated_offers:\n", "            offer_id = offer['id']\n", "            try:\n", "                # Update each translated field\n", "                for target_field in SOURCE_FIELDS.values():\n", "                    if target_field in offer:\n", "                        solr.add([{'id': offer_id, target_field: offer[target_field]}], fieldUpdates={'set': [target_field]})\n", "                \n", "                # Commit after each offer to ensure changes are saved\n", "                solr.commit()\n", "                logger.info(f\"Updated Solr with translations for offer ID {offer_id}\")\n", "            except Exception as inner_e:\n", "                offers_with_error.append(offer_id)\n", "                logger.error(f\"Error updating Solr for offer ID {offer_id}: {inner_e}\")\n", "        \n", "        # Return result\n", "        if not offers_with_error:\n", "            logger.info(\"Successfully updated all offers in Solr\")\n", "            return True\n", "        else:\n", "            logger.warning(f\"Failed to update {len(offers_with_error)} offers in Solr\")\n", "            return offers_with_error\n", "    except Exception as e:\n", "        logger.error(f\"Error in update_solr_with_translations: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "id": "test-query-functions", "metadata": {}, "outputs": [], "source": ["# Test retrieving job offers\n", "print(\"Testing job offer retrieval...\\n\")\n", "\n", "# Use development mode for testing\n", "country = \"dev\"\n", "print(f\"Using development mode with local Solr instance\\n\")\n", "\n", "job_offers = get_job_offers(country, num_offers=10)\n", "\n", "if job_offers:\n", "    print(f\"Retrieved {len(job_offers)} job offers\")\n", "    \n", "    # Display sample job offer\n", "    if len(job_offers) > 0:\n", "        sample_offer = job_offers[0]\n", "        print(\"\\nSample job offer:\")\n", "        print(f\"  ID: {sample_offer.get('id', 'N/A')}\")\n", "        print(f\"  Entity ID: {sample_offer.get('entity_id', 'N/A')}\")\n", "        \n", "        # Display source fields\n", "        for field in SOURCE_FIELDS.keys():\n", "            if field in sample_offer:\n", "                value = sample_offer[field]\n", "                if isinstance(value, list) and value:\n", "                    value = value[0]\n", "                print(f\"\\n  {field}:\")\n", "                print(f\"  {value[:150]}...\" if len(str(value)) > 150 else f\"  {value}\")\n", "else:\n", "    print(\"Failed to retrieve job offers. Make sure your local Solr instance is running and has data.\")"]}, {"cell_type": "markdown", "id": "conclusion-section", "metadata": {}, "source": ["## Conclusion\n", "\n", "Based on our lab experimentation, we've identified a working approach for Solr connectivity that uses a development mode pattern. This approach allows us to:\n", "\n", "1. Use a local Solr instance for development and testing\n", "2. Switch to the remote Solr server for production\n", "3. Handle connectivity issues gracefully\n", "\n", "This approach can be integrated into the job offer translation pipeline to ensure robust connectivity to Solr."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}