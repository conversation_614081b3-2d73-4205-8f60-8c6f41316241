# Solr Query Reference Guide for Job Offer Translation Pipeline

This document provides a comprehensive reference for all Solr query variations used throughout the job offer translation pipeline. It details the exact query syntax, parameters, use cases, and optimizations for each type of query.

## Table of Contents

1. [Basic Solr Connection](#basic-solr-connection)
2. [Job Offer Retrieval Queries](#job-offer-retrieval-queries)
3. [Checking for Translated Documents](#checking-for-translated-documents)
4. [Entity ID Filtering](#entity-id-filtering)
5. [Solr Update Queries](#solr-update-queries)
6. [Query Optimizations](#query-optimizations)
7. [Testing and Debugging Queries](#testing-and-debugging-queries)

## Basic Solr Connection

### Connection Establishment

```python
def get_jobsearch_connection(country, timeout=30):
    # Use the correct core pattern: core_jobsearch_[country]
    solr_url = f"{SOLR_BASE_URL}core_jobsearch_{country}"
    solr = pysolr.Solr(solr_url, timeout=timeout)
    return solr
```

**Base URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}
```

**Sample URLs for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie
http://************:8983/solr/core_jobsearch_benin
http://************:8983/solr/core_jobsearch_burkina
http://************:8983/solr/core_jobsearch_centrafrique
```

**When Used:** This connection is established at the beginning of any function that needs to interact with Solr. The connection is reused through a connection pool to improve performance.

## Job Offer Retrieval Queries

### Standard Job Offer Retrieval

**Function:** `get_job_offers()`

**Query Parameters:**
```python
params = {
    'q': '*:*',     # Main query - match all documents
    'fq': '*:*',    # Filter query - match all documents
    'fl': ','.join(fields),  # Fields to return
    'rows': num_offers,      # Number of rows to return
    'start': start,          # Starting offset
    'sort': 'entity_id asc', # Sort by entity_id ascending
    'wt': 'json'             # Response format
}
```

**HTTP URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq=*:*&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows={batch_size}&start={start}&sort=entity_id+asc&wt=json
```

**Sample URL for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=*:*&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=10&start=0&sort=entity_id+asc&wt=json
```

**When Used:** This query is used to retrieve a batch of job offers for translation. It's the primary query used in the pipeline to get documents that need processing.

**Parameter Variations:**
- `rows`: Controls the batch size (e.g., 10, 20, 50)
- `start`: Used for pagination (e.g., 0, 10, 20)
- `fl`: Fields list expands to include translated fields when `skip_translated=True`

## Checking for Translated Documents

### In-Memory Check for Translated Fields

The pipeline doesn't use a separate Solr query to check for translated documents. Instead, it:

1. Retrieves documents with both source and target fields
2. Performs an in-memory check to see if the target fields exist

```python
# Check if any of the translated fields already exist
has_translations = any(target_field in offer for target_field in SOURCE_FIELDS.values())
```

**When Used:** This check is performed after retrieving documents to filter out those that already have translations.

### Alternative: Direct Solr Query for Translated Documents

While not implemented in the current pipeline, this query could be used to directly filter out documents that already have translations:

**Query Parameters:**
```python
params = {
    'q': '*:*',
    'fq': '-(tr_field_offre_description_poste:[* TO *]) AND -(tr_field_offre_profil:[* TO *])',
    'fl': ','.join(fields),
    'rows': num_offers,
    'start': start,
    'sort': 'entity_id asc',
    'wt': 'json'
}
```

**HTTP URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq=-(tr_field_offre_description_poste:[*+TO+*])+AND+-(tr_field_offre_profil:[*+TO+*])&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows={batch_size}&start={start}&sort=entity_id+asc&wt=json
```

**Sample URL for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=-(tr_field_offre_description_poste:[*+TO+*])+AND+-(tr_field_offre_profil:[*+TO+*])&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=10&start=0&sort=entity_id+asc&wt=json
```

**When Used:** This query could be used to directly retrieve only documents that need translation, avoiding the need for in-memory filtering.

## Entity ID Filtering

### Query by Single Entity ID

**Function:** `get_job_offer_by_entity_id()`

**Query Parameters:**
```python
params = {
    'q': '*:*',
    'fq': f'entity_id:{entity_id}',
    'fl': ','.join(fields),
    'rows': 1,
    'wt': 'json'
}
```

**HTTP URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq=entity_id:{entity_id}&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=1&wt=json
```

**Sample URL for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=entity_id:123456&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=1&wt=json
```

**When Used:** This query is used to retrieve a specific job offer by its entity_id.

### Query by Multiple Entity IDs

**Function:** `get_job_offers_by_entity_ids()`

**Query Parameters:**
```python
# Construct entity_id filter query
entity_ids_query = " OR ".join([f"entity_id:{eid}" for eid in entity_ids])

params = {
    'q': '*:*',
    'fq': f'({entity_ids_query})',
    'fl': ','.join(fields),
    'rows': len(entity_ids),
    'wt': 'json'
}
```

**HTTP URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq=(entity_id:{id1}+OR+entity_id:{id2}+OR+entity_id:{id3})&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows={num_ids}&wt=json
```

**Sample URL for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=(entity_id:123456+OR+entity_id:789012+OR+entity_id:345678)&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=3&wt=json
```

**When Used:** This query is used to retrieve multiple job offers by their entity_ids.

## Solr Update Queries

### Batch Update with Translated Fields

**Function:** `update_solr_with_translations()`

**PySOLR Update Command:**
```python
# Update documents in batch using atomic updates
commit_within = 5000  # 5 seconds
solr.add(update_batch, fieldUpdates={'set': target_fields}, commitWithin=commit_within)
```

**Equivalent HTTP POST Request:**
```
POST http://************:8983/solr/core_jobsearch_{country}/update?commit=false&commitWithin=5000
Content-Type: application/json

{
  "add": [
    {
      "id": "c8xu1x/node/160086",
      "tr_field_offre_description_poste": {"set": "Translated job description text..."},
      "tr_field_offre_profil": {"set": "Translated profile requirements text..."}
    },
    {
      "id": "c8xu1x/node/160087",
      "tr_field_offre_description_poste": {"set": "Another translated job description..."},
      "tr_field_offre_profil": {"set": "Another translated profile requirements..."}
    }
  ]
}
```

**Final Commit Command:**
```python
# Final commit to ensure all changes are persisted
solr.commit()
```

**Equivalent HTTP GET Request:**
```
GET http://************:8983/solr/core_jobsearch_{country}/update?commit=true
```

**When Used:** This update query is used to add the translated fields to the job offer documents in Solr. It uses atomic updates to modify only the specified fields without affecting the rest of the document.

**Parameter Variations:**
- `commitWithin`: Controls when Solr will automatically commit the changes (in milliseconds)
- `update_batch`: Contains the documents to update, with only the id and translated fields

## Query Optimizations

### 1. Filter Query vs. Main Query

The pipeline uses both `q` and `fq` parameters:
- `q=*:*`: Main query that matches all documents
- `fq=*:*`: Filter query that also matches all documents

**Optimization:** Using `fq` for filtering allows Solr to cache the filter results, improving performance for subsequent queries with the same filter.

### 2. Field Selection

The pipeline carefully selects only the fields it needs:
```python
fields = ['id', 'entity_id'] + list(SOURCE_FIELDS.keys())
if skip_translated:
    fields += list(SOURCE_FIELDS.values())
```

**Optimization:** Retrieving only the necessary fields reduces network traffic and processing time.

### 3. Batch Size Control

The pipeline controls the batch size for both retrieval and updates:
```python
params = {
    'rows': num_offers,
    'start': start
}
```

**Optimization:** Using appropriate batch sizes balances between making too many small requests and making too few large requests.

### 4. Connection Pooling

The pipeline reuses Solr connections:
```python
# Check if we already have a connection for this country
if country in solr_connections:
    return solr_connections[country]
```

**Optimization:** Reusing connections reduces the overhead of establishing new connections.

### 5. Batch Commits

The pipeline uses batch commits for updates:
```python
solr.add(update_batch, fieldUpdates={'set': target_fields}, commitWithin=commit_within)
```

**Optimization:** Using batch commits reduces the number of commit operations, which are expensive in Solr.

## Testing and Debugging Queries

### Direct HTTP Query Function

The pipeline includes a function for testing Solr queries directly:

```python
def query_solr_direct(country, params=None):
    # Set default parameters if none provided
    if params is None:
        params = {
            'q': '*:*',
            'fq': '*:*',
            'fl': 'id,entity_id',
            'rows': 10,
            'start': 0,
            'sort': 'entity_id asc',
            'wt': 'json'
        }

    # Construct the URL
    solr_url = f"{SOLR_BASE_URL}core_jobsearch_{country}/select"

    # Make the request
    response = requests.get(solr_url, params=params, timeout=30)

    # Return the JSON response
    return response.json() if response.status_code == 200 else None
```

**When Used:** This function is used for testing and debugging Solr queries directly, bypassing PySOLR.

### URL Logging

The pipeline logs the full Solr query URL for debugging:

```python
# Construct the exact URL for logging/debugging
solr_url = f"{SOLR_BASE_URL}core_jobsearch_{country}/select"
query_params = '&'.join([f"{k}={v}" for k, v in params.items()])
full_url = f"{solr_url}?{query_params}"
logger.debug(f"Full Solr query URL: {full_url}")
```

**When Used:** This logging helps debug issues with Solr queries by showing the exact URL being used.

## Advanced Query Patterns

### Range Queries for Entity IDs

**Query Parameters:**
```python
params = {
    'q': '*:*',
    'fq': f'entity_id:[{start_id} TO {end_id}]',
    'fl': ','.join(fields),
    'rows': num_offers,
    'sort': 'entity_id asc',
    'wt': 'json'
}
```

**HTTP URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq=entity_id:[{start_id}+TO+{end_id}]&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows={batch_size}&sort=entity_id+asc&wt=json
```

**Sample URL for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=entity_id:[100000+TO+200000]&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=10&sort=entity_id+asc&wt=json
```

**When Used:** This query pattern can be used to retrieve job offers within a specific range of entity IDs, which is useful for processing large datasets in chunks.

### Existence Queries for Fields

**Query Parameters:**
```python
params = {
    'q': '*:*',
    'fq': f'{field_name}:[* TO *]',  # Field exists
    'fl': ','.join(fields),
    'rows': num_offers,
    'wt': 'json'
}
```

**HTTP URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq={field_name}:[*+TO+*]&fl=id,entity_id&rows={batch_size}&wt=json
```

**Sample URL for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=tr_field_offre_description_poste:[*+TO+*]&fl=id,entity_id,tr_field_offre_description_poste&rows=10&wt=json
```

**When Used:** This query pattern is used to find documents where a specific field exists. It's useful for finding documents that already have translations.

### Non-Existence Queries for Fields

**Query Parameters:**
```python
params = {
    'q': '*:*',
    'fq': f'-{field_name}:[* TO *]',  # Field does not exist
    'fl': ','.join(fields),
    'rows': num_offers,
    'wt': 'json'
}
```

**HTTP URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq=-{field_name}:[*+TO+*]&fl=id,entity_id&rows={batch_size}&wt=json
```

**Sample URL for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=-tr_field_offre_description_poste:[*+TO+*]&fl=id,entity_id,sm_field_offre_description_poste&rows=10&wt=json
```

**When Used:** This query pattern is used to find documents where a specific field does not exist. It's useful for finding documents that need translation.

### Combined Field Existence Queries

**Query Parameters:**
```python
params = {
    'q': '*:*',
    'fq': f'{field1}:[* TO *] AND {field2}:[* TO *]',  # Both fields exist
    'fl': ','.join(fields),
    'rows': num_offers,
    'wt': 'json'
}
```

**HTTP URL Format:**
```
http://************:8983/solr/core_jobsearch_{country}/select?q=*:*&fq={field1}:[*+TO+*]+AND+{field2}:[*+TO+*]&fl=id,entity_id&rows={batch_size}&wt=json
```

**Sample URL for Testing:**
```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=tr_field_offre_description_poste:[*+TO+*]+AND+tr_field_offre_profil:[*+TO+*]&fl=id,entity_id,tr_field_offre_description_poste,tr_field_offre_profil&rows=10&wt=json
```

**When Used:** This query pattern is used to find documents where multiple fields exist. It's useful for finding documents that have all required translations.

## Query Parameter Deep Dive

### Main Query Parameter (q)

The `q` parameter defines the main query and affects document scoring:

```
q=*:*                 # Match all documents
q=entity_id:123456    # Match a specific entity_id
q=field:value         # Match documents where field contains value
```

**When to Use:** Use the `q` parameter for the primary search criteria, especially when relevance scoring matters.

### Filter Query Parameter (fq)

The `fq` parameter filters the result set without affecting scoring:

```
fq=*:*                           # Match all documents
fq=entity_id:123456              # Filter by entity_id
fq=field:[* TO *]                # Field exists
fq=-field:[* TO *]               # Field does not exist
fq=(field1:value OR field2:value) # Either field1 or field2 matches
fq=field1:value AND field2:value  # Both field1 and field2 match
```

**When to Use:** Use the `fq` parameter for filtering criteria that don't affect relevance scoring. Filter queries are cached, making them more efficient for repeated use.

### Field List Parameter (fl)

The `fl` parameter specifies which fields to return:

```
fl=id,entity_id                  # Return only id and entity_id
fl=*                             # Return all fields
fl=id,entity_id,field1,field2    # Return specific fields
```

**When to Use:** Use the `fl` parameter to limit the fields returned to only those needed, reducing network traffic and processing time.

### Rows and Start Parameters (rows, start)

The `rows` and `start` parameters control pagination:

```
rows=10&start=0       # First 10 documents
rows=10&start=10      # Next 10 documents
rows=10&start=20      # Next 10 documents after that
```

**When to Use:** Use these parameters to implement pagination or to process large result sets in manageable chunks.

### Sort Parameter (sort)

The `sort` parameter controls the order of results:

```
sort=entity_id+asc    # Sort by entity_id in ascending order
sort=entity_id+desc   # Sort by entity_id in descending order
sort=field1+asc,field2+desc # Sort by field1 ascending, then field2 descending
```

**When to Use:** Use the `sort` parameter to ensure consistent ordering of results, which is important for pagination and batch processing.

### Response Format Parameter (wt)

The `wt` parameter controls the response format:

```
wt=json               # Return results as JSON
wt=xml                # Return results as XML
wt=csv                # Return results as CSV
```

**When to Use:** Use the `wt` parameter to specify the desired response format. JSON is the most commonly used format for programmatic access.

## Common Query Patterns by Use Case

### 1. Initial Data Exploration

```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&rows=10&wt=json
```

**Purpose:** Quick check to see what's in the index and what fields are available.

### 2. Finding Documents Needing Translation

```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=-(tr_field_offre_description_poste:[*+TO+*])+AND+-(tr_field_offre_profil:[*+TO+*])&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil&rows=10&wt=json
```

**Purpose:** Find documents that don't have any translated fields.

### 3. Checking Translation Progress

```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=tr_field_offre_description_poste:[*+TO+*]&fl=id,entity_id,tr_field_offre_description_poste&rows=0&wt=json
```

**Purpose:** Count how many documents have translations (using rows=0 to just get the count).

### 4. Verifying Updates

```
http://************:8983/solr/core_jobsearch_algerie/select?q=*:*&fq=id:"c8xu1x/node/160086"&fl=id,entity_id,sm_field_offre_description_poste,sm_field_offre_profil,tr_field_offre_description_poste,tr_field_offre_profil&wt=json
```

**Purpose:** Check if a specific document has been updated with translations.

## Conclusion

This reference guide covers all the Solr query variations used in the job offer translation pipeline. By understanding these query patterns and parameters, you can effectively work with the pipeline, debug issues, and extend its functionality as needed.

Remember that the most critical aspects of Solr queries in this pipeline are:

1. Using the correct core pattern (`core_jobsearch_{country}`)
2. Using both `q` and `fq` parameters for optimal performance
3. Selecting only the necessary fields with the `fl` parameter
4. Using batch processing with appropriate `rows` and `start` parameters
5. Using atomic updates to modify only the translated fields
6. Implementing batch commits for better performance

With these principles in mind, you can effectively work with Solr in the job offer translation pipeline.
